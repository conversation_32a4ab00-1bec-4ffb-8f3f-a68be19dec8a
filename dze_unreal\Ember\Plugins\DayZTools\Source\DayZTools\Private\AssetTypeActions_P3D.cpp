#include "AssetTypeActions_P3D.h"
#include "BlueprintEditorModule.h"
#include "Modules/ModuleManager.h"

void FAssetTypeActions_P3D::OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor)
{
    EToolkitMode::Type Mode = EditWithinLevelEditor.IsValid() ? EToolkitMode::WorldCentric : EToolkitMode::Standalone;

    for (UObject* Object : InObjects)
    {
        if (UP3DBlueprint* Blueprint = Cast<UP3DBlueprint>(Object))
        {
            FBlueprintEditorModule& BlueprintEditorModule = FModuleManager::LoadModuleChecked<FBlueprintEditorModule>("Kismet");
            BlueprintEditorModule.CreateBlueprintEditor(Mode, EditWithinLevelEditor, Blueprint);
        }
    }
}
