#include "Assets/TerrainLayers/TerrainLayerCustomization.h"
#include "DetailWidgetRow.h"
#include "IDetailCustomization.h"
#include "IDetailChildrenBuilder.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Colors/SColorBlock.h"
#include "UObject/UnrealType.h"
#include "Widgets/SBoxPanel.h"
#include "PropertyHandle.h"
#include "Internationalization/Text.h"

TSharedRef<IPropertyTypeCustomization> FTerrainLayerCustomization::MakeInstance()
{
	return MakeShareable(new FTerrainLayerCustomization);
}

void FTerrainLayerCustomization::CustomizeHeader(TSharedRef<IPropertyHandle> PropertyHandle,
	FDetailWidgetRow& HeaderRow,
	IPropertyTypeCustomizationUtils& CustomizationUtils)
{
	// Create a horizontal box with a color swatch and a text block.
	HeaderRow.NameContent()
		[
			SNew(SHorizontalBox)
				+ SHorizontalBox::Slot()
				.AutoWidth()
				.Padding(0, 0, 4, 0)
				[
					// Color swatch that updates live
					SNew(SColorBlock)
						.ToolTipText(FText::FromString("Layer Color"))
						.Size(FVector2D(16, 16))
						.Color_Lambda([PropertyHandle]() -> FLinearColor
							{
								TSharedPtr<IPropertyHandle> ColorHandle = PropertyHandle->GetChildHandle("LayerColor");
								if (ColorHandle.IsValid())
								{
									TArray<void*> RawData;
									ColorHandle->AccessRawData(RawData);
									if (RawData.Num() > 0)
									{
										FColor* ColorPtr = reinterpret_cast<FColor*>(RawData[0]);
										if (ColorPtr)
										{
											return FLinearColor(*ColorPtr);
										}
									}
								}
								return FLinearColor::White;
							})

				]
				+ SHorizontalBox::Slot()
				.FillWidth(1.0f)
				.VAlign(VAlign_Center)
				[
					// A text block that displays the layer name and array index.
					SNew(STextBlock)
						.Font(FCoreStyle::GetDefaultFontStyle("Regular", 10))
						.Text_Lambda([PropertyHandle]() -> FText
							{
								// Get the "LayerName" child property.
								TSharedPtr<IPropertyHandle> LayerNameHandle = PropertyHandle->GetChildHandle("LayerName");
								FText LayerNameText = FText::FromString("Unknown Layer");
								if (LayerNameHandle.IsValid())
								{
									LayerNameHandle->GetValueAsDisplayText(LayerNameText);
								}

								// Get the index of this array element.
								TOptional<int32> ArrayIndex = PropertyHandle->GetIndexInArray();
								if (ArrayIndex.IsSet())
								{
									return FText::Format(FText::FromString("{0} [{1}]"), LayerNameText, FText::AsNumber(ArrayIndex.GetValue()));
								}
								return LayerNameText;
							})
				]
		];
}

void FTerrainLayerCustomization::CustomizeChildren(TSharedRef<IPropertyHandle> PropertyHandle,
	IDetailChildrenBuilder& ChildBuilder,
	IPropertyTypeCustomizationUtils& CustomizationUtils)
{
	// Retrieve the number of child properties.
	uint32 NumChildren = 0;
	PropertyHandle->GetNumChildren(NumChildren);

	// Add each child property so they appear in the expanded view.
	for (uint32 ChildIndex = 0; ChildIndex < NumChildren; ++ChildIndex)
	{
		TSharedRef<IPropertyHandle> ChildHandle = PropertyHandle->GetChildHandle(ChildIndex).ToSharedRef();
		
		IDetailPropertyRow& AtomicRow = ChildBuilder.AddProperty(ChildHandle);
	}
}