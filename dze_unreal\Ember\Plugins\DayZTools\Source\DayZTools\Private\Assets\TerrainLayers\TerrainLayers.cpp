// TerrainLayers.cpp
#include "Assets/TerrainLayers/TerrainLayers.h"
#include "Misc/FileHelper.h"
#include "Internationalization/Regex.h"
#include "Logging/LogMacros.h"
#include "Materials/MaterialInstanceConstant.h"

#if WITH_EDITOR
#include "Editor.h"
#include "EditorSupportDelegates.h"
#endif

// Cache of parameter existence to avoid repeated lookups
static TMap<FName, bool> ParameterExistenceCache;
static UMaterialInstanceConstant* LastMaterialInstance = nullptr;

void UTerrainLayers::PostLoad()
{
    Super::PostLoad();

    // Sync colors with material parameters when the asset is loaded
    SyncAllLayerColorsWithMaterial();
}

void UTerrainLayers::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

    if (PropertyChangedEvent.Property)
    {
        FName ChangedPropName = PropertyChangedEvent.Property->GetFName();
        UE_LOG(LogTemp, Warning, TEXT("Property changed: %s"), *ChangedPropName.ToString());

        // Instead of checking for LayersConfigFile, check for its member "FilePath"
        if (ChangedPropName == FName("FilePath"))
        {
            bHasValidConfigFile = !LayersConfigFile.FilePath.IsEmpty();
            UE_LOG(LogTemp, Warning, TEXT("bHasValidConfigFile is now %s"), bHasValidConfigFile ? TEXT("true") : TEXT("false"));
            if (bHasValidConfigFile)
            {
                ParseAndPopulateLayers();
            }
        }
        else if (ChangedPropName == FName("TerrainMaterial"))
        {
            UE_LOG(LogTemp, Warning, TEXT("Terrain Material changed to: %s"),
                TerrainMaterial ? *TerrainMaterial->GetName() : TEXT("None"));

            // Sync all layer colors with material parameters
            SyncAllLayerColorsWithMaterial();
        }
        else if (ChangedPropName == FName("LayerColor"))
        {
            // Find which specific layer was changed
            // In Unreal Engine, we can use the array index from the property change event
            int32 ModifiedLayerIndex = PropertyChangedEvent.GetArrayIndex("Layers");

            if (ModifiedLayerIndex != INDEX_NONE && Layers.IsValidIndex(ModifiedLayerIndex))
            {
                // Only update the specific layer that was changed
                const FTerrainLayer& Layer = Layers[ModifiedLayerIndex];
                UE_LOG(LogTemp, Warning, TEXT("Layer color changed for layer %d: %s to (%d,%d,%d,%d)"),
                    ModifiedLayerIndex,
                    *Layer.LayerName,
                    Layer.LayerColor.R,
                    Layer.LayerColor.G,
                    Layer.LayerColor.B,
                    Layer.LayerColor.A);

                // Update only this specific layer's parameter
                UpdateMaterialParameterForLayer(Layer.LayerName, Layer.LayerColor);
            }
            else
            {
                // Fallback: If we couldn't determine the specific layer, update all layers
                // This should rarely happen with the optimized code
                UE_LOG(LogTemp, Warning, TEXT("Could not determine specific layer, updating all material parameters"));
                SyncAllLayerColorsWithMaterial();
            }
        }
    }
}

bool UTerrainLayers::ReloadFromDisk()
{
    return ParseAndPopulateLayers();
}

void UTerrainLayers::SyncAllLayerColorsWithMaterial()
{
    // Check if we have a valid material and layers
    if (!TerrainMaterial || Layers.Num() == 0)
    {
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("Syncing all layer colors with material parameters"));

    // Try to cast to UMaterialInstanceConstant
    UMaterialInstanceConstant* MaterialInstance = Cast<UMaterialInstanceConstant>(TerrainMaterial);
    if (!MaterialInstance)
    {
        UE_LOG(LogTemp, Warning, TEXT("TerrainMaterial is not a UMaterialInstanceConstant"));
        return;
    }

    // Reset the parameter cache for the new material instance
    ParameterExistenceCache.Empty();
    LastMaterialInstance = MaterialInstance;

    // Cache all vector parameter names for this material instance
    TArray<FMaterialParameterInfo> VectorParameterInfos;
    TArray<FGuid> VectorParameterIds;
    MaterialInstance->GetAllVectorParameterInfo(VectorParameterInfos, VectorParameterIds);

    for (const FMaterialParameterInfo& Info : VectorParameterInfos)
    {
        ParameterExistenceCache.Add(Info.Name, true);
    }

    // Batch all material updates together for better performance
    bool bMaterialModified = false;

    // Update all layers
    for (int32 i = 0; i < Layers.Num(); ++i)
    {
        const FTerrainLayer& Layer = Layers[i];

        // Only log at verbose level for better performance
        UE_LOG(LogTemp, Verbose, TEXT("Syncing layer %d: %s with color (%d,%d,%d,%d)"),
            i,
            *Layer.LayerName,
            Layer.LayerColor.R,
            Layer.LayerColor.G,
            Layer.LayerColor.B,
            Layer.LayerColor.A);

        // Create parameter name
        FName ParameterName(*Layer.LayerName);

        // Check if the parameter exists using our cache
        if (ParameterExistenceCache.Contains(ParameterName))
        {
            // Convert FColor (sRGB) to FLinearColor (linear space)
            // This properly converts from sRGB to linear color space
            FLinearColor LinearColor = FLinearColor::FromSRGBColor(Layer.LayerColor);

            // Log the conversion for debugging
            UE_LOG(LogTemp, Verbose, TEXT("Color conversion for %s: sRGB(%d,%d,%d,%d) -> Linear(%f,%f,%f,%f)"),
                *Layer.LayerName,
                Layer.LayerColor.R, Layer.LayerColor.G, Layer.LayerColor.B, Layer.LayerColor.A,
                LinearColor.R, LinearColor.G, LinearColor.B, LinearColor.A);

            // Get the current parameter value to see if it actually changed
            FLinearColor CurrentValue;
            MaterialInstance->GetVectorParameterValue(FMaterialParameterInfo(ParameterName), CurrentValue);

            // Only update if the color actually changed (avoid unnecessary updates)
            if (!CurrentValue.Equals(LinearColor, 0.001f))
            {
                // Update the parameter value
                MaterialInstance->SetVectorParameterValueEditorOnly(ParameterName, LinearColor);
                bMaterialModified = true;
            }
        }
    }

    // Only update the material if at least one parameter was modified
    if (bMaterialModified)
    {
#if WITH_EDITOR
        // Mark the material instance as modified
        MaterialInstance->PreEditChange(nullptr);

        // Update material resources
        MaterialInstance->UpdateStaticPermutation();

        // Force material to update in the editor
        MaterialInstance->PostEditChange();

        // Make sure the material instance is marked as needing to be saved
        MaterialInstance->MarkPackageDirty();

        // Force a refresh of the editor viewports
        if (GEditor)
        {
            // Refresh all viewports
            FEditorSupportDelegates::RedrawAllViewports.Broadcast();
        }
#endif
    }
}



void UTerrainLayers::UpdateMaterialParameterForLayer(const FString& LayerName, const FColor& LayerColor)
{
    // Check if we have a valid material
    if (!TerrainMaterial)
    {
        return;
    }

    // Try to cast to UMaterialInstanceConstant
    UMaterialInstanceConstant* MaterialInstance = Cast<UMaterialInstanceConstant>(TerrainMaterial);
    if (!MaterialInstance)
    {
        UE_LOG(LogTemp, Warning, TEXT("TerrainMaterial is not a UMaterialInstanceConstant"));
        return;
    }

    // Convert FColor (sRGB) to FLinearColor (linear space)
    // This properly converts from sRGB to linear color space
    FLinearColor LinearColor = FLinearColor::FromSRGBColor(LayerColor);

    // Log the conversion for debugging
    UE_LOG(LogTemp, Verbose, TEXT("Color conversion for %s: sRGB(%d,%d,%d,%d) -> Linear(%f,%f,%f,%f)"),
        *LayerName,
        LayerColor.R, LayerColor.G, LayerColor.B, LayerColor.A,
        LinearColor.R, LinearColor.G, LinearColor.B, LinearColor.A);

    // Create parameter name
    FName ParameterName(*LayerName);

    // Check if we need to rebuild the parameter cache
    if (LastMaterialInstance != MaterialInstance)
    {
        // Clear the cache and rebuild it for the new material instance
        ParameterExistenceCache.Empty();
        LastMaterialInstance = MaterialInstance;

        // Cache all vector parameter names for this material instance
        TArray<FMaterialParameterInfo> VectorParameterInfos;
        TArray<FGuid> VectorParameterIds;
        MaterialInstance->GetAllVectorParameterInfo(VectorParameterInfos, VectorParameterIds);

        for (const FMaterialParameterInfo& Info : VectorParameterInfos)
        {
            ParameterExistenceCache.Add(Info.Name, true);
        }
    }

    // Check if the parameter exists using our cache
    bool bParameterExists = ParameterExistenceCache.Contains(ParameterName);

    // If the parameter exists, update it
    if (bParameterExists)
    {
        // Get the current parameter value to see if it actually changed
        FLinearColor CurrentValue;
        MaterialInstance->GetVectorParameterValue(FMaterialParameterInfo(ParameterName), CurrentValue);

        // Only update if the color actually changed (avoid unnecessary updates)
        if (!CurrentValue.Equals(LinearColor, 0.001f))
        {
            // Update the parameter value
            MaterialInstance->SetVectorParameterValueEditorOnly(ParameterName, LinearColor);
            UE_LOG(LogTemp, Verbose, TEXT("Updated material parameter %s with color (%f, %f, %f, %f)"),
                *LayerName, LinearColor.R, LinearColor.G, LinearColor.B, LinearColor.A);

#if WITH_EDITOR
            // Mark the material instance as modified
            MaterialInstance->PreEditChange(nullptr);

            // Update material resources
            MaterialInstance->UpdateStaticPermutation();

            // Force material to update in the editor
            MaterialInstance->PostEditChange();

            // Make sure the material instance is marked as needing to be saved
            MaterialInstance->MarkPackageDirty();

            // We don't need to refresh all viewports for every color change
            // This is a major performance improvement
            static float LastViewportRefreshTime = 0.0f;
            float CurrentTime = FPlatformTime::Seconds();

            // Only refresh viewports at most once every 0.5 seconds
            if (CurrentTime - LastViewportRefreshTime > 0.5f)
            {
                if (GEditor)
                {
                    // Refresh all viewports
                    FEditorSupportDelegates::RedrawAllViewports.Broadcast();
                    LastViewportRefreshTime = CurrentTime;
                }
            }
#endif
        }
    }
    else
    {
        UE_LOG(LogTemp, Verbose, TEXT("Material parameter %s does not exist in the material"), *LayerName);
    }
}

void UTerrainLayers::PreSave(const ITargetPlatform* TargetPlatform)
{
    Super::PreSave(TargetPlatform);

    // Log terrain material info
    UE_LOG(LogTemp, Log, TEXT("Saving TerrainLayers asset. Terrain Material: %s"),
        TerrainMaterial ? *TerrainMaterial->GetName() : TEXT("None"));

    // Log layer info for debugging.
    UE_LOG(LogTemp, Log, TEXT("Layer info:"));
    for (int32 i = 0; i < Layers.Num(); i++)
    {
        const FTerrainLayer& Layer = Layers[i];
        UE_LOG(LogTemp, Log, TEXT("Layer[%d]: Name=%s, Color=(%d,%d,%d,%d)"),
            i,
            *Layer.LayerName,
            Layer.LayerColor.R,
            Layer.LayerColor.G,
            Layer.LayerColor.B,
            Layer.LayerColor.A);
    }

    // Only write the file if a valid file path is provided.
    if (!LayersConfigFile.FilePath.IsEmpty())
    {
        FString OutText;

        // Build the "Layers" block.
        OutText += "class Layers\n{\n";
        for (const FTerrainLayer& Layer : Layers)
        {
            // Extract texture path if available
            FString TexturePath = "";
            if (Layer.LayerTexture)
            {
                FString AssetPath = Layer.LayerTexture->GetPathName();
                // Convert /Game/path/to/texture to path/to/texture
                if (AssetPath.StartsWith("/Game/"))
                {
                    AssetPath = AssetPath.RightChop(6); // Remove "/Game/"

                    // Remove Unreal's internal extension by splitting at the dot
                    FString PathWithoutExtension;
                    if (AssetPath.Split(TEXT("."), &PathWithoutExtension, nullptr))
                    {
                        AssetPath = PathWithoutExtension;
                    }

                    // Find the last slash to separate path from name
                    int32 LastSlashIndex;
                    if (AssetPath.FindLastChar('/', LastSlashIndex))
                    {
                        FString Path = AssetPath.Left(LastSlashIndex);
                        FString Name = AssetPath.RightChop(LastSlashIndex + 1);
                        // Replace forward slashes with backslashes
                        Path = Path.Replace(TEXT("/"), TEXT("\\"));

                        // Simply add our extension
                        TexturePath = Path + "\\" + Name + ".paa";
                    }
                    else
                    {
                        TexturePath = AssetPath + ".paa";
                    }
                }
                UE_LOG(LogTemp, Log, TEXT("Layer %s texture path: %s"), *Layer.LayerName, *TexturePath);
            }

            // Extract material path if available
            FString MaterialPath = "";
            if (Layer.LayerMaterial)
            {
                FString AssetPath = Layer.LayerMaterial->GetPathName();
                // Convert /Game/path/to/material to path/to/material
                if (AssetPath.StartsWith("/Game/"))
                {
                    AssetPath = AssetPath.RightChop(6); // Remove "/Game/"

                    // Remove Unreal's internal extension by splitting at the dot
                    FString PathWithoutExtension;
                    if (AssetPath.Split(TEXT("."), &PathWithoutExtension, nullptr))
                    {
                        AssetPath = PathWithoutExtension;
                    }

                    // Find the last slash to separate path from name
                    int32 LastSlashIndex;
                    if (AssetPath.FindLastChar('/', LastSlashIndex))
                    {
                        FString Path = AssetPath.Left(LastSlashIndex);
                        FString Name = AssetPath.RightChop(LastSlashIndex + 1);
                        // Replace forward slashes with backslashes
                        Path = Path.Replace(TEXT("/"), TEXT("\\"));

                        // Simply add our extension
                        MaterialPath = Path + "\\" + Name + ".rvmat";
                    }
                    else
                    {
                        MaterialPath = AssetPath + ".rvmat";
                    }
                }
                UE_LOG(LogTemp, Log, TEXT("Layer %s material path: %s"), *Layer.LayerName, *MaterialPath);
            }

            OutText += "\tclass " + Layer.LayerName + "\n\t{\n";
            OutText += "\t\ttexture = \"" + TexturePath + "\";\n";
            OutText += "\t\tmaterial = \"" + MaterialPath + "\";\n";
            OutText += "\t};\n\n";
        }
        OutText += "};\n\n";

        // Build the "Legend" block.
        OutText += "class Legend\n{\n";
        OutText += "\tpicture=\"D:\\Projects\\DayZ\\Ember\\Ember_Raw\\EZ\\worlds\\asset_zoo\\MapLegend.png\";\n";
        OutText += "\tclass Colors\n\t{\n";
        for (const FTerrainLayer& Layer : Layers)
        {
            // Convert sRGB color to linear color space
            FLinearColor LinearColor = FLinearColor::FromSRGBColor(Layer.LayerColor);

            // Convert to 0-255 range for the config file
            int32 R = FMath::RoundToInt(LinearColor.R * 255.0f);
            int32 G = FMath::RoundToInt(LinearColor.G * 255.0f);
            int32 B = FMath::RoundToInt(LinearColor.B * 255.0f);

            // Clamp values to valid range
            R = FMath::Clamp(R, 0, 255);
            G = FMath::Clamp(G, 0, 255);
            B = FMath::Clamp(B, 0, 255);

            // Log the conversion for debugging
            UE_LOG(LogTemp, Log, TEXT("Writing color for layer %s: sRGB(%d,%d,%d) -> Linear(%d,%d,%d)"),
                *Layer.LayerName,
                Layer.LayerColor.R, Layer.LayerColor.G, Layer.LayerColor.B,
                R, G, B);

            // Format the color as: LayerName[] = {{R,G,B}}; (using linear color values)
            FString ColorLine = FString::Printf(TEXT("\t\t%s[] = {{%d,%d,%d}};\n"),
                *Layer.LayerName, R, G, B);
            OutText += ColorLine;
        }
        OutText += "\t};\n";
        OutText += "};\n";

        // Write the file.
        if (FFileHelper::SaveStringToFile(OutText, *LayersConfigFile.FilePath))
        {
            UE_LOG(LogTemp, Log, TEXT("Successfully saved layers config to disk."));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to write layers config file: %s"), *LayersConfigFile.FilePath);
        }
    }
}

bool UTerrainLayers::ParseAndPopulateLayers()
{
    UE_LOG(LogTemp, Warning, TEXT("Parsing layers config."));
    if (LayersConfigFile.FilePath.IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("No LayersConfig file specified."));
        return false;
    }

    FString ConfigText;
    if (!FFileHelper::LoadFileToString(ConfigText, *LayersConfigFile.FilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load layers config file: %s"), *LayersConfigFile.FilePath);
        return false;
    }

    // Local containers to hold parsed data.
    TMap<FString, FTerrainLayer> LayersInfo;
    TArray<FString> GlobalLayerNames;
    TMap<FColor, int32> ColorToLayer;

    Layers.Empty();

    // --- Extract and parse the Layers block ---
    FString LayersBlock;
    if (!ExtractLayersBlock(ConfigText, LayersBlock))
    {
        UE_LOG(LogTemp, Warning, TEXT("No 'Layers' block found in config."));
    }
    else
    {
        // Use DOTALL mode to match newlines.
        FRegexPattern Pattern(TEXT("(?s)class\\s+(\\w+)\\s*\\{(.*?)\\}\\s*;"));
        FRegexMatcher Matcher(Pattern, LayersBlock);
        while (Matcher.FindNext())
        {
            FString LayerName = Matcher.GetCaptureGroup(1);
            FString Content = Matcher.GetCaptureGroup(2);
            FTerrainLayer TempLayer;
            TempLayer.LayerName = LayerName;
            TempLayer.LayerMaterial = nullptr;
            TempLayer.LayerTexture = nullptr;

            // Extract texture path
            FRegexPattern TexturePattern(TEXT("texture\\s*=\\s*\"([^\"]*)\";"));
            FRegexMatcher TextureMatcher(TexturePattern, Content);
            FString TexturePath;
            if (TextureMatcher.FindNext())
            {
                TexturePath = TextureMatcher.GetCaptureGroup(1);
                UE_LOG(LogTemp, Log, TEXT("Found texture path for layer %s: %s"), *LayerName, *TexturePath);

                // Try to find the texture asset if path is not empty
                if (!TexturePath.IsEmpty())
                {
                    // Convert DayZ path format to Unreal path format
                    // Example: "ez/art/test_texture.paa" -> "/Game/ez/art/test_texture"
                    FString UnrealTexturePath;
                    if (ConvertDayZPathToUnrealPath(TexturePath, UnrealTexturePath, TEXT(".paa")))
                    {
                        // Try to load the texture
                        UTexture* TextureAsset = LoadObject<UTexture>(nullptr, *UnrealTexturePath);
                        if (TextureAsset)
                        {
                            UE_LOG(LogTemp, Log, TEXT("Found texture asset for layer %s: %s"), *LayerName, *UnrealTexturePath);
                            TempLayer.LayerTexture = TextureAsset;
                        }
                        else
                        {
                            UE_LOG(LogTemp, Warning, TEXT("Could not find texture asset for path: %s"), *UnrealTexturePath);
                        }
                    }
                }
            }

            // Extract material path
            FRegexPattern MaterialPattern(TEXT("material\\s*=\\s*\"([^\"]*)\";"));
            FRegexMatcher MaterialMatcher(MaterialPattern, Content);
            FString MaterialPath;
            if (MaterialMatcher.FindNext())
            {
                MaterialPath = MaterialMatcher.GetCaptureGroup(1);
                UE_LOG(LogTemp, Log, TEXT("Found material path for layer %s: %s"), *LayerName, *MaterialPath);

                // Try to find the material asset if path is not empty
                if (!MaterialPath.IsEmpty())
                {
                    // Convert DayZ path format to Unreal path format
                    // Example: "ez/art/test_mtr.rvmat" -> "/Game/ez/art/test_mtr"
                    FString UnrealMaterialPath;
                    if (ConvertDayZPathToUnrealPath(MaterialPath, UnrealMaterialPath, TEXT(".rvmat")))
                    {
                        // Try to load the material
                        UMaterialInterface* MaterialAsset = LoadObject<UMaterialInterface>(nullptr, *UnrealMaterialPath);
                        if (MaterialAsset)
                        {
                            UE_LOG(LogTemp, Log, TEXT("Found material asset for layer %s: %s"), *LayerName, *UnrealMaterialPath);
                            TempLayer.LayerMaterial = MaterialAsset;
                        }
                        else
                        {
                            UE_LOG(LogTemp, Warning, TEXT("Could not find material asset for path: %s"), *UnrealMaterialPath);
                        }
                    }
                }
            }

            // Only add the layer if it hasn't been seen yet (to preserve insertion order)
            if (!LayersInfo.Contains(LayerName))
            {
                LayersInfo.Add(LayerName, TempLayer);
                GlobalLayerNames.Add(LayerName);
            }
        }
    }

    // --- Parse color definitions ---
    FRegexPattern ColorPattern(TEXT("(\\w+)\\[\\]\\s*=\\s*\\{\\{\\s*([0-9,\\s]+)\\s*\\}\\};"));
    FRegexMatcher ColorMatcher(ColorPattern, ConfigText);

    UE_LOG(LogTemp, Log, TEXT("=== Starting Color Parsing ==="));
    while (ColorMatcher.FindNext())
    {
        FString LayerName = ColorMatcher.GetCaptureGroup(1);
        FString ColorStr = ColorMatcher.GetCaptureGroup(2);

        UE_LOG(LogTemp, Log, TEXT("Found color definition:"));
        UE_LOG(LogTemp, Log, TEXT("  Layer: %s"), *LayerName);
        UE_LOG(LogTemp, Log, TEXT("  Raw color string: %s"), *ColorStr);

        TArray<FString> ColorComponents;
        ColorStr.ParseIntoArray(ColorComponents, TEXT(","), true);

        UE_LOG(LogTemp, Log, TEXT("  Parsed components (%d):"), ColorComponents.Num());
        for (const FString& Comp : ColorComponents)
        {
            UE_LOG(LogTemp, Log, TEXT("    Component: '%s'"), *Comp.TrimStartAndEnd());
        }

        int32 R = 0, G = 0, B = 0;
        if (ColorComponents.Num() >= 3)
        {
            R = FCString::Atoi(*ColorComponents[0].TrimStartAndEnd());
            G = FCString::Atoi(*ColorComponents[1].TrimStartAndEnd());
            B = FCString::Atoi(*ColorComponents[2].TrimStartAndEnd());
        }

        FColor ParsedColor(R, G, B, 255);
        UE_LOG(LogTemp, Log, TEXT("  Final color: R=%d, G=%d, B=%d, A=255"), R, G, B);

        if (LayersInfo.Contains(LayerName))
        {
            FTerrainLayer Temp = LayersInfo[LayerName];
            Temp.LayerColor = ParsedColor;
            LayersInfo[LayerName] = Temp;
            UE_LOG(LogTemp, Log, TEXT("  Updated existing layer"));
        }
        else
        {
            FTerrainLayer Temp;
            Temp.LayerColor = ParsedColor;
            LayersInfo.Add(LayerName, Temp);
            GlobalLayerNames.Add(LayerName);
            UE_LOG(LogTemp, Log, TEXT("  Created new layer"));
        }
    }

    // --- Build ColorToLayer mapping using the EXACT same order as Python ---
    ColorToLayer.Empty();
    for (int32 i = 0; i < GlobalLayerNames.Num(); i++)
    {
        const FString& LayerName = GlobalLayerNames[i];
        if (LayersInfo.Contains(LayerName))
        {
            FTerrainLayer Temp = LayersInfo[LayerName];
            FColor KeyColor = Temp.LayerColor;
            KeyColor.A = 255;  // Ensure alpha is 255 for comparison
            ColorToLayer.Add(KeyColor, i);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Raw config text (first 500 chars): %s"), *ConfigText.Left(500));
    UE_LOG(LogTemp, Log, TEXT("=== Global Layer Names (in order) ==="));
    for (int32 i = 0; i < GlobalLayerNames.Num(); i++)
    {
        UE_LOG(LogTemp, Log, TEXT("[%d]: %s"), i, *GlobalLayerNames[i]);
    }
    UE_LOG(LogTemp, Log, TEXT("=== Color To Layer Mapping ==="));
    for (const auto& Pair : ColorToLayer)
    {
        FColor Col = Pair.Key;
        int32 LayerIndex = Pair.Value;
        UE_LOG(LogTemp, Log, TEXT("Color(%d,%d,%d,%d) -> Layer %d"), Col.R, Col.G, Col.B, Col.A, LayerIndex);
    }

    // --- Update the asset's Layers array in the order of GlobalLayerNames ---
    TArray<FTerrainLayer> NewLayers;
    for (const FString& LayerName : GlobalLayerNames)
    {
        if (LayersInfo.Contains(LayerName))
        {
            NewLayers.Add(LayersInfo[LayerName]);
        }
    }
    Layers = NewLayers;

    // Sync all layer colors with material parameters
    UE_LOG(LogTemp, Warning, TEXT("Updating material parameters for all layers"));
    SyncAllLayerColorsWithMaterial();

    return true;
}