#include "Assets/TerrainLayers/TerrainLayersAssetTypeActions.h"
#include "Assets/TerrainLayers/TerrainLayers.h"

UClass* FTerrainLayersAssetTypeActions::GetSupportedClass() const
{
    return UTerrainLayers::StaticClass();
}

FText FTerrainLayersAssetTypeActions::GetName() const
{
    return INVTEXT("Terrain Layers");
}

FColor FTerrainLayersAssetTypeActions::GetTypeColor() const
{
    return FColor::Orange;
}

uint32 FTerrainLayersAssetTypeActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}