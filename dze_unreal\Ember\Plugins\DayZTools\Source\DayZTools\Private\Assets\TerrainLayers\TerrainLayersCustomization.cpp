#include "Assets/TerrainLayers/TerrainLayersCustomization.h"
#include "Assets/TerrainLayers/TerrainLayers.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Text/STextBlock.h"
#include "DetailWidgetRow.h"

TSharedRef<IDetailCustomization> FTerrainLayersCustomization::MakeInstance()
{
    return MakeShareable(new FTerrainLayersCustomization);
}

void FTerrainLayersCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // Get the currently selected objects (should be our UTerrainLayers asset).
    TArray<TWeakObjectPtr<UObject>> SelectedObjects;
    DetailBuilder.GetObjectsBeingCustomized(SelectedObjects);

    UTerrainLayers* TerrainAsset = nullptr;
    if (SelectedObjects.Num() == 1)
    {
        TerrainAsset = Cast<UTerrainLayers>(SelectedObjects[0].Get());
    }
    if (!TerrainAsset)
    {
        return;
    }

    // Edit the "Terrain Layers" category.
    IDetailCategoryBuilder& Category = DetailBuilder.EditCategory(FName("Terrain Layers"));

    // Add a custom button row.
    Category.AddCustomRow(FText::FromString("Reload From File"))
        .ValueContent()
        [
            SNew(SButton)
                .Text(FText::FromString("Reload From File"))
                .OnClicked_Lambda([TerrainAsset, &DetailBuilder]()
                    {
                        if (TerrainAsset->ParseAndPopulateLayers())
                        {
                            DetailBuilder.ForceRefreshDetails();
                        }
                        return FReply::Handled();
                    })
        ];
}
