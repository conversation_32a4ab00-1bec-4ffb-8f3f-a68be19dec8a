#include "Assets/TerrainLayers/TerrainLayersFactory.h"
#include "Assets/TerrainLayers/TerrainLayers.h"

UTerrainLayersFactory::UTerrainLayersFactory()
{
    SupportedClass = UTerrainLayers::StaticClass();
    bCreateNew = true;
}

UObject* UTerrainLayersFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    return NewObject<UTerrainLayers>(InParent, Class, Name, Flags, Context);
}