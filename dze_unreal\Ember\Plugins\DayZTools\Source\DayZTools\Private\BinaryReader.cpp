#include "BinaryReader.h"
#include "HAL/UnrealMemory.h"
#include "Misc/StringBuilder.h"
#include "Serialization/MemoryReader.h"
#include <vector>
#include <stdexcept>
#include "LZO.h"
#include "LZSS.h"

//FBinaryReader::FBinaryReader(const std::vector<uint8_t>& data) : Data(data) {}

int64 FBinaryReader::GetPosition() const { return Position; }
void FBinaryReader::SetPosition(int64 NewPosition) { Position = NewPosition; }

bool FBinaryReader::HasReachedEnd() const { return Position == Data.size(); }

uint32 FBinaryReader::ReadUInt24()
{
    return ReadByte() + (ReadByte() << 8) + (ReadByte() << 16);
}

std::string FBinaryReader::ReadAscii(int32 Count)
{
    std::string result;
    result.reserve(Count);
    for (int32 i = 0; i < Count; ++i)
    {
        result.push_back(static_cast<char>(ReadByte()));
    }
    return result;
}

std::string FBinaryReader::ReadAscii()
{
    return ReadAscii(ReadUInt16());
}

std::string FBinaryReader::ReadAscii32()
{
    return ReadAscii(ReadUInt32());
}

std::vector<uint8_t> FBinaryReader::ReadBytes(int32_t count)
{
    if (count < 0)
    {
        throw std::invalid_argument("Count cannot be negative");
    }

    if (static_cast<uint64_t>(Position) + static_cast<uint64_t>(count) > Data.size())
    {
        throw std::out_of_range("Not enough data to read");
    }

    std::vector<uint8_t> result(count);
    std::copy(Data.begin() + Position, Data.begin() + Position + count, result.begin());
    Position += count;

    return result;
}

std::string FBinaryReader::ReadAsciiz()
{
    std::string result;
    char Ch;
    while ((Ch = static_cast<char>(ReadByte())) != 0)
    {
        result.push_back(Ch);
    }
    return result;
}

template<typename T>
std::vector<T> FBinaryReader::ReadArrayBase(std::function<T(FBinaryReader&)> ReadElement, int32 Size)
{
    std::vector<T> Array(Size);
    for (int32 i = 0; i < Size; i++)
    {
        Array[i] = ReadElement(*this);
    }
    return Array;
}

template<typename T>
std::vector<T> FBinaryReader::ReadArray(std::function<T(FBinaryReader&)> ReadElement)
{
    return ReadArrayBase(ReadElement, ReadInt32());
}

std::vector<float> FBinaryReader::ReadFloatArray()
{
    return ReadArray<float>([](FBinaryReader& Reader) { return Reader.ReadFloat(); });
}

std::vector<int32> FBinaryReader::ReadIntArray()
{
    return ReadArray<int32>([](FBinaryReader& Reader) { return Reader.ReadInt32(); });
}

std::vector<std::string> FBinaryReader::ReadStringArray()
{
    return ReadArray<std::string>([](FBinaryReader& Reader) { return Reader.ReadAsciiz(); });
}

template<typename T>
std::vector<T> FBinaryReader::ReadCompressedArray(std::function<T(FBinaryReader&)> ReadElement, int32 ElemSize)
{
    int32 NElements = ReadInt32();
    return ReadCompressed(ReadElement, NElements, ElemSize);
}

std::vector<int16> FBinaryReader::ReadCompressedShortArray()
{
    return ReadCompressedArray<int16>([](FBinaryReader& Reader) { return Reader.ReadInt16(); }, 2);
}

std::vector<int32> FBinaryReader::ReadCompressedIntArray()
{
    return ReadCompressedArray<int32>([](FBinaryReader& Reader) { return Reader.ReadInt32(); }, 4);
}

std::vector<float> FBinaryReader::ReadCompressedFloatArray()
{
    return ReadCompressedArray<float>([](FBinaryReader& Reader) { return Reader.ReadFloat(); }, 4);
}

std::vector<uint8> FBinaryReader::ReadCompressedByteArray()
{
    return ReadCompressedArray<uint8>([](FBinaryReader& Reader) { return Reader.ReadByte(); }, 1);
}

template<typename T>
std::vector<T> FBinaryReader::ReadCondensedArray(std::function<T(FBinaryReader&)> ReadElement, int32 SizeOfT)
{
    int32 Size = ReadInt32();
    std::vector<T> Result(Size);
    bool DefaultFill = ReadBoolean();
    if (DefaultFill)
    {
        T DefaultValue = ReadElement(*this);
        std::fill(Result.begin(), Result.end(), DefaultValue);
        return Result;
    }

    uint32 ExpectedDataSize = Size * SizeOfT;
    std::vector<uint8> CompressedData = ReadCompressed(ExpectedDataSize);
    FBinaryReader TempReader(CompressedData);
    return TempReader.ReadArrayBase(ReadElement, Size);
}

std::vector<int32> FBinaryReader::ReadCondensedIntArray()
{
    return ReadCondensedArray<int32>([](FBinaryReader& Reader) { return Reader.ReadInt32(); }, 4);
}

int32 FBinaryReader::ReadCompactInteger()
{
    int32 Result = 0;
    int32 i = 0;
    bool End;
    do
    {
        int32 b = ReadByte();
        Result |= (b & 0x7f) << (i * 7);
        End = b < 0x80;
        i++;
    } while (!End);
    return Result;
}

std::vector<uint8> FBinaryReader::ReadCompressed(uint32 ExpectedSize, bool ForceCompressed)
{
    if (ExpectedSize == 0)
    {
        return std::vector<uint8>();
    }

    if (UseLZOCompression) return ReadLZO(ExpectedSize, ForceCompressed);

    return ReadLZSS(ExpectedSize);
}

std::vector<uint8> FBinaryReader::ReadLZO(uint32 ExpectedSize, bool ForceCompressed)
{
    bool IsCompressed = (ExpectedSize >= 1024) || ForceCompressed;
    if (UseCompressionFlag)
    {
        IsCompressed = ReadBoolean();
    }

    if (!IsCompressed)
    {
        std::vector<uint8> Result(ExpectedSize);
        std::memcpy(Result.data(), &Data[Position], ExpectedSize);
        Position += ExpectedSize;
        return Result;
    }

    return FLZO::ReadLZO(std::vector<uint8>(Data.begin() + Position, Data.end()), ExpectedSize);
}

std::vector<uint8_t> FBinaryReader::ReadLZSS(uint32_t ExpectedSize, bool InPAA)
{
    if (ExpectedSize < 1024 && !InPAA)
    {
        std::vector<uint8_t> Result(ExpectedSize);
        std::memcpy(Result.data(), &Data[Position], ExpectedSize);
        Position += ExpectedSize;
        return Result;
    }
    else
    {
        std::vector<uint8_t> Buffer;
        uint32_t BytesRead = FLZSS::ReadLZSS(std::vector<uint8_t>(Data.begin() + Position, Data.end()), Buffer, ExpectedSize, InPAA);
        Position += BytesRead;
        return Buffer;
    }
}

std::vector<uint8> FBinaryReader::ReadCompressedIndices(int32 BytesToRead, uint32 ExpectedSize)
{
    std::vector<uint8> Result(ExpectedSize);
    int32 OutputI = 0;
    for (int32 i = 0; i < BytesToRead; i++)
    {
        uint8 b = ReadByte();
        if ((b & 128) != 0)
        {
            uint8 n = static_cast<uint8>(b - 127);
            uint8 value = ReadByte();
            for (int32 j = 0; j < n; j++)
                Result[OutputI++] = value;
        }
        else
        {
            for (int32 j = 0; j < b + 1; j++)
                Result[OutputI++] = ReadByte();
        }
    }

    check(OutputI == ExpectedSize);

    return Result;
}

std::vector<float> FBinaryReader::ReadCompressedFloats(int32 NElements)
{
    return ReadCompressed<float>([](FBinaryReader& Reader) { return Reader.ReadFloat(); }, NElements, 4);
}

std::vector<float> FBinaryReader::ReadFloats(int32 NElements)
{
    return ReadArrayBase<float>([](FBinaryReader& Reader) { return Reader.ReadFloat(); }, NElements);
}

std::vector<uint16> FBinaryReader::ReadUshorts(int32 NElements)
{
    return ReadArrayBase<uint16>([](FBinaryReader& Reader) { return Reader.ReadUInt16(); }, NElements);
}

template<typename T>
std::vector<T> FBinaryReader::ReadCompressed(std::function<T(FBinaryReader&)> ReadElement, int32 NElements, int32 ElemSize)
{
    uint32 ExpectedDataSize = NElements * ElemSize;
    std::vector<uint8> CompressedData = ReadCompressed(ExpectedDataSize);
    FBinaryReader TempReader(CompressedData);
    return TempReader.ReadArrayBase(ReadElement, NElements);
}

uint8 FBinaryReader::ReadByte()
{
    if (static_cast<size_t>(Position) < Data.size())
    {
        return Data[Position++];
    }
    // Handle the case where we've reached the end of the data
    throw std::out_of_range("Attempted to read past the end of the data");
}

int16 FBinaryReader::ReadInt16()
{
    int16 Value;
    FMemory::Memcpy(&Value, &Data[Position], sizeof(int16));
    Position += sizeof(int16);
    return Value;
}

uint16 FBinaryReader::ReadUInt16()
{
    uint16 Value;
    FMemory::Memcpy(&Value, &Data[Position], sizeof(uint16));
    Position += sizeof(uint16);
    return Value;
}

int32 FBinaryReader::ReadInt32()
{
    int32 Value;
    FMemory::Memcpy(&Value, &Data[Position], sizeof(int32));
    Position += sizeof(int32);
    return Value;
}

uint32 FBinaryReader::ReadUInt32()
{
    uint32 Value;
    FMemory::Memcpy(&Value, &Data[Position], sizeof(uint32));
    Position += sizeof(uint32);
    return Value;
}

float FBinaryReader::ReadFloat()
{
    float Value;
    //UE_LOG(LogTemp, Log, TEXT("Raw bytes: %02x %02x %02x %02x"), Data[Position], Data[Position + 1], Data[Position + 2], Data[Position + 3]);

    FMemory::Memcpy(&Value, &Data[Position], sizeof(float));
    Position += sizeof(float);
    //UE_LOG(LogTemp, Log, TEXT("Read float: %f"), Value);
    return Value;
}

float FBinaryReader::ReadFloat2()
{
    float Value;
    UE_LOG(LogTemp, Log, TEXT("Raw bytes: %02x %02x %02x %02x"), Data[Position], Data[Position + 1], Data[Position + 2], Data[Position + 3]);

    FMemory::Memcpy(&Value, &Data[Position], sizeof(float));
    Position += sizeof(float);
    UE_LOG(LogTemp, Log, TEXT("Read float: %f"), Value);
    return Value;
}

bool FBinaryReader::ReadBoolean()
{
    return ReadByte() != 0;
}