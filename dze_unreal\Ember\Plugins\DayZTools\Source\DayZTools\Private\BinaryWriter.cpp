// FBinaryWriter.cpp

#include "BinaryWriter.h"
#include "Misc/FileHelper.h"

FBinaryWriter::FBinaryWriter()
    : Position(0)
{
}

int64 FBinaryWriter::GetPosition() const
{
    return Position;
}

void FBinaryWriter::SetPosition(int64 NewPosition)
{
    Position = NewPosition;
}

void FBinaryWriter::WriteByte(uint8 Value)
{
    if (Position >= Data.size())
    {
        Data.resize(Position + 1);
    }
    Data[Position++] = Value;
}

void FBinaryWriter::WriteInt16(int16 Value)
{
    uint8* Bytes = reinterpret_cast<uint8*>(&Value);
    if (Position + sizeof(int16) > Data.size())
    {
        Data.resize(Position + sizeof(int16));
    }
    memcpy(&Data[Position], Bytes, sizeof(int16));
    Position += sizeof(int16);
}

void FBinaryWriter::WriteUInt16(uint16 Value)
{
    uint8* Bytes = reinterpret_cast<uint8*>(&Value);
    if (Position + sizeof(uint16) > Data.size())
    {
        Data.resize(Position + sizeof(uint16));
    }
    memcpy(&Data[Position], Bytes, sizeof(uint16));
    Position += sizeof(uint16);
}

void FBinaryWriter::WriteInt32(int32 Value)
{
    uint8* Bytes = reinterpret_cast<uint8*>(&Value);
    if (Position + sizeof(int32) > Data.size())
    {
        Data.resize(Position + sizeof(int32));
    }
    memcpy(&Data[Position], Bytes, sizeof(int32));
    Position += sizeof(int32);
}

void FBinaryWriter::WriteUInt32(uint32 Value)
{
    uint8* Bytes = reinterpret_cast<uint8*>(&Value);
    if (Position + sizeof(uint32) > Data.size())
    {
        Data.resize(Position + sizeof(uint32));
    }
    memcpy(&Data[Position], Bytes, sizeof(uint32));
    Position += sizeof(uint32);
}

void FBinaryWriter::WriteUInt24(uint32 Value)
{
    // Write only the lower 3 bytes of the uint32
    if (Position + 3 > Data.size())
    {
        Data.resize(Position + 3);
    }

    // Extract the bytes (assuming little-endian)
    uint8 Bytes[3];
    Bytes[0] = (Value & 0xFF);
    Bytes[1] = ((Value >> 8) & 0xFF);
    Bytes[2] = ((Value >> 16) & 0xFF);

    // Copy the bytes to the output
    memcpy(&Data[Position], Bytes, 3);
    Position += 3;
}

void FBinaryWriter::WriteFloat(float Value)
{
    uint8* Bytes = reinterpret_cast<uint8*>(&Value);
    if (Position + sizeof(float) > Data.size())
    {
        Data.resize(Position + sizeof(float));
    }
    memcpy(&Data[Position], Bytes, sizeof(float));
    Position += sizeof(float);
}

void FBinaryWriter::WriteAscii(const std::string& Value)
{
    int32 Count = Value.length();
    if (Position + Count > Data.size())
    {
        Data.resize(Position + Count);
    }
    memcpy(&Data[Position], Value.c_str(), Count);
    Position += Count;
}

void FBinaryWriter::WriteAscii(int32 Count, const std::string& Value)
{
    if (Value.length() != static_cast<size_t>(Count))
    {
        // Handle error
        UE_LOG(LogTemp, Error, TEXT("Ascii string length does not match the specified count"));
    }
    WriteAscii(Value);
}

void FBinaryWriter::WriteAscii32(const std::string& Value)
{
    WriteInt32(Value.length());
    WriteAscii(Value);
}

void FBinaryWriter::WriteAsciiz(const std::string& Value)
{
    WriteAscii(Value);
    WriteByte(0); // Null terminator
}

void FBinaryWriter::WriteBytes(const TArray<uint8>& InData)
{
    // Ensure our Data vector has enough space
    if (Position + InData.Num() > Data.size())
    {
        Data.resize(Position + InData.Num());
    }

    // Copy the bytes
    FMemory::Memcpy(&Data[Position], InData.GetData(), InData.Num());
    Position += InData.Num();
}

void FBinaryWriter::WriteBytes(const std::vector<uint8_t>& InData)
{
    if (InData.empty())
    {
        return;
    }

    try
    {
        if (Position + InData.size() > Data.size())
        {
            Data.resize(Position + InData.size());
        }
        memcpy(&Data[Position], InData.data(), InData.size());
        Position += InData.size();
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("Exception in WriteBytes: %s"), UTF8_TO_TCHAR(e.what()));
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("Unknown exception in WriteBytes"));
    }
}

void FBinaryWriter::WriteBytes(const uint8_t* InData, int32 Length)
{
    if (InData == nullptr || Length <= 0)
    {
        return;
    }

    try
    {
        if (Position + Length > Data.size())
        {
            Data.resize(Position + Length);
        }
        memcpy(&Data[Position], InData, Length);
        Position += Length;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("Exception in WriteBytes(ptr): %s"), UTF8_TO_TCHAR(e.what()));
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("Unknown exception in WriteBytes(ptr)"));
    }
}

void FBinaryWriter::WriteFloatArray(const std::vector<float>& Array)
{
    for (const float& Value : Array)
    {
        WriteFloat(Value);
    }
}

void FBinaryWriter::WriteFloatArray(const TArray<float>& Array)
{
    for (const float& Value : Array)
    {
        WriteFloat(Value);
    }
}

void FBinaryWriter::WriteUInt16Array(const std::vector<uint16>& Array)
{
    for (const uint16& Value : Array)
    {
        WriteUInt16(Value);
    }
}

void FBinaryWriter::WriteUInt16Array(const TArray<uint16>& Array)
{
    for (const uint16& Value : Array)
    {
        WriteUInt16(Value);
    }
}

bool FBinaryWriter::SaveToFile(const FString& FilePath) const
{
    TArray<uint8> UEData;
    UEData.Append(Data.data(), Data.size());
    return FFileHelper::SaveArrayToFile(UEData, *FilePath);
}

