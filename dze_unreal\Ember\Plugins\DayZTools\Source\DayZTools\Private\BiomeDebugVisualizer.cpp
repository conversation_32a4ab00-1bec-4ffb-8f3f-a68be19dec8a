#include "BiomeDebugVisualizer.h"
#include "PCGComponent.h"
#include "PCGSubsystem.h"
#include "PCGData.h"
#include "PCGPoint.h"
#include "Data/PCGPointData.h"
#include "Metadata/PCGMetadata.h"
#include "Metadata/PCGMetadataAccessor.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "UObject/ConstructorHelpers.h"

ABiomeDebugVisualizer::ABiomeDebugVisualizer()
{
    PrimaryActorTick.bCanEverTick = true;

    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("Root"));

    PCGComponent = CreateDefaultSubobject<UPCGComponent>(TEXT("PCGComponent"));
    //PCGComponent->SetupAttachment(RootComponent);
}

void ABiomeDebugVisualizer::BeginPlay()
{
    Super::BeginPlay();
}

void ABiomeDebugVisualizer::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
}

#if WITH_EDITOR
void ABiomeDebugVisualizer::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

    if (PropertyChangedEvent.Property)
    {
        const FName PropertyName = PropertyChangedEvent.Property->GetFName();
        if (PropertyName == GET_MEMBER_NAME_CHECKED(ABiomeDebugVisualizer, DebugMode) ||
            PropertyName == GET_MEMBER_NAME_CHECKED(ABiomeDebugVisualizer, CustomAttributeName))
        {
            UpdateVisualization();
        }
    }
}
#endif

void ABiomeDebugVisualizer::UpdateVisualization()
{
    ClearVisualization();

    if (DebugMode == EBiomeDebugMode::None)
    {
        return;
    }

    FString AttributeName;
    switch (DebugMode)
    {
    case EBiomeDebugMode::Slope: AttributeName = "Slope"; break;
    case EBiomeDebugMode::Aspect: AttributeName = "Aspect"; break;
    case EBiomeDebugMode::Occlusion: AttributeName = "Occlusion"; break;
    case EBiomeDebugMode::FlowAccumulation: AttributeName = "FlowAccumulation"; break;
    case EBiomeDebugMode::Curvature: AttributeName = "Curvature"; break;
    case EBiomeDebugMode::WindExposure: AttributeName = "WindExposure"; break;
    case EBiomeDebugMode::Altitude: AttributeName = "Altitude"; break;
    case EBiomeDebugMode::Custom: AttributeName = CustomAttributeName; break;
    default: return;
    }

    VisualizeAttribute(AttributeName);
}

void ABiomeDebugVisualizer::ClearVisualization()
{
    for (UInstancedStaticMeshComponent* Component : DebugMeshComponents)
    {
        if (Component)
        {
            Component->ClearInstances();
            Component->DestroyComponent();
        }
    }
    DebugMeshComponents.Empty();
}

void ABiomeDebugVisualizer::VisualizeAttribute(const FString& AttributeName)
{
    if (!PCGComponent)
    {
        return;
    }

    // Get the generated data from the PCG component
    const FPCGDataCollection& DataCollection = PCGComponent->GetGeneratedGraphOutput();
    if (DataCollection.TaggedData.Num() == 0)
    {
        return;
    }

    for (const FPCGTaggedData& TaggedData : DataCollection.TaggedData)
    {
        const UPCGPointData* PointData = Cast<UPCGPointData>(TaggedData.Data);
        if (!PointData)
        {
            continue;
        }

        // Create instanced mesh component
        UInstancedStaticMeshComponent* MeshComponent = NewObject<UInstancedStaticMeshComponent>(this, UInstancedStaticMeshComponent::StaticClass());
        MeshComponent->SetupAttachment(RootComponent);
        MeshComponent->RegisterComponent();

        // Set mesh
        UStaticMesh* DebugMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/BasicShapes/Sphere.Sphere"));
        if (DebugMesh)
        {
            MeshComponent->SetStaticMesh(DebugMesh);
        }

        // Create material instance
        UMaterial* BaseMaterial = LoadObject<UMaterial>(nullptr, TEXT("/Engine/BasicShapes/BasicShapeMaterial.BasicShapeMaterial"));
        if (BaseMaterial)
        {
            UMaterialInstanceDynamic* DynMaterial = UMaterialInstanceDynamic::Create(BaseMaterial, this);
            MeshComponent->SetMaterial(0, DynMaterial);
        }

        // Get metadata
        const UPCGMetadata* Metadata = PointData->ConstMetadata();
        if (!Metadata)
        {
            continue;
        }

        // Check if attribute exists
        const FPCGMetadataAttributeBase* AttributeBase = Metadata->GetConstAttribute(FName(*AttributeName));
        if (!AttributeBase)
        {
            UE_LOG(LogTemp, Warning, TEXT("Attribute %s not found in PCG data"), *AttributeName);
            continue;
        }

        // Get the attribute accessor for float values
        const FPCGMetadataAttribute<float>* FloatAttribute = static_cast<const FPCGMetadataAttribute<float>*>(AttributeBase);
        if (!FloatAttribute)
        {
            UE_LOG(LogTemp, Warning, TEXT("Attribute %s is not a float attribute"), *AttributeName);
            continue;
        }

        // Add instances for each point
        const TArray<FPCGPoint>& Points = PointData->GetPoints();
        for (const FPCGPoint& Point : Points)
        {
            float AttributeValue = 0.0f;
            AttributeValue = FloatAttribute->GetValueFromItemKey(Point.MetadataEntry);

            FTransform InstanceTransform = Point.Transform;
            InstanceTransform.SetScale3D(FVector(VisualizationScale / 100.0f));

            FLinearColor Color = GetColorForValue(AttributeValue, DebugMode);

            int32 InstanceIndex = MeshComponent->AddInstance(InstanceTransform);

            // Set color via per-instance custom data if supported
            if (MeshComponent->NumCustomDataFloats >= 4)
            {
                MeshComponent->SetCustomDataValue(InstanceIndex, 0, Color.R);
                MeshComponent->SetCustomDataValue(InstanceIndex, 1, Color.G);
                MeshComponent->SetCustomDataValue(InstanceIndex, 2, Color.B);
                MeshComponent->SetCustomDataValue(InstanceIndex, 3, Color.A);
            }
        }

        DebugMeshComponents.Add(MeshComponent);
    }
}

FLinearColor ABiomeDebugVisualizer::GetColorForValue(float Value, EBiomeDebugMode Mode)
{
    // Clamp value to 0-1 range
    Value = FMath::Clamp(Value, 0.0f, 1.0f);

    switch (Mode)
    {
    case EBiomeDebugMode::Slope:
        // Green (flat) to Red (steep)
        return FLinearColor::LerpUsingHSV(FLinearColor::Green, FLinearColor::Red, Value);

    case EBiomeDebugMode::Occlusion:
        // White (exposed) to Black (occluded)
        return FLinearColor(1.0f - Value, 1.0f - Value, 1.0f - Value);

    case EBiomeDebugMode::FlowAccumulation:
        // Blue (low) to Cyan (high)
        return FLinearColor(0, Value * 0.5f, 1.0f);

    case EBiomeDebugMode::Altitude:
        // Use terrain gradient
        if (Value < 0.25f)
            return FLinearColor::LerpUsingHSV(FLinearColor(0.2f, 0.4f, 0.1f), FLinearColor(0.4f, 0.6f, 0.2f), Value * 4.0f);
        else if (Value < 0.5f)
            return FLinearColor::LerpUsingHSV(FLinearColor(0.4f, 0.6f, 0.2f), FLinearColor(0.6f, 0.5f, 0.3f), (Value - 0.25f) * 4.0f);
        else if (Value < 0.75f)
            return FLinearColor::LerpUsingHSV(FLinearColor(0.6f, 0.5f, 0.3f), FLinearColor(0.5f, 0.5f, 0.5f), (Value - 0.5f) * 4.0f);
        else
            return FLinearColor::LerpUsingHSV(FLinearColor(0.5f, 0.5f, 0.5f), FLinearColor::White, (Value - 0.75f) * 4.0f);

    default:
        // Default gradient: Blue to Red
        return FLinearColor::LerpUsingHSV(FLinearColor::Blue, FLinearColor::Red, Value);
    }
}