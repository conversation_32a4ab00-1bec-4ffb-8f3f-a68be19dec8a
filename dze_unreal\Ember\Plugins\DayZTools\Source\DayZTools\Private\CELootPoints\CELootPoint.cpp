// CELootPoint.cpp
// Copyright Epic Games, Inc. All Rights Reserved.

#include "CELootPoints/CELootPoint.h"
#include "Components/StaticMeshComponent.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "DrawDebugHelpers.h"              // <— for DrawDebugCylinder
#include "UObject/ConstructorHelpers.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"

ACELootPoint::ACELootPoint()
{
	PrimaryActorTick.bCanEverTick = true;   // enable Tick()
	PrimaryActorTick.bStartWithTickEnabled = true;

	// 1) Scene root at actor origin
	SceneRootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("SceneRoot"));
	SetRootComponent(SceneRootComponent);

	// 2) Cylinder mesh, pivot offset up by half-height (50 cm)
	CylinderMeshComponent = CreateDefaultSubobject<UStaticMeshComponent>(TEXT("CylinderMesh"));
	CylinderMeshComponent->SetupAttachment(SceneRootComponent);

	static ConstructorHelpers::FObjectFinder<UStaticMesh> CylinderMeshFinder(
		TEXT("/Engine/BasicShapes/Cylinder.Cylinder"));
	if (CylinderMeshFinder.Succeeded())
	{
		CylinderMeshComponent->SetStaticMesh(CylinderMeshFinder.Object);
		CylinderMeshComponent->SetRelativeLocation(FVector(0.f, 0.f, 50.f));
	}

	CylinderMeshComponent->SetCollisionProfileName(TEXT("NoCollision"));
	CylinderMeshComponent->SetCastShadow(false);
	CylinderMeshComponent->bCastDynamicShadow = false;
	CylinderMeshComponent->SetTranslucentSortPriority(0);
	CylinderMeshComponent->SetVisibility(true);
	CylinderMeshComponent->SetHiddenInGame(false);

	// 3) Create a dynamic translucent‐color material in the constructor
	{
		static ConstructorHelpers::FObjectFinder<UMaterial> SolidMatFinder(
			TEXT("/Engine/EngineDebugMaterials/M_SimpleUnlitTranslucent.M_SimpleUnlitTranslucent"));
		if (SolidMatFinder.Succeeded())
		{
			DynamicMaterialInstance = UMaterialInstanceDynamic::Create(
				SolidMatFinder.Object, this);
			DynamicMaterialInstance->SetVectorParameterValue(
				TEXT("Color"), CylinderColor);
			CylinderMeshComponent->SetMaterial(0, DynamicMaterialInstance);
		}
	}

	// Initial uniform scale
	SetActorScale3D(FVector(1.f, 1.f, 1.f));
}

void ACELootPoint::BeginPlay()
{
	Super::BeginPlay();
	UpdateScale();
	SetupMaterial();
}

void ACELootPoint::PostLoad()
{
	Super::PostLoad();
	UpdateScale();
	SetupMaterial();
}

void ACELootPoint::Tick(float DeltaTime)
{
	Super::Tick(DeltaTime);

#if WITH_EDITOR
	UWorld* World = GetWorld();
	if (World && !World->IsGameWorld())
	{
		// Re-draw the wireframe debug cylinder every frame (single-frame duration)
		const FVector Bottom = GetActorLocation();

		// Use the same scale for X and Y to ensure uniform radius
		const FVector Scale = GetActorScale3D();
		const float Radius = 50.f * Scale.X; // X and Y should be the same due to our scaling logic
		const float Height = 100.f * Scale.Z;

		const FVector Top = Bottom + FVector(0.f, 0.f, Height);

		DrawDebugCylinder(
			World,
			Bottom,
			Top,
			Radius,
			32,
			FColor(0, 195, 20),
			/*bPersistentLines=*/ false,
			/*LifeTime=*/ 0.f,
			/*DepthPriority=*/ 0,
			/*Thickness=*/ 0.3f
		);
	}
#endif
}

#if WITH_EDITOR
void ACELootPoint::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
	Super::PostEditChangeProperty(PropertyChangedEvent);

	if (PropertyChangedEvent.Property &&
		PropertyChangedEvent.Property->GetFName() ==
			GET_MEMBER_NAME_CHECKED(ACELootPoint, CylinderColor))
	{
		if (DynamicMaterialInstance)
		{
			DynamicMaterialInstance->SetVectorParameterValue(
				TEXT("Color"), CylinderColor);
			CylinderMeshComponent->SetMaterial(0, DynamicMaterialInstance);
		}
	}

	UpdateScale();
}

void ACELootPoint::OnConstruction(const FTransform& Transform)
{
	Super::OnConstruction(Transform);
	UpdateScale();
	SetupMaterial();

	// Lock rotation to zero
    SetActorRotation(FRotator::ZeroRotator);
}

void ACELootPoint::EditorApplyTranslation(
	const FVector& DeltaTranslation,
	bool bAltDown, bool bShiftDown, bool bCtrlDown)
{
	Super::EditorApplyTranslation(DeltaTranslation, bAltDown, bShiftDown, bCtrlDown);
}

void ACELootPoint::EditorApplyScale(
	const FVector& DeltaScale,
	const FVector* PivotLocation,
	bool bAltDown, bool bShiftDown, bool bCtrlDown)
{
	if (bIsUpdatingScale)
	{
		Super::EditorApplyScale(DeltaScale, PivotLocation, bAltDown, bShiftDown, bCtrlDown);
		return;
	}

	bIsUpdatingScale = true;

	FVector Current = GetActorScale3D();
	FVector Desired = Current + DeltaScale;
	float AbsX = FMath::Abs(DeltaScale.X), AbsY = FMath::Abs(DeltaScale.Y);

	float TargetXY = (AbsY > AbsX) ? Desired.Y
	               : (AbsX > AbsY) ? Desired.X
	               : FMath::Max(Desired.X, Desired.Y);

	TargetXY = FMath::Max(TargetXY, 0.0001f);

	FVector ModifiedDelta;
	ModifiedDelta.X = TargetXY - Current.X;
	ModifiedDelta.Y = TargetXY - Current.Y;
	ModifiedDelta.Z = DeltaScale.Z;

	Super::EditorApplyScale(ModifiedDelta, PivotLocation, bAltDown, bShiftDown, bCtrlDown);

	bIsUpdatingScale = false;
}
#endif

#if WITH_EDITOR
void ACELootPoint::EditorApplyRotation(
    const FRotator& /*DeltaRotation*/,
    bool /*bAltDown*/, bool /*bShiftDown*/, bool /*bCtrlDown*/)
{
    // Intentionally empty: we don't want any rotation applied.
}
#endif

void ACELootPoint::SetupMaterial()
{
	// If we already have a dynamic instance, just reapply & recolor
	if (DynamicMaterialInstance)
	{
		DynamicMaterialInstance->SetVectorParameterValue(TEXT("Color"), CylinderColor);
		CylinderMeshComponent->SetMaterial(0, DynamicMaterialInstance);
		return;
	}

	// Otherwise load & create it at runtime
	if (UMaterialInstanceDynamic* MID = CreateDefaultTranslucentMaterial())
	{
		DynamicMaterialInstance = MID;
		CylinderMeshComponent->SetMaterial(0, MID);
		MID->SetVectorParameterValue(TEXT("Color"), CylinderColor);
	}
}

UMaterialInstanceDynamic* ACELootPoint::CreateDefaultTranslucentMaterial()
{
	UMaterial* Base = LoadObject<UMaterial>(
		nullptr,
		TEXT("/Engine/EngineDebugMaterials/M_SimpleUnlitTranslucent.M_SimpleUnlitTranslucent"));
	if (!Base)
	{
		Base = LoadObject<UMaterial>(
			nullptr,
			TEXT("/Engine/EngineMaterials/DefaultTranslucent.DefaultTranslucent"));
	}
	return Base
		? UMaterialInstanceDynamic::Create(Base, this)
		: nullptr;
}

void ACELootPoint::UpdateScale()
{
	if (bIsUpdatingScale) return;
	bIsUpdatingScale = true;

	FVector S = GetActorScale3D();
	float Target = FMath::Max(S.X, S.Y);
	Target = FMath::Max(Target, 0.0001f);
	SetActorScale3D(FVector(Target, Target, S.Z));

	bIsUpdatingScale = false;
}

void ACELootPoint::UpdateScale(float TargetXY)
{
	if (bIsUpdatingScale) return;
	bIsUpdatingScale = true;

	FVector S = GetActorScale3D();
	TargetXY = FMath::Max(TargetXY, 0.0001f);
	SetActorScale3D(FVector(TargetXY, TargetXY, S.Z));

	bIsUpdatingScale = false;
}

void ACELootPoint::SetSelected(bool bSelected)
{
    // Store selected state
    bIsSelected = bSelected;

    // Update material to show selection
    if (IsValid(DynamicMaterialInstance))
    {
        // Use a brighter color when selected
        FLinearColor Color = CylinderColor;

        if (bIsSelected)
        {
            // Make the color brighter and more opaque when selected
            Color = FLinearColor(0.f, 0.47f, 0.10f, 0.7f);
        }

        // Update material color
        DynamicMaterialInstance->SetVectorParameterValue(TEXT("Color"), Color);
    }
}
