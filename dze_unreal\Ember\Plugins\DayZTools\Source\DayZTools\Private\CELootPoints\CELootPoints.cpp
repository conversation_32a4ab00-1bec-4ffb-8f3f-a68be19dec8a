// CELootPoints.cpp
#include "CELootPoints/CELootPoints.h"
#include "P3DBlueprint.h"

// Helper function to get the P3D model from ConfigClass
UP3DBlueprint* FCELootPointsRoot::GetModel() const
{
    if (Type.IsValid())
    {
        return Type.Get()->Model.Get();
    }
    return nullptr;
}

// Update the container name based on the container type
void FCELootPointsContainer::UpdateName()
{
    switch (ContainerType)
    {
    case EContainerType::LootFloor:
        Name = TEXT("lootFloor");
        break;
    case EContainerType::LootShelves:
        Name = TEXT("lootshelves");
        break;
    case EContainerType::LootWeapons:
        Name = TEXT("lootweapons");
        break;
    default:
        Name = TEXT("Unknown");
        break;
    }
}

// Get the display name for an item
FString FCELootPointsItem::GetDisplayName() const
{
    switch (ItemType)
    {
    case ECELootPointsItemType::Root:
        return TEXT("Root");
    case ECELootPointsItemType::Container:
        return ContainerData.Name;
    case ECELootPointsItemType::Point:
        return PointData.Name;
    default:
        return TEXT("Unknown");
    }
}

UCELootPoints::UCELootPoints(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Initialize with default values
    AssetName = TEXT("NewLootPoints");

    // Initialize with default structure
    InitializeDefault();
}

void UCELootPoints::InitializeDefault()
{
    // Clear existing items
    Items.Empty();

    // Create root item
    FCELootPointsItem RootItem;
    RootItem.ItemType = ECELootPointsItemType::Root;
    RootItem.ParentIndex = -1; // No parent

    // Add root item
    Items.Add(RootItem);

    // Add a default container
    AddContainer(EContainerType::LootFloor);
}

void UCELootPoints::AddContainer(EContainerType ContainerType)
{
    // Create container item
    FCELootPointsItem ContainerItem;
    ContainerItem.ItemType = ECELootPointsItemType::Container;
    ContainerItem.ParentIndex = 0; // Root is always at index 0
    ContainerItem.ContainerData.ContainerType = ContainerType;
    ContainerItem.ContainerData.UpdateName();

    // Add container item
    Items.Add(ContainerItem);
}

void UCELootPoints::AddPoint(int32 ContainerIndex)
{
    // Validate container index
    if (ContainerIndex <= 0 || ContainerIndex >= Items.Num())
    {
        return;
    }

    // Ensure the container is actually a container
    if (Items[ContainerIndex].ItemType != ECELootPointsItemType::Container)
    {
        return;
    }

    // Create point item
    FCELootPointsItem PointItem;
    PointItem.ItemType = ECELootPointsItemType::Point;
    PointItem.ParentIndex = ContainerIndex;

    // Add point item
    Items.Add(PointItem);
}

void UCELootPoints::RemoveItem(int32 ItemIndex)
{
    // Can't remove root (index 0)
    if (ItemIndex <= 0 || ItemIndex >= Items.Num())
    {
        return;
    }

    // Get the item type
    ECELootPointsItemType ItemType = Items[ItemIndex].ItemType;

    // If it's a container, remove all its points first
    if (ItemType == ECELootPointsItemType::Container)
    {
        // Remove points in reverse order to avoid index issues
        for (int32 i = Items.Num() - 1; i > ItemIndex; --i)
        {
            if (Items[i].ParentIndex == ItemIndex)
            {
                Items.RemoveAt(i);
            }
        }
    }

    // Remove the item itself
    Items.RemoveAt(ItemIndex);

    // Update parent indices for remaining items
    for (int32 i = 0; i < Items.Num(); ++i)
    {
        if (Items[i].ParentIndex > ItemIndex)
        {
            Items[i].ParentIndex--;
        }
    }
}

FString UCELootPoints::ExportToXML() const
{
    return ExportToXMLWithAllProperties();
}

FString UCELootPoints::ExportToXMLWithAllProperties() const
{
    FString XMLOutput;

    // Get the model name: fall back to GetName() if no Type was selected
    FString GroupName = GetName();
    if (Items.Num() > 0 && Items[0].RootData.Type.ToSoftObjectPath().IsValid())
    {
        GroupName = Items[0].RootData.Type.GetAssetName();
    }

    // Root <group>
    XMLOutput += FString::Printf(
        TEXT("<group name=\"%s\" lootmax=\"%d\">\n"),
        *GroupName,
        Items[0].RootData.LootMax
    );

    // <usage> entries
    for (const auto& UsagePtr : Items[0].RootData.Usage)
    {
        if (UsagePtr.ToSoftObjectPath().IsValid())
        {
            XMLOutput += FString::Printf(
                TEXT("\t\t\t\t<usage name=\"%s\" />\n"),
                *UsagePtr.GetAssetName()
            );
        }
    }

    // Process each container (skip index 0)
    for (int32 i = 1; i < Items.Num(); ++i)
    {
        const FCELootPointsItem& Item = Items[i];
        if (Item.ItemType != ECELootPointsItemType::Container)
            continue;

        const FCELootPointsContainer& Container = Item.ContainerData;

        // <container>
        XMLOutput += FString::Printf(
            TEXT("\t\t\t\t<container name=\"%s\" lootmax=\"%d\">\n"),
            *Container.Name,
            Container.LootMax
        );

        // <category> tags
        for (const auto& CategoryPtr : Container.Category)
        {
            if (CategoryPtr.ToSoftObjectPath().IsValid())
            {
                XMLOutput += FString::Printf(
                    TEXT("\t\t\t\t\t\t<category name=\"%s\" />\n"),
                    *CategoryPtr.GetAssetName()
                );
            }
        }

        // <tag> tags
        for (const auto& TagPtr : Container.Tag)
        {
            if (TagPtr.ToSoftObjectPath().IsValid())
            {
                XMLOutput += FString::Printf(
                    TEXT("\t\t\t\t\t\t<tag name=\"%s\" />\n"),
                    *TagPtr.GetAssetName()
                );
            }
        }

        // <point> entries (pos = X/Y/Z, range = ScaleXY, height = ScaleZ)
        for (int32 j = 1; j < Items.Num(); ++j)
        {
            const FCELootPointsItem& PointItem = Items[j];
            if (PointItem.ItemType == ECELootPointsItemType::Point
                && PointItem.ParentIndex == i)
            {
                const FCELootPointsPoint& P = PointItem.PointData;
                // Check if force spawn flag is set
                if (P.bForceSpawn)
                {
                    // Include flags="32" attribute for forced spawn
                    XMLOutput += FString::Printf(
                        TEXT(
                            "\t\t\t\t\t\t<point pos=\"%.3f %.3f %.3f\" "
                            "range=\"%.6f\" height=\"%.6f\" flags=\"32\" />\n"
                        ),
                        P.X/100.0f, P.Y/100.0f, P.Z/100.0f,
                        P.ScaleXY, P.ScaleZ
                    );
                }
                else
                {
                    // Standard point without flags
                    XMLOutput += FString::Printf(
                        TEXT(
                            "\t\t\t\t\t\t<point pos=\"%.3f %.3f %.3f\" "
                            "range=\"%.6f\" height=\"%.6f\" />\n"
                        ),
                        P.Y/100.0f, P.Z/100.0f, P.X/100.0f,
                        P.ScaleXY, P.ScaleZ
                    );
                }
            }
        }

        // Close </container>
        XMLOutput += TEXT("\t\t\t\t</container>\n");
    }

    // Close </group>
    XMLOutput += TEXT("</group>");

    return XMLOutput;
}


