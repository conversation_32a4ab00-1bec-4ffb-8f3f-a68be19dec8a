// CELootPointsAssetActions.cpp
#include "CELootPoints/CELootPointsAssetActions.h"
#include "CELootPoints/CELootPoints.h"
#include "CELootPoints/CELootPointsEditorToolkit.h"
#include "DayZTools.h"

FText FCELootPointsAssetActions::GetName() const
{
    return NSLOCTEXT("AssetTypeActions", "AssetTypeActions_CELootPoints", "CE Loot Points");
}

FColor FCELootPointsAssetActions::GetTypeColor() const
{
    return FColor(128, 64, 64); // Reddish brown color
}

UClass* FCELootPointsAssetActions::GetSupportedClass() const
{
    return UCELootPoints::StaticClass();
}

uint32 FCELootPointsAssetActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FCELootPointsAssetActions::OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor)
{
    // Open the editor for each selected asset
    for (auto ObjIt = InObjects.CreateConstIterator(); ObjIt; ++ObjIt)
    {
        auto LootPointsAsset = Cast<UCELootPoints>(*ObjIt);
        if (LootPointsAsset)
        {
            // Create a new editor instance
            TSharedRef<FCELootPointsEditorToolkit> NewEditor = MakeShareable(new FCELootPointsEditorToolkit());

            // Initialize the editor
            NewEditor->InitCELootPointsEditorToolkit(EToolkitMode::Standalone, EditWithinLevelEditor, LootPointsAsset);
        }
    }
}
