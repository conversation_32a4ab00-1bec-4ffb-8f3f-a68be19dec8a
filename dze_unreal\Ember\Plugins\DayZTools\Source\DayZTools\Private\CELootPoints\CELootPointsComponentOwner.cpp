// CELootPointsComponentOwner.cpp
#include "CELootPoints/CELootPointsComponentOwner.h"

// Sets default values
ACELootPointsComponentOwner::ACELootPointsComponentOwner()
{
    // Set this actor to call Tick() every frame
    PrimaryActorTick.bCanEverTick = false;
    PrimaryActorTick.bStartWithTickEnabled = false;

    // Create a root scene component
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));

    // Set actor properties
    bNetLoadOnClient = false;
    bNetUseOwnerRelevancy = false;
    bReplicates = false;

    // Set actor flags
    SetActorEnableCollision(false);
    SetCanBeDamaged(false);
}

// Called when the game starts or when spawned
void ACELootPointsComponentOwner::BeginPlay()
{
    Super::BeginPlay();
}

// Called every frame
void ACELootPointsComponentOwner::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
}
