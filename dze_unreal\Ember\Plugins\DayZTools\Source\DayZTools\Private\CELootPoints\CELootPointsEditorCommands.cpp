// CELootPointsEditorCommands.cpp
#include "CELootPoints/CELootPointsEditorCommands.h"

#define LOCTEXT_NAMESPACE "CELootPointsEditorCommands"

FCELootPointsEditorCommands::FCELootPointsEditorCommands()
    : TCommands<FCELootPointsEditorCommands>(
        "CELootPointsEditor", // Context name
        NSLOCTEXT("Contexts", "CELootPointsEditor", "CE Loot Points Editor"), // Context description
        NAME_None, // Parent context
        FAppStyle::GetAppStyleSetName() // Icon style set
    )
{
}

void FCELootPointsEditorCommands::RegisterCommands()
{
    UI_COMMAND(ExportToXML, "Export to XML", "Export the loot points to XML", EUserInterfaceActionType::Button, FInputChord(EKeys::E, EModifierKey::Control));
    UI_COMMAND(ToggleGroundPlane, "Toggle Ground Plane", "Toggle the visibility of the ground plane", EUserInterfaceActionType::ToggleButton, FInputChord(EKeys::G));

    // Register copy/paste commands with standard keyboard shortcuts
    UI_COMMAND(CopyItem, "Copy", "Copy the selected item", EUserInterfaceActionType::Button, FInputChord(EKeys::C, EModifierKey::Control));
    UI_COMMAND(PasteItem, "Paste", "Paste the copied item", EUserInterfaceActionType::Button, FInputChord(EKeys::V, EModifierKey::Control));
}

#undef LOCTEXT_NAMESPACE
