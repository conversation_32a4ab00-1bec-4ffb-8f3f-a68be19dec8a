// CELootPointsEditorToolkit.cpp
#include "CELootPoints/CELootPointsEditorToolkit.h"
#include "CELootPoints/CELootPointsEditorViewport.h"
#include "CELootPoints/CELootPointsHierarchy.h"
#include "CELootPoints/CELootPointsItemProxy.h"
#include "CELootPoints/CELootPointsEditorCommands.h"
#include "PropertyEditorModule.h"
#include "IDetailsView.h"
#include "Modules/ModuleManager.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "EditorStyleSet.h"
#include "Widgets/Docking/SDockTab.h"
#include "Misc/MessageDialog.h"
#include "HAL/PlatformApplicationMisc.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "UObject/Package.h"
#include "EditorAssetLibrary.h"

#define LOCTEXT_NAMESPACE "CELootPointsEditor"

const FName FCELootPointsEditorToolkit::ViewportTabID(TEXT("CELootPointsEditor_Viewport"));
const FName FCELootPointsEditorToolkit::DetailsTabID(TEXT("CELootPointsEditor_Details"));
const FName FCELootPointsEditorToolkit::HierarchyTabID(TEXT("CELootPointsEditor_Hierarchy"));

FCELootPointsEditorToolkit::FCELootPointsEditorToolkit()
    : LootPointsAsset(nullptr)
    , SelectedItemIndex(-1)
    , SelectedItemProxy(nullptr)
    , bHasValidClipboardItem(false)
    , bIsSelectingItem(false)
{
    // Register commands
    if (GEditor)
    {
        GEditor->RegisterForUndo(this);
    }
    FCELootPointsEditorCommands::Register();
}

FCELootPointsEditorToolkit::~FCELootPointsEditorToolkit()
{
    // Unregister commands
    if (GEditor)
    {
        GEditor->UnregisterForUndo(this);
    }
    FCELootPointsEditorCommands::Unregister();
}

void FCELootPointsEditorToolkit::InitCELootPointsEditorToolkit(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UCELootPoints* InLootPointsAsset)
{
    UE_LOG(LogTemp, Warning, TEXT("FCELootPointsEditorToolkit::InitCELootPointsEditorToolkit - Start"));


    // Store asset reference
    LootPointsAsset = InLootPointsAsset;

    // Ensure the asset has been properly initialized
    if (LootPointsAsset && LootPointsAsset->Items.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("Asset has no items, initializing with defaults"));
        LootPointsAsset->InitializeDefault();
    }

    // Create details view
    FPropertyEditorModule& PropertyEditorModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");

    FDetailsViewArgs DetailsViewArgs;
    DetailsViewArgs.bUpdatesFromSelection = false;
    DetailsViewArgs.bLockable = false;
    DetailsViewArgs.bAllowSearch = true;
    DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
    DetailsViewArgs.bHideSelectionTip = true;
    DetailsViewArgs.bShowOptions = false;
    DetailsViewArgs.bShowModifiedPropertiesOption = false;
    DetailsViewArgs.bShowDifferingPropertiesOption = false;
    DetailsViewArgs.bAllowMultipleTopLevelObjects = false;
    DetailsViewArgs.bShowPropertyMatrixButton = false;

    DetailsView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);
    DetailsView->OnFinishedChangingProperties().AddRaw(this, &FCELootPointsEditorToolkit::OnPropertyChanged);

    // Create hierarchy widget
    HierarchyWidget = SNew(SCELootPointsHierarchy, SharedThis(this));
    HierarchyWidget->SetAsset(LootPointsAsset);

    // --- Call these BEFORE InitAssetEditor ---
    // Bind commands
    BindCommands();

    // Register toolbar extender
    RegisterToolbarExtender();
    // ---

    // Create tab layout
    const TSharedRef<FTabManager::FLayout> Layout = CreateEditorLayout();

    // Initialize asset editor
    InitAssetEditor(Mode, InitToolkitHost, FCELootPointsEditorCommands::Get().GetContextName(), Layout, true, true, InLootPointsAsset);

    // --- Call these AFTER InitAssetEditor ---
    // Initialize the selected item index to -1 (no selection)
    SelectedItemIndex = -1;

    // Regenerate menus and toolbars to make sure extenders are applied
    RegenerateMenusAndToolbars();
    // ---

    // Only select the root item if the asset has items
    if (LootPointsAsset && LootPointsAsset->Items.Num() > 0)
    {
        // Use a timer to delay the selection until after the UI is fully initialized
        FTimerHandle TimerHandle;
        GEditor->GetTimerManager()->SetTimer(TimerHandle, [this]()
        {
            // First, force a refresh of the viewport to ensure all actors are created
            if (ViewportWidget.IsValid())
            {
                UE_LOG(LogTemp, Warning, TEXT("InitCELootPointsEditorToolkit: Forcing viewport refresh"));
                ViewportWidget->RefreshViewport();
            }

            // Then select the root item
            SelectItem(0);
        }, 0.1f, false);
    }

    UE_LOG(LogTemp, Warning, TEXT("FCELootPointsEditorToolkit::InitCELootPointsEditorToolkit - Complete"));
}

void FCELootPointsEditorToolkit::PostUndo(bool bSuccess)
{
  // Re-sync scene & details
  RefreshEditor();
}

void FCELootPointsEditorToolkit::PostRedo(bool bSuccess)
{
  PostUndo(bSuccess);
}

FName FCELootPointsEditorToolkit::GetToolkitFName() const
{
    return FName("CELootPointsEditor");
}

FText FCELootPointsEditorToolkit::GetBaseToolkitName() const
{
    return LOCTEXT("AppLabel", "CE Loot Points Editor");
}

FString FCELootPointsEditorToolkit::GetWorldCentricTabPrefix() const
{
    return LOCTEXT("WorldCentricTabPrefix", "CE Loot Points ").ToString();
}

FLinearColor FCELootPointsEditorToolkit::GetWorldCentricTabColorScale() const
{
    return FLinearColor(0.7f, 0.3f, 0.3f, 0.5f);
}

void FCELootPointsEditorToolkit::RegisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    // Call parent register tab spawners
    FAssetEditorToolkit::RegisterTabSpawners(InTabManager);

    // Register viewport tab
    InTabManager->RegisterTabSpawner(ViewportTabID, FOnSpawnTab::CreateSP(this, &FCELootPointsEditorToolkit::SpawnViewportTab))
        .SetDisplayName(LOCTEXT("ViewportTab", "Viewport"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef())
        .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Viewports"));

    // Register details tab
    InTabManager->RegisterTabSpawner(DetailsTabID, FOnSpawnTab::CreateSP(this, &FCELootPointsEditorToolkit::SpawnDetailsTab))
        .SetDisplayName(LOCTEXT("DetailsTab", "Details"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef())
        .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Details"));

    // Register hierarchy tab
    InTabManager->RegisterTabSpawner(HierarchyTabID, FOnSpawnTab::CreateSP(this, &FCELootPointsEditorToolkit::SpawnHierarchyTab))
        .SetDisplayName(LOCTEXT("HierarchyTab", "Hierarchy"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef())
        .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Outliner"));
}

void FCELootPointsEditorToolkit::UnregisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    // Call parent unregister tab spawners
    FAssetEditorToolkit::UnregisterTabSpawners(InTabManager);

    // Unregister tabs
    InTabManager->UnregisterTabSpawner(ViewportTabID);
    InTabManager->UnregisterTabSpawner(DetailsTabID);
    InTabManager->UnregisterTabSpawner(HierarchyTabID);
}

void FCELootPointsEditorToolkit::SelectItem(int32 ItemIndex)
{
    // Check if we're already selecting an item
    if (bIsSelectingItem)
    {
        UE_LOG(LogTemp, Warning, TEXT("SelectItem: Already selecting an item, skipping"));
        return;
    }

    // Set the flag to prevent recursive calls
    bIsSelectingItem = true;

    UE_LOG(LogTemp, Warning, TEXT("SelectItem: %d"), ItemIndex);

    // Store selected item index
    SelectedItemIndex = ItemIndex;

    // Always update all UI components to ensure synchronization

    // Update hierarchy widget
    if (HierarchyWidget.IsValid())
    {
        HierarchyWidget->SelectItem(ItemIndex);
    }

    // Update viewport widget
    if (ViewportWidget.IsValid())
    {
        ViewportWidget->SelectItem(ItemIndex);
    }

    // Create proxy for the selected item
    CreateItemProxy(ItemIndex);

    // Update details view
    UpdateDetailsView();

    // Clear the flag
    bIsSelectingItem = false;
}

void FCELootPointsEditorToolkit::HandleTransformUpdate(int32 ItemIndex, const FTransform& NewTransform)
{
    // Sanity check
    if (!LootPointsAsset || ItemIndex < 0 || ItemIndex >= LootPointsAsset->Items.Num())
    {
        return;
    }

    FCELootPointsItem& Item = LootPointsAsset->Items[ItemIndex];

    // Only translate point‐type items
    if (Item.ItemType == ECELootPointsItemType::Point)
    {
        // Begin the transaction / undo buffer
        LootPointsAsset->Modify();

        // Update the asset data
        Item.PointData.X = NewTransform.GetLocation().X;
        Item.PointData.Y = NewTransform.GetLocation().Y;
        Item.PointData.Z = NewTransform.GetLocation().Z;

        // Notify the editor that this UObject has changed
        LootPointsAsset->PostEditChange();

        // If we're editing the currently selected item, refresh the details view
        if (ItemIndex == SelectedItemIndex && SelectedItemProxy)
        {
            SelectedItemProxy->Initialize(LootPointsAsset, ItemIndex);
            UpdateDetailsView();
        }
    }
}


// Flag to prevent recursive refreshing
static bool bIsRefreshingEditor = false;

void FCELootPointsEditorToolkit::RefreshEditor()
{
    // Check if we're already refreshing the editor
    if (bIsRefreshingEditor)
    {
        UE_LOG(LogTemp, Warning, TEXT("RefreshEditor: Already refreshing the editor, skipping"));
        return;
    }

    // Set the flag to prevent recursive calls
    bIsRefreshingEditor = true;

    // Refresh hierarchy widget
    if (HierarchyWidget.IsValid())
    {
        HierarchyWidget->RefreshHierarchy();
    }

    // Refresh viewport widget
    if (ViewportWidget.IsValid())
    {
        ViewportWidget->RefreshViewport();
    }

    // Update details view
    UpdateDetailsView();

    // Clear the flag
    bIsRefreshingEditor = false;
}

void FCELootPointsEditorToolkit::ExportToXML()
{
    // Check if we have a valid asset
    if (!LootPointsAsset)
    {
        return;
    }

    // Export to XML
    FString XMLOutput = LootPointsAsset->ExportToXML();

    // Show message
    FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("ExportToXMLSuccess", "XML export completed successfully."));
}

void FCELootPointsEditorToolkit::ToggleGroundPlane()
{
    // Toggle ground plane in viewport
    if (ViewportWidget.IsValid() && ViewportWidget->GetPreviewScene().IsValid())
    {
        bool bCurrentVisibility = ViewportWidget->GetPreviewScene()->IsGroundPlaneVisible();
        ViewportWidget->GetPreviewScene()->ToggleGroundPlane(!bCurrentVisibility);
    }
}

bool FCELootPointsEditorToolkit::IsGroundPlaneVisible() const
{
    // Check if ground plane is visible
    if (ViewportWidget.IsValid() && ViewportWidget->GetPreviewScene().IsValid())
    {
        return ViewportWidget->GetPreviewScene()->IsGroundPlaneVisible();
    }

    return false;
}

TSharedRef<FTabManager::FLayout> FCELootPointsEditorToolkit::CreateEditorLayout()
{
    // Create tab layout
    return FTabManager::NewLayout("CELootPointsEditorLayout_v1")
        ->AddArea
        (
            FTabManager::NewPrimaryArea()->SetOrientation(Orient_Vertical)
            ->Split
            (
                FTabManager::NewSplitter()->SetOrientation(Orient_Horizontal)->SetSizeCoefficient(0.9f)
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.2f)
                    ->AddTab(HierarchyTabID, ETabState::OpenedTab)
                )
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.6f)
                    ->AddTab(ViewportTabID, ETabState::OpenedTab)
                )
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.2f)
                    ->AddTab(DetailsTabID, ETabState::OpenedTab)
                )
            )
        );
}

TSharedRef<SDockTab> FCELootPointsEditorToolkit::SpawnViewportTab(const FSpawnTabArgs& Args)
{
    // Create a new advanced preview scene
    FAdvancedPreviewScene::ConstructionValues CVS;
    CVS.bCreatePhysicsScene = true;  // No physics needed
    CVS.bForceMipsResident = true;    // Better texture quality
    CVS.SkyBrightness = 1.0f;         // Standard brightness

    TSharedPtr<FCELootPointsPreviewScene> PreviewScene = MakeShareable(new FCELootPointsPreviewScene(CVS));

    // Set the editor toolkit reference
    PreviewScene->SetEditorToolkit(this);

    // Create the viewport widget
    ViewportWidget = SNew(SCELootPointsEditorViewport)
        .PreviewScene(PreviewScene)
        .Asset(LootPointsAsset)
        .EditorToolkit(this);

    // Create an extender for the viewport toolbar
    TSharedPtr<FExtender> ToolbarExtender = MakeShareable(new FExtender);
    // Add the extender to the viewport
    ViewportWidget->AddViewportToolbarExtender(ToolbarExtender);

    // Log for debugging
    UE_LOG(LogTemp, Display, TEXT("SpawnViewportTab: Created viewport for asset %s"),
    LootPointsAsset ? *LootPointsAsset->GetName() : TEXT("NULL"));

    return SNew(SDockTab)
        .Label(LOCTEXT("ViewportTitle", "Viewport"))
        [
            ViewportWidget.ToSharedRef()
        ];
}

TSharedRef<SDockTab> FCELootPointsEditorToolkit::SpawnDetailsTab(const FSpawnTabArgs& Args)
{
    // Create details tab
    return SNew(SDockTab)
        .Label(LOCTEXT("DetailsTitle", "Details"))
        [
            DetailsView.ToSharedRef()
        ];
}

TSharedRef<SDockTab> FCELootPointsEditorToolkit::SpawnHierarchyTab(const FSpawnTabArgs& Args)
{
    // Create hierarchy tab
    return SNew(SDockTab)
        .Label(LOCTEXT("HierarchyTitle", "Hierarchy"))
        [
            HierarchyWidget.ToSharedRef()
        ];
}

void FCELootPointsEditorToolkit::CreateItemProxy(int32 ItemIndex)
{
    // Clean up existing proxy
    if (SelectedItemProxy)
    {
        SelectedItemProxy->RemoveFromRoot();
        SelectedItemProxy = nullptr;
    }

    // Check if we have a valid asset and index
    if (!LootPointsAsset)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateItemProxy: LootPointsAsset is null"));
        return;
    }

    // Check if the asset has any items
    if (LootPointsAsset->Items.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateItemProxy: Asset has no items"));
        return;
    }

    // Check if the index is valid
    if (ItemIndex < 0 || ItemIndex >= LootPointsAsset->Items.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateItemProxy: Invalid item index %d (valid range: 0-%d)"),
            ItemIndex, LootPointsAsset->Items.Num() - 1);
        return;
    }

    // Create new proxy
    SelectedItemProxy = NewObject<UCELootPointsItemProxy>();
    if (!SelectedItemProxy)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateItemProxy: Failed to create proxy object"));
        return;
    }

    // Add to root to prevent garbage collection
    SelectedItemProxy->AddToRoot();

    // Initialize the proxy
    SelectedItemProxy->Initialize(LootPointsAsset, ItemIndex);

    UE_LOG(LogTemp, Warning, TEXT("CreateItemProxy: Created proxy for item %d"), ItemIndex);
}

void FCELootPointsEditorToolkit::UpdateDetailsView()
{
    // Update details view
    if (DetailsView.IsValid())
    {
        // Check if we have a valid proxy
        if (SelectedItemProxy)
        {
            UE_LOG(LogTemp, Warning, TEXT("UpdateDetailsView: Setting proxy for item %d"), SelectedItemIndex);
            DetailsView->SetObject(SelectedItemProxy);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("UpdateDetailsView: No proxy available, clearing details view"));
            DetailsView->SetObject(nullptr);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateDetailsView: Details view is not valid"));
    }
}

// Flag to prevent recursive property change handling
static bool bIsHandlingPropertyChange = false;
void FCELootPointsEditorToolkit::OnPropertyChanged(const FPropertyChangedEvent& PropertyChangedEvent)
{
    // Prevent recursion
    if (bIsHandlingPropertyChange)
    {
        UE_LOG(LogTemp, Warning, TEXT("OnPropertyChanged: Already handling a property change, skipping"));
        return;
    }
    bIsHandlingPropertyChange = true;

    // First, push whatever the user just edited back into our asset
    SelectedItemProxy->ApplyChanges();

    // If they just changed the root-item’s Type, swap in the new P3D and repaint
    if (SelectedItemProxy->bIsRoot
        && PropertyChangedEvent.Property
        && PropertyChangedEvent.Property->GetFName() == GET_MEMBER_NAME_CHECKED(UCELootPointsItemProxy, Type))
    {
        UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: Root-Type changed, updating P3D"));

        // Attempt to rename the asset based on the selected Config Class
        AttemptRenameToConfigClass();

        if (ViewportWidget.IsValid() && ViewportWidget->GetPreviewScene().IsValid())
        {
            // Swap out the old blueprint mesh for the new one
            bool bOK = ViewportWidget->GetPreviewScene()->UpdateP3DModelFromRootType();
            UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: UpdateP3DModelFromRootType() %s"),
                bOK ? TEXT("succeeded") : TEXT("failed"));

            // And redraw the viewport
            ViewportWidget->RefreshViewport();
            UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: Viewport refreshed"));
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("OnPropertyChanged: Cannot update P3D — invalid viewport or scene"));
        }
    }

    // In all cases, rebuild the hierarchy/toolbar/details
    RefreshEditor();

    bIsHandlingPropertyChange = false;
}


void FCELootPointsEditorToolkit::BindCommands()
{
    // Get command list
    const TSharedRef<FUICommandList>& CommandList = GetToolkitCommands();

    // Bind commands
    CommandList->MapAction(
        FCELootPointsEditorCommands::Get().ExportToXML,
        FExecuteAction::CreateSP(this, &FCELootPointsEditorToolkit::ExportToXML),
        FCanExecuteAction::CreateLambda([this]() { return LootPointsAsset != nullptr; })
    );

    CommandList->MapAction(
        FCELootPointsEditorCommands::Get().ToggleGroundPlane,
        FExecuteAction::CreateSP(this, &FCELootPointsEditorToolkit::ToggleGroundPlane),
        FCanExecuteAction(),
        FIsActionChecked::CreateSP(this, &FCELootPointsEditorToolkit::IsGroundPlaneVisible)
    );

    // Bind copy/paste commands
    CommandList->MapAction(
        FCELootPointsEditorCommands::Get().CopyItem,
        FExecuteAction::CreateSP(this, &FCELootPointsEditorToolkit::CopySelectedItem),
        FCanExecuteAction::CreateLambda([this]() {
            return LootPointsAsset != nullptr && SelectedItemIndex >= 0 && SelectedItemIndex < LootPointsAsset->Items.Num();
        })
    );

    CommandList->MapAction(
        FCELootPointsEditorCommands::Get().PasteItem,
        FExecuteAction::CreateSP(this, &FCELootPointsEditorToolkit::PasteItem),
        FCanExecuteAction::CreateSP(this, &FCELootPointsEditorToolkit::CanPasteItem)
    );
}

void FCELootPointsEditorToolkit::RegisterToolbarExtender()
{
    // Create toolbar extender
    TSharedPtr<FExtender> ToolbarExtender = MakeShareable(new FExtender);

    // Add toolbar extension
    ToolbarExtender->AddToolBarExtension(
        "Asset",
        EExtensionHook::After,
        GetToolkitCommands(),
        FToolBarExtensionDelegate::CreateSP(this, &FCELootPointsEditorToolkit::BuildToolbar)
    );

    // Add extender
    AddToolbarExtender(ToolbarExtender);
}

void FCELootPointsEditorToolkit::BuildToolbar(FToolBarBuilder& ToolBarBuilder)
{
    // Add export button
    ToolBarBuilder.BeginSection("Export");
    {
        ToolBarBuilder.AddToolBarButton(
            FCELootPointsEditorCommands::Get().ExportToXML,
            NAME_None,
            LOCTEXT("ExportToXMLButton", "Export to XML"),
            LOCTEXT("ExportToXMLTooltip", "Export the loot points to XML"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "AssetEditor.SaveAsset.Toolbar")
        );
    }
    ToolBarBuilder.EndSection();



    // Add ground plane toggle button
    ToolBarBuilder.BeginSection("Options");
    {
        ToolBarBuilder.AddToolBarButton(
            FCELootPointsEditorCommands::Get().ToggleGroundPlane,
            NAME_None,
            LOCTEXT("ToggleGroundPlane_Label", "Show Floor"),
            LOCTEXT("ToggleGroundPlane_Tooltip", "Toggle Floor Plane Visibility"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "BlueprintEditor.ShowFloor")
        );
    }
    ToolBarBuilder.EndSection();
}

void FCELootPointsEditorToolkit::CopySelectedItem()
{
    // Check if we have a valid asset and selection
    if (!LootPointsAsset || SelectedItemIndex < 0 || SelectedItemIndex >= LootPointsAsset->Items.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("CopySelectedItem: No valid item selected"));
        return;
    }

    // Copy the selected item to the clipboard
    ClipboardItem = LootPointsAsset->Items[SelectedItemIndex];

    // Mark that we have a valid item in the clipboard
    bHasValidClipboardItem = true;

    UE_LOG(LogTemp, Display, TEXT("CopySelectedItem: Copied item %d to clipboard"), SelectedItemIndex);
}

bool FCELootPointsEditorToolkit::CanPasteItem() const
{
    // Check if we have a valid asset and clipboard item
    if (!LootPointsAsset || !bHasValidClipboardItem)
    {
        return false;
    }

    // Check if we have a valid selection
    if (SelectedItemIndex < 0 || SelectedItemIndex >= LootPointsAsset->Items.Num())
    {
        return false;
    }

    // Get the selected item type and clipboard item type
    ECELootPointsItemType SelectedType = LootPointsAsset->Items[SelectedItemIndex].ItemType;
    ECELootPointsItemType ClipboardType = ClipboardItem.ItemType;

    // We can only duplicate points and containers, not the root
    if (ClipboardType == ECELootPointsItemType::Root)
    {
        return false;
    }

    // For points, we need to be selecting a point or its parent container
    if (ClipboardType == ECELootPointsItemType::Point)
    {
        // If we're selecting a point, we can duplicate it
        if (SelectedType == ECELootPointsItemType::Point)
        {
            return true;
        }

        // If we're selecting a container, we can add a new point to it
        if (SelectedType == ECELootPointsItemType::Container)
        {
            return true;
        }
    }

    // For containers, we need to be selecting a container or the root
    if (ClipboardType == ECELootPointsItemType::Container)
    {
        // If we're selecting a container, we can duplicate it
        if (SelectedType == ECELootPointsItemType::Container)
        {
            return true;
        }

        // If we're selecting the root, we can add a new container to it
        if (SelectedType == ECELootPointsItemType::Root)
        {
            return true;
        }
    }

    // If we get here, we can't paste
    return false;
}

void FCELootPointsEditorToolkit::PasteItem()
{
    // Check if we can paste
    if (!CanPasteItem())
    {
        UE_LOG(LogTemp, Warning, TEXT("PasteItem: Cannot paste item"));
        return;
    }

    // Get the selected item type and clipboard item type
    ECELootPointsItemType SelectedType = LootPointsAsset->Items[SelectedItemIndex].ItemType;
    ECELootPointsItemType ClipboardType = ClipboardItem.ItemType;

    // Begin transaction for undo/redo
    GEditor->BeginTransaction(LOCTEXT("PasteItem", "Paste Item"));

    // Mark the asset as modified
    LootPointsAsset->Modify();

    // Create a new item based on the clipboard item
    FCELootPointsItem NewItem = ClipboardItem;
    int32 NewItemIndex = -1;

    // Handle different paste scenarios based on what we're copying and where we're pasting
    if (ClipboardType == ECELootPointsItemType::Point)
    {
        // If we're copying a point...
        if (SelectedType == ECELootPointsItemType::Point)
        {
            // If we're selecting a point, duplicate it with the same parent
            int32 ParentIndex = LootPointsAsset->Items[SelectedItemIndex].ParentIndex;
            NewItem.ParentIndex = ParentIndex;
        }
        else if (SelectedType == ECELootPointsItemType::Container)
        {
            // If we're selecting a container, add the point to that container
            NewItem.ParentIndex = SelectedItemIndex;
        }

        // Add the new point to the asset
        NewItemIndex = LootPointsAsset->Items.Add(NewItem);
    }
    else if (ClipboardType == ECELootPointsItemType::Container)
    {
        // If we're copying a container...
        if (SelectedType == ECELootPointsItemType::Container)
        {
            // If we're selecting a container, duplicate it with the same parent (usually root)
            int32 ParentIndex = LootPointsAsset->Items[SelectedItemIndex].ParentIndex;
            NewItem.ParentIndex = ParentIndex;
        }
        else if (SelectedType == ECELootPointsItemType::Root)
        {
            // If we're selecting the root, add the container to the root
            NewItem.ParentIndex = 0; // Root is always at index 0
        }

        // Add the new container to the asset
        NewItemIndex = LootPointsAsset->Items.Add(NewItem);
    }

    // End transaction
    GEditor->EndTransaction();

    // Use a multi-step approach with timers to ensure proper selection and visibility
    if (NewItemIndex >= 0)
    {
        // Step 1: Refresh the editor and deselect everything
        RefreshEditor();
        SelectItem(INDEX_NONE);

        // Step 2: After a short delay, refresh the viewport and hierarchy
        FTimerHandle RefreshTimerHandle;
        GEditor->GetTimerManager()->SetTimer(RefreshTimerHandle, [this]()
        {
            if (ViewportWidget.IsValid())
            {
                UE_LOG(LogTemp, Warning, TEXT("PasteItem: Refreshing viewport"));
                ViewportWidget->RefreshViewport();
            }

            if (HierarchyWidget.IsValid())
            {
                UE_LOG(LogTemp, Warning, TEXT("PasteItem: Refreshing hierarchy"));
                HierarchyWidget->RefreshHierarchy();
            }
        }, 0.05f, false);

        // Step 3: After another delay, select the new item
        FTimerHandle SelectionTimerHandle;
        GEditor->GetTimerManager()->SetTimer(SelectionTimerHandle, [this, NewItemIndex]()
        {
            UE_LOG(LogTemp, Warning, TEXT("PasteItem: Selecting new item at index %d"), NewItemIndex);

            // First make sure the hierarchy is properly updated
            if (HierarchyWidget.IsValid())
            {
                HierarchyWidget->SelectItem(NewItemIndex);
            }

            // Then select the item through the toolkit to ensure all components are updated
            SelectItem(NewItemIndex);

            // Final refresh to ensure everything is in sync
            RefreshEditor();

            // Force the viewport to update
            if (ViewportWidget.IsValid())
            {
                ViewportWidget->RefreshViewport();
            }
        }, 0.15f, false);
    }
    else
    {
        // If we don't have a new item, just refresh the editor
        RefreshEditor();
    }

    UE_LOG(LogTemp, Display, TEXT("PasteItem: Pasted item successfully as new item at index %d"), NewItemIndex);
}

bool FCELootPointsEditorToolkit::AttemptRenameToConfigClass()
{
    if (!LootPointsAsset)
    {
        return false;
    }

    // Get the selected Config Class asset from the root item
    if (SelectedItemIndex != 0 || !SelectedItemProxy || !SelectedItemProxy->bIsRoot)
    {
        // We need to be selecting the root item
        return false;
    }

    // Get the Config Class asset
    UConfigClass* ConfigClass = SelectedItemProxy->Type.LoadSynchronous();

    // If no Config Class selected (or it's null/invalid), don't do anything
    if (!ConfigClass || !IsValid(ConfigClass))
    {
        return false;
    }

    // Get the name of the selected Config Class asset
    FString NewName = ConfigClass->GetName();

    // If the asset already has this name, no need to rename
    if (LootPointsAsset->GetName() == NewName)
    {
        return true;
    }

    // Check if an asset with this name already exists in the same folder
    FString CurrentPath = LootPointsAsset->GetPathName();
    FString CurrentPackagePath = FPackageName::GetLongPackagePath(CurrentPath);
    FString NewAssetPath = CurrentPackagePath + TEXT("/") + NewName;

    // Check if asset with this path already exists
    if (UEditorAssetLibrary::DoesAssetExist(NewAssetPath))
    {
        // Asset with the same name already exists, show error message and reset the Type property
        FText ErrorMessage = FText::Format(
            FText::FromString(TEXT("Loot Points for {0} already exist")),
            FText::FromString(NewName)
        );
        FMessageDialog::Open(EAppMsgType::Ok, ErrorMessage);

        // Reset the Type property to None
        SelectedItemProxy->Type = nullptr;
        SelectedItemProxy->ApplyChanges();

        return false;
    }

    // Get the Asset Tools module
    IAssetTools& AssetTools = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools").Get();

    // Get the asset's package
    UPackage* Package = LootPointsAsset->GetOutermost();
    FString PackagePath = Package->GetName();

    // Get just the folder path without the asset name
    FString FolderPath = FPaths::GetPath(PackagePath);

    // Prepare the rename data
    TArray<FAssetRenameData> AssetsAndNames;
    FAssetRenameData RenameData(LootPointsAsset, FolderPath, NewName);
    AssetsAndNames.Add(RenameData);

    // Perform the rename operation
    bool bSuccess = AssetTools.RenameAssets(AssetsAndNames);

    return bSuccess;
}

#undef LOCTEXT_NAMESPACE
