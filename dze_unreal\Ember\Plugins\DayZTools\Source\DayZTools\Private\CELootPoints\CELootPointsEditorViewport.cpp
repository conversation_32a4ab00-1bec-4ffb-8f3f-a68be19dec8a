// CELootPointsEditorViewport.cpp
#include "CELootPoints/CELootPointsEditorViewport.h"
#include "CELootPoints/CELootPointsEditorToolkit.h"
#include "CELootPoints/CELootPointsViewportClient.h"
#include "CELootPoints/CELootPointsPreviewScene.h"
#include "CELootPoints/CELootPointsEditorViewportToolBar.h"

// Engine includes
#include "Editor.h"
#include "EditorViewportClient.h"
#include "SEditorViewport.h"
#include "EditorStyleSet.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Input/SCheckBox.h"
#include "Widgets/SViewport.h"
#include "Framework/Docking/TabManager.h"
#include "Widgets/SWindow.h"
#include "Toolkits/IToolkit.h"

#define LOCTEXT_NAMESPACE "CELootPointsEditor"

// Define the static constant
const float SCELootPointsEditorViewport::WorldCheckInterval = 0.5f;

void SCELootPointsEditorViewport::Construct(const FArguments& InArgs)
{
    UE_LOG(LogTemp, Warning, TEXT("SCELootPointsEditorViewport::Construct - Start"));

    // Store references from arguments
    PreviewScene = InArgs._PreviewScene;
    Asset = InArgs._Asset;
    EditorToolkit = InArgs._EditorToolkit;

    // Initialize world check attempts
    WorldCheckAttempts = 0;

    // Call parent constructor - this will call MakeEditorViewportClient
    SEditorViewport::Construct(SEditorViewport::FArguments());

    // Start a timer to check for a valid world and refresh the viewport
    // This ensures the viewport is properly refreshed when the asset is opened
    StartWorldCheckTimer();

    // Log for debugging
    UE_LOG(LogTemp, Warning, TEXT("SCELootPointsEditorViewport::Construct - Complete"));
}

SCELootPointsEditorViewport::~SCELootPointsEditorViewport()
{
    UE_LOG(LogTemp, Warning, TEXT("SCELootPointsEditorViewport::~SCELootPointsEditorViewport"));

    // Clear the world check timer
    if (GEditor)
    {
        GEditor->GetTimerManager()->ClearTimer(WorldCheckTimerHandle);
    }

    // Make sure the viewport client is released
    if (ViewportClient.IsValid())
    {
        ViewportClient.Reset();
    }

    // Make sure the preview scene is released
    if (PreviewScene.IsValid())
    {
        PreviewScene.Reset();
    }
}

void SCELootPointsEditorViewport::SetAsset(UCELootPoints* InAsset)
{
    UE_LOG(LogTemp, Warning, TEXT("SCELootPointsEditorViewport::SetAsset - %s"),
        InAsset ? *InAsset->GetName() : TEXT("NULL"));

    // Store asset reference regardless of whether it has changed
    Asset = InAsset;

    // Update preview scene
    if (PreviewScene.IsValid())
    {
        PreviewScene->SetAsset(Asset);
    }

    // Start a timer to check for a valid world and refresh the viewport
    // This ensures the viewport is properly refreshed when the asset is set
    StartWorldCheckTimer();
}

void SCELootPointsEditorViewport::RefreshViewport()
{
    UE_LOG(LogTemp, Display, TEXT("SCELootPointsEditorViewport::RefreshViewport"));

    if (PreviewScene.IsValid())
    {
        // Make sure the preview‐scene knows about the current asset
        PreviewScene->SetAsset(Asset);

        // Now rebuild the actors
        PreviewScene->RefreshScene();

        if (ViewportClient)
        {
            UE_LOG(LogTemp, Display, TEXT("SCELootPointsEditorViewport::RefreshViewport - invalidating"));
            ViewportClient->Invalidate();
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SCELootPointsEditorViewport::RefreshViewport - no preview scene"));
    }
}

void SCELootPointsEditorViewport::SelectItem(int32 ItemIndex)
{
    UE_LOG(LogTemp, Warning, TEXT("SCELootPointsEditorViewport::SelectItem - %d"), ItemIndex);

    // Select the item in the preview scene
    if (PreviewScene.IsValid())
    {
        PreviewScene->SelectItem(ItemIndex);
    }

    // Update the viewport client
    if (ViewportClient.IsValid())
    {
        ViewportClient->SetSelectedItemIndex(ItemIndex);
        ViewportClient->Invalidate();
    }
}

TSharedRef<FEditorViewportClient> SCELootPointsEditorViewport::MakeEditorViewportClient()
{
    UE_LOG(LogTemp, Warning, TEXT("SCELootPointsEditorViewport::MakeEditorViewportClient"));

    // Retrieve the toolkit host from the owning editor toolkit
    TSharedPtr<IToolkitHost> ToolkitHostPtr = EditorToolkit->GetToolkitHost();
    check(ToolkitHostPtr.IsValid());

    // Create the viewport client with the toolkit host
    ViewportClient = MakeShareable(new FCELootPointsViewportClient(
        ToolkitHostPtr.ToSharedRef(),
        PreviewScene.Get(),
        SharedThis(this),
        EditorToolkit
    ));

    return ViewportClient.ToSharedRef();
}

TSharedPtr<SWidget> SCELootPointsEditorViewport::MakeViewportToolbar()
{
    // Create the toolbar
    return SNew(SCELootPointsEditorViewportToolBar, SharedThis(this))
        .Cursor(EMouseCursor::Default);
}

TSharedRef<SEditorViewport> SCELootPointsEditorViewport::GetViewportWidget()
{
    return SharedThis(this);
}


bool SCELootPointsEditorViewport::IsGroundPlaneVisible() const
{
     return bShowGroundPlane; // Or however you track visibility
}

void SCELootPointsEditorViewport::OnFloatingButtonClicked()
{
    // Handle floating button click if needed
}



TSharedPtr<FExtender> SCELootPointsEditorViewport::GetExtenders() const
{
    TSharedPtr<FExtender> CombinedExtender = MakeShareable(new FExtender);

    // Add all registered extenders
    for (const TSharedPtr<FExtender>& Extender : ViewportToolbarExtenders)
    {
        if (Extender.IsValid())
        {
            TArray<TSharedPtr<FExtender>> Extenders;
            Extenders.Add(Extender);
            CombinedExtender->Combine(Extenders);
        }
    }

    return CombinedExtender;
}

void SCELootPointsEditorViewport::ToggleGroundPlaneVisibility()
{
    if (PreviewScene.IsValid())
    {
        bShowGroundPlane = !bShowGroundPlane;
        PreviewScene->SetFloorVisibility(bShowGroundPlane);

        if (ViewportClient.IsValid())
        {
            ViewportClient->Invalidate();
        }
    }
}

void SCELootPointsEditorViewport::ExportEventGroup()
{
    // Forward the export request to the editor toolkit
    if (EditorToolkit)
    {
        //EditorToolkit->ExportEventGroup();
    }
}

void SCELootPointsEditorViewport::StartWorldCheckTimer()
{
    UE_LOG(LogTemp, Display, TEXT("SCELootPointsEditorViewport::StartWorldCheckTimer"));

    // Reset the attempt counter
    WorldCheckAttempts = 0;

    // Get the world timer manager
    if (GEditor)
    {
        // Clear any existing timer
        GEditor->GetTimerManager()->ClearTimer(WorldCheckTimerHandle);

        // Set a timer to check for a valid world
        GEditor->GetTimerManager()->SetTimer(
            WorldCheckTimerHandle,
            FTimerDelegate::CreateSP(this, &SCELootPointsEditorViewport::CheckWorldAndRefresh),
            WorldCheckInterval,
            true); // true = looping

        UE_LOG(LogTemp, Display, TEXT("SCELootPointsEditorViewport::StartWorldCheckTimer - Timer started"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SCELootPointsEditorViewport::StartWorldCheckTimer - Failed to get timer manager"));
    }
}

void SCELootPointsEditorViewport::CheckWorldAndRefresh()
{
    // Increment the attempt counter
    WorldCheckAttempts++;

    UE_LOG(LogTemp, Display, TEXT("SCELootPointsEditorViewport::CheckWorldAndRefresh - Attempt %d of %d"),
        WorldCheckAttempts, MaxWorldCheckAttempts);

    // Check if we have a valid preview scene
    if (!PreviewScene.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("SCELootPointsEditorViewport::CheckWorldAndRefresh - Preview scene is not valid"));

        // Stop the timer if we've reached the maximum number of attempts
        if (WorldCheckAttempts >= MaxWorldCheckAttempts && GEditor)
        {
            GEditor->GetTimerManager()->ClearTimer(WorldCheckTimerHandle);
            UE_LOG(LogTemp, Error, TEXT("SCELootPointsEditorViewport::CheckWorldAndRefresh - Max attempts reached, stopping timer"));
        }

        return;
    }

    // Check if the preview scene has a valid world
    UWorld* World = PreviewScene->GetWorld();
    if (World && World->PersistentLevel)
    {
        UE_LOG(LogTemp, Display, TEXT("SCELootPointsEditorViewport::CheckWorldAndRefresh - World is valid: %s"),
            *World->GetName());

        // Stop the timer
        if (GEditor)
        {
            GEditor->GetTimerManager()->ClearTimer(WorldCheckTimerHandle);
            UE_LOG(LogTemp, Display, TEXT("SCELootPointsEditorViewport::CheckWorldAndRefresh - Timer stopped"));
        }

        // Refresh the viewport
        RefreshViewport();
    }
    else
    {
        UE_LOG(LogTemp, Display, TEXT("SCELootPointsEditorViewport::CheckWorldAndRefresh - World is not valid yet"));
    }
}

#undef LOCTEXT_NAMESPACE
