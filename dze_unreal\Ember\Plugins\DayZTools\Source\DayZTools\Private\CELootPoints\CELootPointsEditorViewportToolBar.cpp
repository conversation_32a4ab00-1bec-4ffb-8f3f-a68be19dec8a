// CELootPointsEditorViewportToolBar.cpp
#include "CELootPoints/CELootPointsEditorViewportToolBar.h"
#include "CELootPoints/CELootPointsEditorViewport.h"
#include "CELootPoints/CELootPointsEditorViewportCommands.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"


void SCELootPointsEditorViewportToolBar::Construct(const FArguments& InArgs, TSharedPtr<SCELootPointsEditorViewport> InViewport)
{
    EditorViewport = InViewport;
    SCommonEditorViewportToolbarBase::Construct(SCommonEditorViewportToolbarBase::FArguments(), InViewport);
}

TSharedRef<SWidget> SCELootPointsEditorViewportToolBar::MakeToolbar(const TSharedPtr<FExtender> InExtenders)
{
    // Create a command list
    TSharedPtr<FUICommandList> CommandList = MakeShareable(new FUICommandList);

    // Bind the toggle ground plane command
    CommandList->MapAction(
        FCELootPointsEditorViewportCommands::Get().ToggleGroundPlane,
        FExecuteAction::CreateSP(EditorViewport.ToSharedRef(), &SCELootPointsEditorViewport::ToggleGroundPlaneVisibility),
        FCanExecuteAction(),
        FIsActionChecked::CreateSP(EditorViewport.ToSharedRef(), &SCELootPointsEditorViewport::IsGroundPlaneVisible)
    );

    // Bind the export event group command
    CommandList->MapAction(
        FCELootPointsEditorViewportCommands::Get().ExportEventGroup,
        FExecuteAction::CreateSP(EditorViewport.ToSharedRef(), &SCELootPointsEditorViewport::ExportEventGroup),
        FCanExecuteAction()
    );

    // Get the extenders from the viewport
    TSharedPtr<FExtender> CombinedExtender = InExtenders;
    if (EditorViewport.IsValid())
    {
        // Get any additional extenders from the viewport
        TSharedPtr<FExtender> ViewportExtenders = EditorViewport->GetExtenders();
        if (ViewportExtenders.IsValid())
        {
            // Combine the extenders
            if (!CombinedExtender.IsValid())
            {
                CombinedExtender = ViewportExtenders;
            }
            else
            {
                TArray<TSharedPtr<FExtender>> Extenders;
                Extenders.Add(ViewportExtenders);
                CombinedExtender->Combine(Extenders);
            }
        }
    }

    // Create a toolbar builder with the combined extenders
    FToolBarBuilder ToolbarBuilder(CommandList, FMultiBoxCustomization::None, CombinedExtender);

    // Add the ground plane toggle button
    ToolbarBuilder.BeginSection("Options");
    {
        ToolbarBuilder.AddToolBarButton(
            FCELootPointsEditorViewportCommands::Get().ToggleGroundPlane,
            NAME_None,
            NSLOCTEXT("CELootPointsEditorViewport", "GroundPlane", "Ground"),
            NSLOCTEXT("CELootPointsEditorViewport", "GroundPlaneTooltip", "Toggle the visibility of the ground plane"),
            FSlateIcon()
        );
    }
    ToolbarBuilder.EndSection();

    // Add the export button
    ToolbarBuilder.BeginSection("Export");
    {
        ToolbarBuilder.AddToolBarButton(
            FCELootPointsEditorViewportCommands::Get().ExportEventGroup,
            NAME_None,
            NSLOCTEXT("CELootPointsEditorViewport", "ExportEventGroup", "Export"),
            NSLOCTEXT("CELootPointsEditorViewport", "ExportEventGroupTooltip", "Export the event group to XML"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.SaveIcon")
        );
    }
    ToolbarBuilder.EndSection();

    return ToolbarBuilder.MakeWidget();
}
