// CELootPointsFactory.cpp
#include "CELootPoints/CELootPointsFactory.h"
#include "CELootPoints/CELootPoints.h"

UCELootPointsFactory::UCELootPointsFactory()
{
    // Set factory properties
    bCreateNew = true;
    bEditAfterNew = true;
    SupportedClass = UCELootPoints::StaticClass();
}

UObject* UCELootPointsFactory::FactoryCreateNew(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new CELootPoints asset
    UCELootPoints* NewAsset = NewObject<UCELootPoints>(InParent, InClass, InName, Flags);
    
    // Initialize with default structure
    NewAsset->InitializeDefault();
    
    return NewAsset;
}

bool UCELootPointsFactory::ShouldShowInNewMenu() const
{
    return true;
}
