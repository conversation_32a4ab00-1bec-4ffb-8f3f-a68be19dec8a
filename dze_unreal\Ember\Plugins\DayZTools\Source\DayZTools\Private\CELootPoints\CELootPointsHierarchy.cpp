// CELootPointsHierarchy.cpp
#include "CELootPoints/CELootPointsHierarchy.h"
#include "CELootPoints/CELootPointsEditorToolkit.h"
#include "Widgets/Views/STreeView.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Input/SButton.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"

void SCELootPointsHierarchy::Construct(const FArguments& InArgs, TSharedPtr<FCELootPointsEditorToolkit> InEditorToolkit)
{
    // Store editor toolkit reference
    EditorToolkit = InEditorToolkit;

    // Create the tree view
    ChildSlot
    [
        SNew(SVerticalBox)

        // Toolbar
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2)
        [
            SNew(SHorizontalBox)

            // Add Container button
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(2)
            [
                SNew(SButton)
                .Text(NSLOCTEXT("CELootPointsEditor", "AddContainer", "Add Container"))
                .ToolTipText(NSLOCTEXT("CELootPointsEditor", "AddContainerTooltip", "Add a new container to the root"))
                .OnClicked_Lambda([this]() { return OnAddContainer(EContainerType::LootFloor); })
            ]

            // Add Point button
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(2)
            [
                SNew(SButton)
                .Text(NSLOCTEXT("CELootPointsEditor", "AddPoint", "Add Point"))
                .ToolTipText(NSLOCTEXT("CELootPointsEditor", "AddPointTooltip", "Add a new point to the selected container"))
                .OnClicked_Lambda([this]() { return OnAddPoint(); })
                .IsEnabled(this, &SCELootPointsHierarchy::IsContainerSelected)
            ]

            // Remove button
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(2)
            [
                SNew(SButton)
                .Text(NSLOCTEXT("CELootPointsEditor", "Remove", "Remove"))
                .ToolTipText(NSLOCTEXT("CELootPointsEditor", "RemoveTooltip", "Remove the selected item"))
                .OnClicked_Lambda([this]() { return OnRemoveItem(); })
                .IsEnabled(this, &SCELootPointsHierarchy::CanRemoveSelectedItem)
            ]
        ]

        // Tree view
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        [
            SAssignNew(TreeView, STreeView<TSharedPtr<FCELootPointsHierarchyItem>>)
            .TreeItemsSource(&RootItems)
            .OnGenerateRow(this, &SCELootPointsHierarchy::GenerateTreeRow)
            .OnGetChildren(this, &SCELootPointsHierarchy::GetChildrenForTree)
            .OnSelectionChanged(this, &SCELootPointsHierarchy::OnSelectionChanged)
            .OnContextMenuOpening(this, &SCELootPointsHierarchy::OnContextMenuOpening)
            .SelectionMode(ESelectionMode::Single)
        ]
    ];
}

void SCELootPointsHierarchy::SetAsset(UCELootPoints* InAsset)
{
    // Store asset reference
    Asset = InAsset;

    // Rebuild hierarchy
    BuildHierarchy();
}

void SCELootPointsHierarchy::RefreshHierarchy()
{
    // Rebuild hierarchy
    BuildHierarchy();

    // Refresh tree view
    if (TreeView.IsValid())
    {
        TreeView->RequestTreeRefresh();
    }
}

void SCELootPointsHierarchy::SelectItem(int32 ItemIndex)
{
    // Find the item with the given index and its parent path
    TSharedPtr<FCELootPointsHierarchyItem> ItemToSelect;
    TArray<TSharedPtr<FCELootPointsHierarchyItem>> ParentPath;

    // Helper function to find an item by index and build parent path
    TFunction<bool(const TArray<TSharedPtr<FCELootPointsHierarchyItem>>&, TArray<TSharedPtr<FCELootPointsHierarchyItem>>&)> FindItemFunc;
    FindItemFunc = [&](const TArray<TSharedPtr<FCELootPointsHierarchyItem>>& Items, TArray<TSharedPtr<FCELootPointsHierarchyItem>>& CurrentPath) -> bool
    {
        for (const auto& Item : Items)
        {
            if (Item->ItemIndex == ItemIndex)
            {
                ItemToSelect = Item;
                return true;
            }

            // Add this item to the current path
            CurrentPath.Add(Item);

            // Check children
            if (FindItemFunc(Item->Children, CurrentPath))
            {
                // Item found in children, keep the current path
                return true;
            }

            // Item not found in this branch, remove this item from path
            CurrentPath.Pop();
        }

        return false;
    };

    // Find the item and build parent path
    TArray<TSharedPtr<FCELootPointsHierarchyItem>> TempPath;
    FindItemFunc(RootItems, TempPath);
    ParentPath = TempPath;

    // Select the item if found
    if (TreeView.IsValid())
    {
        // Clear all existing selections first
        TreeView->ClearSelection();

        if (ItemToSelect.IsValid())
        {
            UE_LOG(LogTemp, Warning, TEXT("SelectItem: Found item %d with %d parents in path"),
                ItemIndex, ParentPath.Num());

            // Expand all parent items to make the selected item visible
            for (const auto& ParentItem : ParentPath)
            {
                TreeView->SetItemExpansion(ParentItem, true);
                UE_LOG(LogTemp, Warning, TEXT("SelectItem: Expanding parent item %d"),
                    ParentItem->ItemIndex);
            }

            // Set the new selection
            TreeView->SetItemSelection(ItemToSelect, true);
            TreeView->RequestScrollIntoView(ItemToSelect);

            // Store the selected item
            SelectedItem = ItemToSelect;
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("SelectItem: Item %d not found"), ItemIndex);

            // Clear the selected item
            SelectedItem.Reset();
        }
    }
}

int32 SCELootPointsHierarchy::GetSelectedItemIndex() const
{
    return SelectedItem.IsValid() ? SelectedItem->ItemIndex : -1;
}

void SCELootPointsHierarchy::AddContainer(EContainerType ContainerType)
{
    // Add container to the asset
    if (Asset)
    {
        Asset->AddContainer(ContainerType);

        // Refresh hierarchy
        RefreshHierarchy();

        // Select the new container
        SelectItem(Asset->Items.Num() - 1);
    }
}

void SCELootPointsHierarchy::AddPoint()
{
    // Add point to the selected container
    if (Asset && SelectedItem.IsValid() && IsContainerSelected())
    {
        // Remember the container index
        int32 ContainerIndex = SelectedItem->ItemIndex;

        // Add the point
        Asset->AddPoint(ContainerIndex);

        // Get the index of the newly added point
        int32 NewPointIndex = Asset->Items.Num() - 1;

        UE_LOG(LogTemp, Warning, TEXT("AddPoint: Added new point at index %d to container %d"),
            NewPointIndex, ContainerIndex);

        // Refresh hierarchy
        RefreshHierarchy();

        if (TSharedPtr<FCELootPointsEditorToolkit> Toolkit = EditorToolkit.Pin())
        {
            // This drives the hierarchy, viewport AND details view to all update
            Toolkit->RefreshEditor();

            // Use the toolkit to select the new point to ensure proper synchronization
            Toolkit->SelectItem(NewPointIndex);
        }
        else
        {
            // Fallback if toolkit is not available
            SelectItem(NewPointIndex);
        }
    }
}

void SCELootPointsHierarchy::RemoveSelectedItem()
{
    // Remove the selected item
    if (Asset && SelectedItem.IsValid() && CanRemoveSelectedItem())
    {
        Asset->RemoveItem(SelectedItem->ItemIndex);

        // Refresh hierarchy
        RefreshHierarchy();

        if (TSharedPtr<FCELootPointsEditorToolkit> Toolkit = EditorToolkit.Pin())
        {
            // This drives the hierarchy, viewport AND details view to all update
            Toolkit->RefreshEditor();
        }

        // Select the root
        SelectItem(0);
    }
}

void SCELootPointsHierarchy::BuildHierarchy()
{
    // Save expansion state before rebuilding
    if (TreeView.IsValid())
    {
        ExpandedItems.Empty();

        // Helper function to save expansion state
        TFunction<void(const TArray<TSharedPtr<FCELootPointsHierarchyItem>>&)> SaveExpansionFunc;
        SaveExpansionFunc = [&](const TArray<TSharedPtr<FCELootPointsHierarchyItem>>& Items)
        {
            for (const auto& Item : Items)
            {
                if (Item.IsValid())
                {
                    bool bIsExpanded = TreeView->IsItemExpanded(Item);
                    ExpandedItems.Add(Item->ItemIndex, bIsExpanded);

                    // Process children
                    SaveExpansionFunc(Item->Children);
                }
            }
        };

        // Save expansion state for all items
        SaveExpansionFunc(RootItems);
    }

    // Remember the selected item index
    int32 PreviouslySelectedItemIndex = SelectedItem.IsValid() ? SelectedItem->ItemIndex : INDEX_NONE;

    // Clear existing items
    RootItems.Empty();
    SelectedItem.Reset();

    // Check if we have a valid asset
    if (!Asset)
    {
        return;
    }

    // Create a map of parent index to children
    TMap<int32, TArray<TSharedPtr<FCELootPointsHierarchyItem>>> ParentToChildren;

    // Create items for each item in the asset
    for (int32 i = 0; i < Asset->Items.Num(); ++i)
    {
        const FCELootPointsItem& Item = Asset->Items[i];

        // Create a hierarchy item
        TSharedPtr<FCELootPointsHierarchyItem> HierarchyItem = MakeShareable(new FCELootPointsHierarchyItem(i, Item.GetDisplayName()));

        // Add to parent's children
        ParentToChildren.FindOrAdd(Item.ParentIndex).Add(HierarchyItem);
    }

    // Add root items (parent index -1)
    if (ParentToChildren.Contains(-1))
    {
        RootItems = ParentToChildren[-1];
    }

    // Add children to each item
    TFunction<void(const TArray<TSharedPtr<FCELootPointsHierarchyItem>>&)> AddChildrenFunc;
    AddChildrenFunc = [&](const TArray<TSharedPtr<FCELootPointsHierarchyItem>>& Items)
    {
        for (const auto& Item : Items)
        {
            if (ParentToChildren.Contains(Item->ItemIndex))
            {
                Item->Children = ParentToChildren[Item->ItemIndex];
            }

            AddChildrenFunc(Item->Children);
        }
    };

    // Add children to root items
    AddChildrenFunc(RootItems);

    // Restore expansion state if tree view is valid
    if (TreeView.IsValid())
    {
        // Helper function to restore expansion state
        TFunction<void(const TArray<TSharedPtr<FCELootPointsHierarchyItem>>&)> RestoreExpansionFunc;
        RestoreExpansionFunc = [&](const TArray<TSharedPtr<FCELootPointsHierarchyItem>>& Items)
        {
            for (const auto& Item : Items)
            {
                if (Item.IsValid())
                {
                    // Check if we have saved expansion state for this item
                    if (ExpandedItems.Contains(Item->ItemIndex))
                    {
                        bool bShouldBeExpanded = ExpandedItems[Item->ItemIndex];
                        TreeView->SetItemExpansion(Item, bShouldBeExpanded);
                    }

                    // Process children
                    RestoreExpansionFunc(Item->Children);
                }
            }
        };

        // Restore expansion state for all items
        RestoreExpansionFunc(RootItems);
    }

    // Restore selection if needed
    if (PreviouslySelectedItemIndex != INDEX_NONE)
    {
        SelectItem(PreviouslySelectedItemIndex);
    }
}

TSharedRef<ITableRow> SCELootPointsHierarchy::GenerateTreeRow(TSharedPtr<FCELootPointsHierarchyItem> Item, const TSharedRef<STableViewBase>& OwnerTable)
{
    // Create a row for the item
    return SNew(STableRow<TSharedPtr<FCELootPointsHierarchyItem>>, OwnerTable)
        [
            SNew(STextBlock)
            .Text(FText::FromString(Item->DisplayName))
        ];
}

void SCELootPointsHierarchy::GetChildrenForTree(TSharedPtr<FCELootPointsHierarchyItem> Item, TArray<TSharedPtr<FCELootPointsHierarchyItem>>& OutChildren)
{
    if (Item.IsValid())
    {
        OutChildren = Item->Children;
    }
}

void SCELootPointsHierarchy::OnSelectionChanged(TSharedPtr<FCELootPointsHierarchyItem> Item, ESelectInfo::Type SelectInfo)
{
    // Store selected item regardless of selection type
    SelectedItem = Item;

    UE_LOG(LogTemp, Warning, TEXT("OnSelectionChanged: Item=%s, SelectInfo=%d"),
        Item.IsValid() ? *FString::FromInt(Item->ItemIndex) : TEXT("None"),
        (int32)SelectInfo);

    // Notify editor toolkit of the selection change
    if (EditorToolkit.IsValid())
    {
        if (Item.IsValid())
        {
            // Always use the toolkit to select the item to ensure proper synchronization
            EditorToolkit.Pin()->SelectItem(Item->ItemIndex);
        }
        else
        {
            // Deselect
            EditorToolkit.Pin()->SelectItem(INDEX_NONE);
        }
    }
}

TSharedPtr<SWidget> SCELootPointsHierarchy::OnContextMenuOpening()
{
    // Create context menu
    FMenuBuilder MenuBuilder(true, nullptr);

    // Add container submenu
    MenuBuilder.AddSubMenu(
        NSLOCTEXT("CELootPointsEditor", "AddContainer", "Add Container"),
        NSLOCTEXT("CELootPointsEditor", "AddContainerTooltip", "Add a new container to the root"),
        FNewMenuDelegate::CreateLambda([this](FMenuBuilder& SubMenuBuilder)
        {
            // Add container types
            SubMenuBuilder.AddMenuEntry(
                NSLOCTEXT("CELootPointsEditor", "AddLootFloor", "lootFloor"),
                NSLOCTEXT("CELootPointsEditor", "AddLootFloorTooltip", "Add a lootFloor container"),
                FSlateIcon(),
                FUIAction(FExecuteAction::CreateLambda([this]() { OnAddContainer(EContainerType::LootFloor); }))
            );

            SubMenuBuilder.AddMenuEntry(
                NSLOCTEXT("CELootPointsEditor", "AddLootShelves", "lootshelves"),
                NSLOCTEXT("CELootPointsEditor", "AddLootShelvesTooltip", "Add a lootshelves container"),
                FSlateIcon(),
                FUIAction(FExecuteAction::CreateLambda([this]() { OnAddContainer(EContainerType::LootShelves); }))
            );

            SubMenuBuilder.AddMenuEntry(
                NSLOCTEXT("CELootPointsEditor", "AddLootWeapons", "lootweapons"),
                NSLOCTEXT("CELootPointsEditor", "AddLootWeaponsTooltip", "Add a lootweapons container"),
                FSlateIcon(),
                FUIAction(FExecuteAction::CreateLambda([this]() { OnAddContainer(EContainerType::LootWeapons); }))
            );
        })
    );

    // Add point (only if a container is selected)
    MenuBuilder.AddMenuEntry(
        NSLOCTEXT("CELootPointsEditor", "AddPoint", "Add Point"),
        NSLOCTEXT("CELootPointsEditor", "AddPointTooltip", "Add a new point to the selected container"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateLambda([this]() { OnAddPoint(); }),
            FCanExecuteAction::CreateSP(this, &SCELootPointsHierarchy::IsContainerSelected)
        )
    );

    // Remove item (only if not root)
    MenuBuilder.AddMenuEntry(
        NSLOCTEXT("CELootPointsEditor", "Remove", "Remove"),
        NSLOCTEXT("CELootPointsEditor", "RemoveTooltip", "Remove the selected item"),
        FSlateIcon(),
        FUIAction(
            FExecuteAction::CreateLambda([this]() { OnRemoveItem(); }),
            FCanExecuteAction::CreateSP(this, &SCELootPointsHierarchy::CanRemoveSelectedItem)
        )
    );

    return MenuBuilder.MakeWidget();
}

FReply SCELootPointsHierarchy::OnAddContainer(EContainerType ContainerType)
{
    // Add container
    AddContainer(ContainerType);
    return FReply::Handled();
}

FReply SCELootPointsHierarchy::OnAddPoint()
{
    // Add point
    AddPoint();
    return FReply::Handled();
}

FReply SCELootPointsHierarchy::OnRemoveItem()
{
    // Remove item
    RemoveSelectedItem();
    return FReply::Handled();
}

bool SCELootPointsHierarchy::IsContainerSelected() const
{
    // Check if a container is selected
    if (Asset && SelectedItem.IsValid() && SelectedItem->ItemIndex >= 0 && SelectedItem->ItemIndex < Asset->Items.Num())
    {
        return Asset->Items[SelectedItem->ItemIndex].ItemType == ECELootPointsItemType::Container;
    }

    return false;
}

bool SCELootPointsHierarchy::CanRemoveSelectedItem() const
{
    // Check if the selected item can be removed (not root)
    return SelectedItem.IsValid() && SelectedItem->ItemIndex > 0;
}
