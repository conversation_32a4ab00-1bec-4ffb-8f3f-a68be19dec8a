// CELootPointsItemProxy.cpp
#include "CELootPoints/CELootPointsItemProxy.h"

void UCELootPointsItemProxy::Initialize(UCELootPoints* InAsset, int32 InItemIndex)
{
    // Store references
    LootPointsAsset = InAsset;
    ItemIndex = InItemIndex;

    // Reset flags
    bIsRoot = false;
    bIsContainer = false;
    bIsPoint = false;

    // Validate index
    if (!LootPointsAsset || ItemIndex < 0 || ItemIndex >= LootPointsAsset->Items.Num())
    {
        return;
    }

    // Get the item
    const FCELootPointsItem& Item = LootPointsAsset->Items[ItemIndex];

    // Set flags based on item type
    switch (Item.ItemType)
    {
    case ECELootPointsItemType::Root:
        bIsRoot = true;

        // Copy root data
        Usage = Item.RootData.Usage;
        Type = Item.RootData.Type;
        RootLootMax = Item.RootData.LootMax;
        break;

    case ECELootPointsItemType::Container:
        bIsContainer = true;

        // Copy container data
        ContainerType = Item.ContainerData.ContainerType;
        LootMax = Item.ContainerData.LootMax;
        Category = Item.ContainerData.Category;
        Tag = Item.ContainerData.Tag;
        break;

    case ECELootPointsItemType::Point:
        bIsPoint = true;

        // Copy point data
        Location = FVector(Item.PointData.X, Item.PointData.Y, Item.PointData.Z);

        // Copy scale data
        ScaleXY = FMath::Max(Item.PointData.ScaleXY, 0.0001f);
        ScaleZ = FMath::Max(Item.PointData.ScaleZ, 0.0001f);

        // Copy force spawn flag
        bForceSpawn = Item.PointData.bForceSpawn;

        // If scale is not set in the asset (older assets), use default values
        if (FMath::IsNearlyZero(ScaleXY))
        {
            ScaleXY = 1.0f;
        }
        if (FMath::IsNearlyZero(ScaleZ))
        {
            ScaleZ = 1.0f;
        }
        break;
    }
}

void UCELootPointsItemProxy::ApplyChanges()
{
    // Validate
    if (!LootPointsAsset || ItemIndex < 0 || ItemIndex >= LootPointsAsset->Items.Num())
    {
        return;
    }

    // Get the item
    FCELootPointsItem& Item = LootPointsAsset->Items[ItemIndex];

    // Apply changes based on item type
    switch (Item.ItemType)
    {
    case ECELootPointsItemType::Root:
        // Apply root data
        Item.RootData.Usage = Usage;
        Item.RootData.Type = Type;
        Item.RootData.LootMax = RootLootMax;
        break;

    case ECELootPointsItemType::Container:
        // Apply container data
        Item.ContainerData.ContainerType = ContainerType;
        Item.ContainerData.LootMax = LootMax;
        Item.ContainerData.Category = Category;
        Item.ContainerData.Tag = Tag;

        // Update container name
        Item.ContainerData.UpdateName();
        break;

    case ECELootPointsItemType::Point:
        // Apply point data
        Item.PointData.X = Location.X;
        Item.PointData.Y = Location.Y;
        Item.PointData.Z = Location.Z;

        // Apply scale data
        Item.PointData.ScaleXY = FMath::Max(ScaleXY, 0.0001f);
        Item.PointData.ScaleZ = FMath::Max(ScaleZ, 0.0001f);

        // Apply force spawn flag
        Item.PointData.bForceSpawn = bForceSpawn;
        break;
    }

    // Mark asset as modified
    LootPointsAsset->Modify();
}

#if WITH_EDITOR
void UCELootPointsItemProxy::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

    // Apply changes to the asset
    ApplyChanges();

    // Check if the Type property changed for a root item
    if (PropertyChangedEvent.Property && bIsRoot &&
        PropertyChangedEvent.Property->GetFName() == GET_MEMBER_NAME_CHECKED(UCELootPointsItemProxy, Type))
    {
        // We don't need to do anything here - the editor toolkit will handle this in its OnPropertyChanged method
        UE_LOG(LogTemp, Display, TEXT("Type property changed for root item"));
    }

    // Check if scale properties changed for a point item
    if (PropertyChangedEvent.Property && bIsPoint)
    {
        FName PropertyName = PropertyChangedEvent.Property->GetFName();

        if (PropertyName == GET_MEMBER_NAME_CHECKED(UCELootPointsItemProxy, ScaleXY) ||
            PropertyName == GET_MEMBER_NAME_CHECKED(UCELootPointsItemProxy, ScaleZ))
        {
            // Notify the editor toolkit to update the viewport
            UE_LOG(LogTemp, Display, TEXT("Scale property changed for point item: %s = %f, %s = %f"),
                TEXT("ScaleXY"), ScaleXY, TEXT("ScaleZ"), ScaleZ);

            // The editor toolkit will handle this in its OnPropertyChanged method
        }
    }
}
#endif
