// CELootPointsItemProxyCustomization.cpp
#include "CELootPoints/CELootPointsItemProxyCustomization.h"
#include "CELootPoints/CELootPointsItemProxy.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"

TSharedRef<IDetailCustomization> FCELootPointsItemProxyCustomization::MakeInstance()
{
    return MakeShareable(new FCELootPointsItemProxyCustomization);
}

void FCELootPointsItemProxyCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // Get the currently selected objects
    TArray<TWeakObjectPtr<UObject>> SelectedObjects;
    DetailBuilder.GetObjectsBeingCustomized(SelectedObjects);

    if (SelectedObjects.Num() != 1)
    {
        return;
    }

    UCELootPointsItemProxy* ItemProxy = Cast<UCELootPointsItemProxy>(SelectedObjects[0].Get());
    if (!ItemProxy)
    {
        return;
    }

    // Set the sort order for categories based on item type
    if (ItemProxy->bIsRoot)
    {
        // For root items, prioritize Root Settings
        IDetailCategoryBuilder& RootCategory = DetailBuilder.EditCategory("Root Settings");
        RootCategory.SetSortOrder(0);
    }
    else if (ItemProxy->bIsContainer)
    {
        // For container items, prioritize Container Settings
        IDetailCategoryBuilder& ContainerCategory = DetailBuilder.EditCategory("Container Settings");
        ContainerCategory.SetSortOrder(0);
    }
    else if (ItemProxy->bIsPoint)
    {
        // For point items, prioritize Transform
        IDetailCategoryBuilder& TransformCategory = DetailBuilder.EditCategory("Transform");
        TransformCategory.SetSortOrder(0);

        // Set Point Settings category to appear after Transform
        IDetailCategoryBuilder& PointSettingsCategory = DetailBuilder.EditCategory("Point Settings");
        PointSettingsCategory.SetSortOrder(1);
    }
}
