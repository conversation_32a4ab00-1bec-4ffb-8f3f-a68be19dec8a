// CELootPointsPreviewScene.cpp
#include "CELootPoints/CELootPointsPreviewScene.h"
#include "CELootPoints/CELootPoint.h"
#include "CELootPoints/CELootPointsComponentOwner.h"
#include "P3DBlueprint.h"
#include "Engine/StreamableManager.h"

// Engine includes
#include "Engine/World.h"
#include "Engine/StaticMeshActor.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "EngineUtils.h"
#include "Engine/Selection.h"
#include "Editor.h"
#include "EditorModeManager.h"
#include "EditorViewportClient.h"
#include "ScopedTransaction.h"
#include "ComponentReregisterContext.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/Engine.h"
#include "GameFramework/WorldSettings.h"
#include "Components/BillboardComponent.h"
#include "Components/ArrowComponent.h"
#include "Components/BoxComponent.h"
#include "Components/SphereComponent.h"
#include "Components/CapsuleComponent.h"
#include "Components/PointLightComponent.h"
#include "Components/SpotLightComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/SkeletalMeshComponent.h"
#include "Components/ChildActorComponent.h"
#include "Components/SceneComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/ShapeComponent.h"
#include "Components/LightComponent.h"
#include "Components/LightComponentBase.h"
#include "Components/MeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/DecalComponent.h"
#include "Components/AudioComponent.h"
#include "Components/SplineMeshComponent.h"
#include "Components/SplineComponent.h"
#include "Components/ModelComponent.h"
#include "Components/BrushComponent.h"
#include "Components/DrawSphereComponent.h"
#include "Components/TextRenderComponent.h"
#include "Components/VectorFieldComponent.h"
#include "Components/WindDirectionalSourceComponent.h"
#include "Components/TimelineComponent.h"
#include "Components/ReflectionCaptureComponent.h"
#include "Components/SceneCaptureComponent.h"
#include "Components/SceneCaptureComponent2D.h"
#include "Components/SceneCaptureComponentCube.h"
#include "Components/PlanarReflectionComponent.h"
#include "Components/BoxReflectionCaptureComponent.h"
#include "Components/SphereReflectionCaptureComponent.h"
#include "Components/PlaneReflectionCaptureComponent.h"
#include "Components/SkyLightComponent.h"
#include "Components/LightmassPortalComponent.h"
#include "Components/ExponentialHeightFogComponent.h"
#include "Components/PostProcessComponent.h"
#include "Components/BoundsCopyComponent.h"

#define LOCTEXT_NAMESPACE "CELootPointsEditor"

FCELootPointsPreviewScene::FCELootPointsPreviewScene(const ConstructionValues& CVS)
    : FAdvancedPreviewScene(CVS)
    , Asset(nullptr)
    , SelectedItemIndex(INDEX_NONE)
    , RootOwnerActor(nullptr)
    , FloorMeshComponent(nullptr)
    , P3DModelComponent(nullptr)
    , PreviewWorld(nullptr)
    , SkyLight(nullptr)
    , DirectionalLight(nullptr)
{
    UE_LOG(LogTemp, Warning, TEXT("PreviewScene constructor - Start"));

    // Get the world from the preview scene
    UWorld* World = GetWorld();
    if (World)
    {
        UE_LOG(LogTemp, Display, TEXT("PreviewScene constructor - Got valid world: %s"), *World->GetName());
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("PreviewScene constructor - Failed to get world. Make sure to set CVS.SetEditor(false) when creating the preview scene."));
        // We'll continue anyway, but the world will be created later when needed
    }

    // Create a simple sky light
    SkyLight = NewObject<USkyLightComponent>();
    if (SkyLight)
    {
        SkyLight->Intensity = 1.0f;
        SkyLight->SetMobility(EComponentMobility::Movable);
        AddComponent(SkyLight, FTransform::Identity);
    }

    // Create a directional light
    DirectionalLight = NewObject<UDirectionalLightComponent>();
    if (DirectionalLight)
    {
        DirectionalLight->Intensity = 1.0f;
        DirectionalLight->SetMobility(EComponentMobility::Movable);
        AddComponent(DirectionalLight, FTransform::Identity);
    }

    // Create the root owner actor - defer this until we actually need it
    // We'll create it on-demand in CreateChildActorComponent

    // Setup the floor mesh
    SetupFloorMesh();

    // Set default floor visibility
    ToggleGroundPlane(true);

    UE_LOG(LogTemp, Warning, TEXT("PreviewScene constructor - Complete"));
}

FCELootPointsPreviewScene::~FCELootPointsPreviewScene()
{
    UE_LOG(LogTemp, Warning, TEXT("PreviewScene destructor - Start"));

    // Ensure child actors are cleaned up
    if (GEngine && !GExitPurge)  // Check if engine is still valid and we're not in shutdown
    {
        // Clear all actors
        ClearLootPointActors();

        // Destroy the root owner actor
        if (RootOwnerActor && IsValid(RootOwnerActor))
        {
            UE_LOG(LogTemp, Display, TEXT("Destroying root owner actor: %s"), *RootOwnerActor->GetName());
            RootOwnerActor->Destroy();
            RootOwnerActor = nullptr;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("PreviewScene destructor - Complete"));
}

void FCELootPointsPreviewScene::SetAsset(UCELootPoints* InAsset)
{
    UE_LOG(LogTemp, Warning, TEXT("SetAsset: %s"), InAsset ? *InAsset->GetName() : TEXT("NULL"));

    // Store asset reference
    Asset = InAsset;

    // Clear the item scales map to ensure we don't use stale data
    ItemScales.Empty();

    // Clear existing actors
    ClearLootPointActors();

    // Create new actors if we have a valid asset
    if (Asset)
    {
        UE_LOG(LogTemp, Warning, TEXT("SetAsset: Creating loot point actors for %s"), *Asset->GetName());
        CreateLootPointActors();
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("SetAsset: No valid asset to create actors for"));
    }
}

void FCELootPointsPreviewScene::RefreshScene()
{
    UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Start"));

    // Check if we have a valid world
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("RefreshScene: No valid world"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Using world %s"), *World->GetName());
    }

    // Store the current P3D model component
    UChildActorComponent* OldP3DModelComponent = P3DModelComponent;

    // Clear existing loot point actors but keep the P3D model component
    ClearLootPointActors();

    // Create new actors
    if (Asset)
    {
        UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Asset is valid (%s), creating loot point actors"), *Asset->GetName());

        // First, update the P3D model from the root item's Type property
        // This ensures the P3D model is updated before creating loot point actors
        bool bP3DModelUpdated = UpdateP3DModelFromRootType();

        if (bP3DModelUpdated)
        {
            UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Successfully updated P3D model"));
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Failed to update P3D model, continuing with loot point creation"));

            // If we failed to update the P3D model but had a valid one before, restore it
            if (OldP3DModelComponent && IsValid(OldP3DModelComponent) && !P3DModelComponent)
            {
                UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Restoring previous P3D model component"));
                P3DModelComponent = OldP3DModelComponent;
            }
        }

        // Then create the loot point actors
        CreateLootPointActors();

        // Make sure the P3D model is visible
        if (P3DModelComponent && IsValid(P3DModelComponent))
        {
            UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Ensuring P3D model component is visible"));

            P3DModelComponent->SetVisibility(true);
            P3DModelComponent->SetHiddenInGame(false);
            //not here


            // Force the component to create its child actor immediately if it hasn't already
            if (!P3DModelComponent->GetChildActor())
            {
                UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Forcing creation of child actor"));
                P3DModelComponent->CreateChildActor();
            }

            // Make sure the child actor is visible
            AActor* ChildActor = P3DModelComponent->GetChildActor();
            if (ChildActor && IsValid(ChildActor))
            {
                UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Ensuring child actor %s is visible"), *ChildActor->GetName());

                ChildActor->SetActorHiddenInGame(false);
                ChildActor->SetActorEnableCollision(true);
                ChildActor->SetActorTickEnabled(true);
                //ChildActor->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

                // Make sure all components of the child actor are visible
                TArray<USceneComponent*> ActorSceneComponents;
                ChildActor->GetComponents<USceneComponent>(ActorSceneComponents);

                UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Child actor has %d components"), ActorSceneComponents.Num());

                for (USceneComponent* Component : ActorSceneComponents)
                {
                    if (Component)
                    {
                        Component->SetVisibility(true);
                        Component->SetHiddenInGame(false);
                        UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Set component %s visibility to true"), *Component->GetName());


                        // Check if this component is a ChildActorComponent that might contain another P3D asset
                        UChildActorComponent* ChildActorComp = Cast<UChildActorComponent>(Component);
                        if (ChildActorComp && IsValid(ChildActorComp))
                        {
                            UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Found child actor component %s in the P3D asset"),
                                *ChildActorComp->GetName());

                            // Make sure the child actor component is visible
                            ChildActorComp->SetVisibility(true);
                            ChildActorComp->SetHiddenInGame(false);
                           // ChildActorComp->SetActorTickEnabled(true);
                           // ChildActorComp->SetActorEnableCollision(true);
                           // ChildActorComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

                            // Force the component to create its child actor immediately if it hasn't already
                            if (!ChildActorComp->GetChildActor())
                            {
                                UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Forcing creation of nested child actor"));
                                ChildActorComp->CreateChildActor();
                            }

                            // Make sure the nested child actor is visible
                            AActor* NestedChildActor = ChildActorComp->GetChildActor();
                            if (NestedChildActor && IsValid(NestedChildActor))
                            {
                                UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Making nested child actor %s visible"),
                                    *NestedChildActor->GetName());

                                NestedChildActor->SetActorHiddenInGame(false);
                                NestedChildActor->SetActorEnableCollision(true);
                                NestedChildActor->SetActorTickEnabled(true);
                                NestedChildActor->SetActorEnableCollision(true);
                                //NestedChildActor->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

                                // Make sure all components of the nested child actor are visible
                                TArray<USceneComponent*> NestedActorComponents;
                                NestedChildActor->GetComponents<USceneComponent>(NestedActorComponents);

                                for (USceneComponent* NestedComponent : NestedActorComponents)
                                {
                                    if (NestedComponent)
                                    {
                                        NestedComponent->SetVisibility(true);
                                        NestedComponent->SetHiddenInGame(false);

                                        // Recursively handle any deeper nested child actor components
                                        UChildActorComponent* DeepNestedChildActorComp = Cast<UChildActorComponent>(NestedComponent);
                                        if (DeepNestedChildActorComp && IsValid(DeepNestedChildActorComp))
                                        {
                                            UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Found deep nested child actor component %s"),
                                                *DeepNestedChildActorComp->GetName());

                                            DeepNestedChildActorComp->SetVisibility(true);
                                            DeepNestedChildActorComp->SetHiddenInGame(false);
                                            //DeepNestedChildActorComp->SetActorEnableCollision(true);
                                            //DeepNestedChildActorComp->SetCollisionEnabled(ECollisionEnabled::QueryAndPhysics);

                                            if (!DeepNestedChildActorComp->GetChildActor())
                                            {
                                                DeepNestedChildActorComp->CreateChildActor();
                                            }

                                            AActor* DeepNestedActor = DeepNestedChildActorComp->GetChildActor();
                                            if (DeepNestedActor && IsValid(DeepNestedActor))
                                            {
                                                DeepNestedActor->SetActorHiddenInGame(false);
                                                DeepNestedActor->SetActorEnableCollision(true);
                                                DeepNestedActor->SetActorTickEnabled(true);

                                                TArray<USceneComponent*> DeepNestedComponents;
                                                DeepNestedActor->GetComponents<USceneComponent>(DeepNestedComponents);

                                                for (USceneComponent* DeepComponent : DeepNestedComponents)
                                                {
                                                    if (DeepComponent)
                                                    {
                                                        DeepComponent->SetVisibility(true);
                                                        DeepComponent->SetHiddenInGame(false);
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("RefreshScene: P3D model component has no valid child actor"));
            }
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("RefreshScene: No valid P3D model component after update"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("RefreshScene: Asset is null, cannot create loot point actors"));
    }

    UE_LOG(LogTemp, Warning, TEXT("RefreshScene: Complete"));
}

void FCELootPointsPreviewScene::SelectItem(int32 ItemIndex)
{
    UE_LOG(LogTemp, Warning, TEXT("SelectItem: %d"), ItemIndex);

    // Only update if selection changed
    if (SelectedItemIndex == ItemIndex)
    {
        return;
    }

    // Store selected item index
    SelectedItemIndex = ItemIndex;

    // Update actor selection state
    UpdateActorSelectionState();
}

void FCELootPointsPreviewScene::ToggleGroundPlane(bool bVisible)
{
    UE_LOG(LogTemp, Warning, TEXT("ToggleGroundPlane: %d"), bVisible);

    // Set floor visibility
    SetFloorVisibility(bVisible);

    // Update floor mesh component visibility
    if (FloorMeshComponent)
    {
        FloorMeshComponent->SetVisibility(bVisible);
    }
}

bool FCELootPointsPreviewScene::IsGroundPlaneVisible() const
{
    // Return floor visibility
    return GetFloorVisibility();
}

void FCELootPointsPreviewScene::HandleTransformUpdate(ACELootPoint* LootPoint, const FTransform& NewTransform)
{
    if (!LootPoint || !Asset) return;

    UE_LOG(LogTemp, Warning, TEXT("HandleTransformUpdate: Actor=%s, Transform=%s"),
        *LootPoint->GetName(), *NewTransform.ToString());

    // Find the item index for this loot point
    int32 ItemIndex = LootPoint->GetItemIndex();

    // If we found the item, update its transform in the asset
    if (ItemIndex >= 0 && ItemIndex < Asset->Items.Num())
    {
        // Get the item
        FCELootPointsItem& Item = Asset->Items[ItemIndex];

        // Only update point items
        if (Item.ItemType == ECELootPointsItemType::Point)
        {
            // Start a transaction
            FScopedTransaction Transaction(LOCTEXT("UpdateLootPointTransform", "Update Loot Point Transform"));
            Asset->Modify();

            // Update point data
            Item.PointData.X = NewTransform.GetLocation().X;
            Item.PointData.Y = NewTransform.GetLocation().Y;
            Item.PointData.Z = NewTransform.GetLocation().Z;

            UE_LOG(LogTemp, Warning, TEXT("Updated item %d position to (%f, %f, %f)"),
                ItemIndex, Item.PointData.X, Item.PointData.Y, Item.PointData.Z);
        }
    }
}

ACELootPoint* FCELootPointsPreviewScene::GetLootPointActor(int32 ItemIndex) const
{
    // Safety check for the item index
    if (ItemIndex < 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetLootPointActor: Invalid item index %d"), ItemIndex);
        return nullptr;
    }

    // Find the loot point actor for the given item index
    for (ACELootPoint* LootPoint : LootPointActors)
    {
        if (LootPoint && IsValid(LootPoint) && LootPoint->GetItemIndex() == ItemIndex)
        {
            return LootPoint;
        }
    }

    return nullptr;
}

void FCELootPointsPreviewScene::CreateLootPointActors()
{
    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Start"));

    // Check if we have a valid asset
    if (!Asset)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLootPointActors: No asset"));
        return;
    }

    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Asset is %s"), *Asset->GetName());

    // Check if we have a valid world
    if (!PreviewWorld)
    {
        PreviewWorld = GetWorld();
        if (!PreviewWorld)
        {
            UE_LOG(LogTemp, Error, TEXT("CreateLootPointActors: No valid world"));
            return;
        }
    }

    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: World is %s"), *PreviewWorld->GetName());

    // Clear existing actors and components
    ClearLootPointActors();

    // Safety check for the asset items
    if (!Asset->Items.Num())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateLootPointActors: Asset has no items"));
        return;
    }

    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Asset has %d items"), Asset->Items.Num());

    // Note: We no longer update the P3D model here
    // The P3D model is now updated in the RefreshScene method before calling CreateLootPointActors
    // This prevents duplicate updates and ensures the P3D model is properly created

    // Count how many point items we have
    int32 PointItemCount = 0;
    for (int32 i = 0; i < Asset->Items.Num(); ++i)
    {
        if (Asset->Items[i].ItemType == ECELootPointsItemType::Point)
        {
            PointItemCount++;
        }
    }

    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Found %d point items"), PointItemCount);

    // Pre-allocate the ChildActorComponents array to match the number of items
    // This ensures we have the correct number of slots for all items
    ChildActorComponents.SetNum(Asset->Items.Num());

    // Initialize all entries to nullptr
    for (int32 i = 0; i < ChildActorComponents.Num(); ++i)
    {
        ChildActorComponents[i] = nullptr;
    }

    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Pre-allocated %d slots in ChildActorComponents array"), ChildActorComponents.Num());

    // Create loot point actors for each point item
    for (int32 i = 0; i < Asset->Items.Num(); ++i)
    {
        // Safety check for the asset
        if (!IsValid(Asset))
        {
            UE_LOG(LogTemp, Error, TEXT("CreateLootPointActors: Asset became invalid during processing"));
            break;
        }

        // Safety check for the item index
        if (!Asset->Items.IsValidIndex(i))
        {
            UE_LOG(LogTemp, Error, TEXT("CreateLootPointActors: Invalid item index %d"), i);
            continue;
        }

        const FCELootPointsItem& Item = Asset->Items[i];

        UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Processing item %d, type %d"),
            i, (int32)Item.ItemType);

        // Only create actors for point items
        if (Item.ItemType == ECELootPointsItemType::Point)
        {
            // Set transform
            FTransform Transform;
            Transform.SetLocation(FVector(Item.PointData.X, Item.PointData.Y, Item.PointData.Z));

            // Use scale from the asset data
            FVector Scale(Item.PointData.ScaleXY, Item.PointData.ScaleXY, Item.PointData.ScaleZ);

            // If scale is not set in the asset (older assets), use default or stored scale
            if (FMath::IsNearlyZero(Item.PointData.ScaleXY))
            {
                // Check if we have stored scale for this item in memory
                if (ItemScales.Contains(i))
                {
                    Scale = ItemScales[i];
                    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Using stored scale for item %d: %s"),
                        i, *Scale.ToString());
                }
                else
                {
                    // Use default scale
                    Scale = FVector(1.0f, 1.0f, 1.0f);
                }

                // Update the asset with the scale
                FCELootPointsItem& MutableItem = Asset->Items[i];
                MutableItem.PointData.ScaleXY = Scale.X;
                MutableItem.PointData.ScaleZ = Scale.Z;
            }

            // Store the scale in our runtime cache as well
            ItemScales.FindOrAdd(i) = Scale;

            Transform.SetScale3D(Scale);

            UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Creating loot point actor at (%f, %f, %f) with scale %s"),
                Item.PointData.X, Item.PointData.Y, Item.PointData.Z, *Scale.ToString());

            // Create a child actor component
            UChildActorComponent* ChildActorComponent = CreateChildActorComponent(ACELootPoint::StaticClass(), Transform);

            if (ChildActorComponent && IsValid(ChildActorComponent))
            {
                UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Created child actor component %s"),
                    *ChildActorComponent->GetName());

                // Store in the pre-allocated array at the correct index
                ChildActorComponents[i] = ChildActorComponent;

                // Get the child actor
                ACELootPoint* LootPoint = Cast<ACELootPoint>(ChildActorComponent->GetChildActor());

                if (LootPoint && IsValid(LootPoint))
                {
                    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Got child actor %s"),
                        *LootPoint->GetName());

                    // Set item index
                    LootPoint->SetItemIndex(i);

                    // Set selection state
                    LootPoint->SetSelected(i == SelectedItemIndex);

                    // Apply scale directly to the actor if we have stored scale
                    if (ItemScales.Contains(i))
                    {
                        float UniformXY = ItemScales[i].X; // X and Y should be the same
                        LootPoint->SetUniformXYScale(UniformXY);
                        UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Applied scale %f to actor %s"),
                            UniformXY, *LootPoint->GetName());
                    }

                    // Store reference
                    LootPointActors.Add(LootPoint);

                    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Created loot point actor %d at (%f, %f, %f)"),
                        i, Item.PointData.X, Item.PointData.Y, Item.PointData.Z);
                }
                else
                {
                    UE_LOG(LogTemp, Error, TEXT("CreateLootPointActors: Failed to get valid child actor for component"));
                }
            }
            else
            {
                UE_LOG(LogTemp, Error, TEXT("CreateLootPointActors: Failed to create valid child actor component"));
                // The array slot is already initialized to nullptr
            }
        }
        // For non-point items, we already have nullptr in the pre-allocated array
    }

    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: Created %d actors"), LootPointActors.Num());

    // Log the state of the ChildActorComponents array
    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: ChildActorComponents array has %d entries"), ChildActorComponents.Num());

    // Count valid components
    int32 ValidComponentCount = 0;
    for (int32 i = 0; i < ChildActorComponents.Num(); ++i)
    {
        if (ChildActorComponents[i] && IsValid(ChildActorComponents[i]))
        {
            ValidComponentCount++;
            UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: ChildActorComponents[%d] is valid"), i);
        }
        else
        {
            UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: ChildActorComponents[%d] is null or invalid"), i);
        }
    }

    UE_LOG(LogTemp, Display, TEXT("CreateLootPointActors: ChildActorComponents array has %d valid entries"), ValidComponentCount);
}

void FCELootPointsPreviewScene::ClearLootPointActors()
{
    UE_LOG(LogTemp, Warning, TEXT("ClearLootPointActors: Start"));

    // Clear the loot point actors array
    LootPointActors.Empty();

    // Note: We no longer clear the P3D model component here
    // This ensures the P3D model remains visible when refreshing the scene
    // The P3D model component will be updated separately in UpdateP3DModelFromRootType

    // If we have a valid root owner actor, remove all child components except the P3D model component and its children
    if (RootOwnerActor && IsValid(RootOwnerActor) && RootOwnerActor->GetRootComponent())
    {
        UE_LOG(LogTemp, Warning, TEXT("ClearLootPointActors: Removing components from root owner actor: %s"), *RootOwnerActor->GetName());

        // Get all child components
        TArray<USceneComponent*> ChildComponents;
        RootOwnerActor->GetRootComponent()->GetChildrenComponents(true, ChildComponents);

        UE_LOG(LogTemp, Warning, TEXT("ClearLootPointActors: Found %d child components"), ChildComponents.Num());

        // Create a set of component names that are part of the P3D model
        TSet<FName> P3DComponentNames;

        // First, add the main P3D model component to the set
        if (P3DModelComponent && IsValid(P3DModelComponent))
        {
            P3DComponentNames.Add(P3DModelComponent->GetFName());

            // Get the child actor of the P3D model component
            AActor* P3DActor = P3DModelComponent->GetChildActor();
            if (P3DActor && IsValid(P3DActor))
            {
                // Get all child components of the P3D actor
                TArray<USceneComponent*> P3DChildComponents;
                P3DActor->GetComponents<USceneComponent>(P3DChildComponents);

                // Add all child components to the set
                for (USceneComponent* P3DChildComponent : P3DChildComponents)
                {
                    if (P3DChildComponent && IsValid(P3DChildComponent))
                    {
                        UChildActorComponent* ChildActorComp = Cast<UChildActorComponent>(P3DChildComponent);
                        if (ChildActorComp && IsValid(ChildActorComp))
                        {
                            // Add the child actor component name to the set
                            P3DComponentNames.Add(ChildActorComp->GetFName());
                        }
                    }
                }
            }
        }

        // Remove each child component except the P3D model component and its children
        for (USceneComponent* Component : ChildComponents)
        {
            if (Component && IsValid(Component))
            {
                UChildActorComponent* ChildActorComponent = Cast<UChildActorComponent>(Component);
                if (ChildActorComponent && IsValid(ChildActorComponent))
                {
                    // Skip the P3D model component
                    if (ChildActorComponent == P3DModelComponent)
                    {
                        UE_LOG(LogTemp, Warning, TEXT("ClearLootPointActors: Skipping P3D model component: %s"), *ChildActorComponent->GetName());
                        continue;
                    }

                    // Skip components that are part of the P3D model
                    if (P3DComponentNames.Contains(ChildActorComponent->GetFName()))
                    {
                        UE_LOG(LogTemp, Warning, TEXT("ClearLootPointActors: Skipping P3D child component: %s"), *ChildActorComponent->GetName());
                        continue;
                    }

                    UE_LOG(LogTemp, Warning, TEXT("ClearLootPointActors: Removing child actor component: %s"), *ChildActorComponent->GetName());

                    // First set child actor class to null to prevent cascading deletion issues
                    ChildActorComponent->SetChildActorClass(nullptr);

                    // Unregister the component
                    if (ChildActorComponent->IsRegistered())
                    {
                        ChildActorComponent->UnregisterComponent();
                    }

                    // Detach from parent
                    ChildActorComponent->DetachFromComponent(FDetachmentTransformRules::KeepRelativeTransform);

                    // Destroy the component
                    ChildActorComponent->DestroyComponent();

                    UE_LOG(LogTemp, Warning, TEXT("ClearLootPointActors: Removed child actor component"));
                }
            }
        }
    }
    else
    {
        // If we don't have a root owner actor, process components the old way
        for (UChildActorComponent* Component : ChildActorComponents)
        {
            if (Component && IsValid(Component))
            {
                // If the component has a child actor, destroy it first
                AActor* ChildActor = Component->GetChildActor();
                if (ChildActor && IsValid(ChildActor))
                {
                    ChildActor->Destroy();
                }

                // Remove from scene
                RemoveComponent(Component);
            }
        }
    }

    // Clear the array
    ChildActorComponents.Empty();

    UE_LOG(LogTemp, Warning, TEXT("ClearLootPointActors: Complete"));
}

void FCELootPointsPreviewScene::UpdateLootPointTransforms()
{
    UE_LOG(LogTemp, Warning, TEXT("UpdateLootPointTransforms: Start"));

    // Check if we have a valid asset
    if (!Asset)
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateLootPointTransforms: No asset"));
        return;
    }

    // Safety check for the asset items
    if (!Asset->Items.Num())
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateLootPointTransforms: Asset has no items"));
        return;
    }

    // Update transforms for each loot point actor
    for (int32 i = 0; i < Asset->Items.Num(); ++i)
    {
        // Safety check for the asset
        if (!IsValid(Asset))
        {
            UE_LOG(LogTemp, Warning, TEXT("UpdateLootPointTransforms: Asset became invalid during processing"));
            break;
        }

        // Safety check for the item index
        if (!Asset->Items.IsValidIndex(i))
        {
            UE_LOG(LogTemp, Warning, TEXT("UpdateLootPointTransforms: Invalid item index %d"), i);
            continue;
        }

        const FCELootPointsItem& Item = Asset->Items[i];

        // Only update point items
        if (Item.ItemType == ECELootPointsItemType::Point)
        {
            // Get the child actor component for this item
            UChildActorComponent* ChildComponent = GetChildActorComponent(i);

            if (ChildComponent && IsValid(ChildComponent))
            {
                // Set transform
                FTransform Transform;
                Transform.SetLocation(FVector(Item.PointData.X, Item.PointData.Y, Item.PointData.Z));

                // Update component transform
                ChildComponent->SetRelativeTransform(Transform);

                // Also update the actor transform if we have a reference to it
                ACELootPoint* LootPoint = GetLootPointActor(i);
                if (LootPoint && IsValid(LootPoint))
                {
                    LootPoint->SetActorTransform(Transform);
                }

                UE_LOG(LogTemp, Warning, TEXT("Updated loot point actor %d to (%f, %f, %f)"),
                    i, Item.PointData.X, Item.PointData.Y, Item.PointData.Z);
            }
            else
            {
                UE_LOG(LogTemp, Warning, TEXT("UpdateLootPointTransforms: Child component %d is invalid"), i);
            }
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("UpdateLootPointTransforms: Complete"));
}

void FCELootPointsPreviewScene::SetupFloorMesh()
{
    UE_LOG(LogTemp, Warning, TEXT("SetupFloorMesh: Start"));

    // Load the floor mesh
    UStaticMesh* FloorMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/EditorMeshes/AssetViewer/Floor_Mesh.Floor_Mesh"));

    if (FloorMesh)
    {
        // Create a static mesh component for the floor
        FloorMeshComponent = NewObject<UStaticMeshComponent>(GetTransientPackage());
        FloorMeshComponent->SetStaticMesh(FloorMesh);

        // Set transform
        FTransform Transform = FTransform::Identity;
        Transform.SetScale3D(FVector(4.0f, 4.0f, 1.0f));

        // Add to scene
        AddComponent(FloorMeshComponent, Transform);

        UE_LOG(LogTemp, Warning, TEXT("SetupFloorMesh: Floor mesh added"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("SetupFloorMesh: Failed to load floor mesh"));
    }

    UE_LOG(LogTemp, Warning, TEXT("SetupFloorMesh: Complete"));
}

void FCELootPointsPreviewScene::UpdateActorSelectionState()
{
    UE_LOG(LogTemp, Warning, TEXT("UpdateActorSelectionState: SelectedItemIndex=%d"), SelectedItemIndex);

    // Update selection state for all loot point actors
    for (ACELootPoint* LootPoint : LootPointActors)
    {
        if (LootPoint)
        {
            bool bSelected = (LootPoint->GetItemIndex() == SelectedItemIndex);
            LootPoint->SetSelected(bSelected);

            UE_LOG(LogTemp, Warning, TEXT("Actor %s (index %d) selected: %d"),
                *LootPoint->GetName(), LootPoint->GetItemIndex(), bSelected);
        }
    }
}

UChildActorComponent* FCELootPointsPreviewScene::GetChildActorComponent(int32 Index) const
{
    // Safety check for the index
    if (Index < 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("GetChildActorComponent: Negative index %d"), Index);
        return nullptr;
    }

    // Safety check for the array
    if (!ChildActorComponents.IsValidIndex(Index))
    {
        UE_LOG(LogTemp, Warning, TEXT("GetChildActorComponent: Invalid index %d (array size: %d)"),
            Index, ChildActorComponents.Num());
        return nullptr;
    }

    // Get the component
    UChildActorComponent* Component = ChildActorComponents[Index];

    // Safety check for the component
    if (!Component || !IsValid(Component))
    {
        UE_LOG(LogTemp, Warning, TEXT("GetChildActorComponent: Component at index %d is invalid"), Index);
        return nullptr;
    }

    return Component;
}

void FCELootPointsPreviewScene::UpdateChildTransform(int32 Index, const FTransform& NewTransform)
{
    // Safety checks
    if (Index < 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Negative index %d"), Index);
        return;
    }

    if (!ChildActorComponents.IsValidIndex(Index))
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Invalid child index %d"), Index);
        return;
    }

    if (!Asset)
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Asset is null"));
        return;
    }

    if (!IsValid(Asset))
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Asset is invalid"));
        return;
    }

    if (!Asset->Items.IsValidIndex(Index))
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Invalid item index %d in asset"), Index);
        return;
    }

    UChildActorComponent* ChildComponent = ChildActorComponents[Index];
    if (!ChildComponent || !IsValid(ChildComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Child component is invalid"));
        return;
    }

    // Ensure uniform X/Y scaling for the transform
    FTransform AdjustedTransform = NewTransform;
    FVector Scale = NewTransform.GetScale3D();

    // Make X and Y scale uniform (use the larger value)
    float UniformXY = FMath::Max(Scale.X, Scale.Y);
    UniformXY = FMath::Max(UniformXY, 0.0001f);
    FVector AdjustedScale = FVector(UniformXY, UniformXY, Scale.Z);
    AdjustedTransform.SetScale3D(AdjustedScale);

    // Store the scale information for this item index
    ItemScales.FindOrAdd(Index) = AdjustedScale;

    UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Storing scale for item %d: %s"),
        Index, *AdjustedScale.ToString());

    // Update the component transform with the adjusted transform
    ChildComponent->SetRelativeTransform(AdjustedTransform);

    // Also update the child actor if it's a CELootPoint
    if (ACELootPoint* LootPoint = Cast<ACELootPoint>(ChildComponent->GetChildActor()))
    {
        if (IsValid(LootPoint))
        {
            // Ensure the actor's scale is also updated correctly using the public method
            LootPoint->SetUniformXYScale(UniformXY);
        }
    }

    // Update the asset data
    FCELootPointsItem& Item = Asset->Items[Index];

    // Only update point items
    if (Item.ItemType == ECELootPointsItemType::Point)
    {
        // Update point data (position and scale)
        Item.PointData.X = AdjustedTransform.GetLocation().X;
        Item.PointData.Y = AdjustedTransform.GetLocation().Y;
        Item.PointData.Z = AdjustedTransform.GetLocation().Z;

        // Store the scale in the asset
        Item.PointData.ScaleXY = AdjustedScale.X; // X and Y are the same
        Item.PointData.ScaleZ = AdjustedScale.Z;

        // Mark the asset as modified
        Asset->Modify();

        UE_LOG(LogTemp, Warning, TEXT("Updated item %d position to (%f, %f, %f) with scale (%f, %f)"),
            Index, Item.PointData.X, Item.PointData.Y, Item.PointData.Z,
            Item.PointData.ScaleXY, Item.PointData.ScaleZ);
    }
}

UChildActorComponent* FCELootPointsPreviewScene::CreateChildActorComponent(TSubclassOf<ACELootPoint> ActorClass, const FTransform& Transform)
{
    // Safety checks
    if (!ActorClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorComponent: ActorClass is null"));
        return nullptr;
    }

    // Get the world from the preview scene
    if (!PreviewWorld)
    {
        PreviewWorld = GetWorld();
    }

    if (!PreviewWorld)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorComponent: Failed to get world"));
        return nullptr;
    }

    // Check if we have a valid root owner actor
    if (!RootOwnerActor || !IsValid(RootOwnerActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorComponent: Root owner actor is invalid, creating a new one"));

        // Make sure we have a valid world with a persistent level
        if (!PreviewWorld->PersistentLevel)
        {
            UE_LOG(LogTemp, Error, TEXT("CreateChildActorComponent: World has no persistent level"));
            return nullptr;
        }

        // Create the root owner actor
        FActorSpawnParameters SpawnParams;
        SpawnParams.ObjectFlags = RF_Transient;
        SpawnParams.Name = FName(TEXT("CELootPointsRootOwner"));
        SpawnParams.bNoFail = true;

        // Create the actor with explicit class and transform
        RootOwnerActor = PreviewWorld->SpawnActor<ACELootPointsComponentOwner>(ACELootPointsComponentOwner::StaticClass(), FTransform::Identity, SpawnParams);

        if (!RootOwnerActor || !IsValid(RootOwnerActor))
        {
            UE_LOG(LogTemp, Error, TEXT("CreateChildActorComponent: Failed to create root owner actor"));
            return nullptr;
        }

        UE_LOG(LogTemp, Display, TEXT("Created new root owner actor: %s"), *RootOwnerActor->GetName());
    }

    // Safety check for root component
    if (!RootOwnerActor->GetRootComponent())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChildActorComponent: Root owner actor has no root component"));
        return nullptr;
    }

    // Create a new child actor component and attach it to the root owner actor
    UChildActorComponent* ChildActorComponent = NewObject<UChildActorComponent>(RootOwnerActor);
    if (!ChildActorComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorComponent: Failed to create child actor component"));
        return nullptr;
    }

    // Set the child actor class
    ChildActorComponent->SetChildActorClass(ActorClass);

    // Register the component with the root owner actor
    ChildActorComponent->RegisterComponent();

    // Attach to the root component
    ChildActorComponent->AttachToComponent(RootOwnerActor->GetRootComponent(), FAttachmentTransformRules::KeepRelativeTransform);

    // Set the component's transform
    ChildActorComponent->SetRelativeTransform(Transform);

    // Log success
    UE_LOG(LogTemp, Display, TEXT("CreateChildActorComponent: Successfully created child actor with root owner actor %s"),
        *RootOwnerActor->GetName());

    return ChildActorComponent;
}

UChildActorComponent* FCELootPointsPreviewScene::CreateChildActorFromP3DBlueprint(UP3DBlueprint* Blueprint, const FTransform& Transform)
{
    // Safety checks
    if (!Blueprint)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: Blueprint is null"));
        return nullptr;
    }

    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Creating child actor for blueprint %s"), *Blueprint->GetName());

    if (!Blueprint->GeneratedClass)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: Blueprint %s has no generated class"), *Blueprint->GetName());
        return nullptr;
    }

    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Blueprint %s has generated class %s"),
        *Blueprint->GetName(), *Blueprint->GeneratedClass->GetName());

    if (!Blueprint->GeneratedClass->IsChildOf(AActor::StaticClass()))
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: Blueprint %s generated class is not an actor"), *Blueprint->GetName());
        return nullptr;
    }

    // Get the world from the preview scene
    if (!PreviewWorld)
    {
        PreviewWorld = GetWorld();
    }

    if (!PreviewWorld)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: Failed to get world"));
        return nullptr;
    }

    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Using world %s"), *PreviewWorld->GetName());

    // Check if we have a valid root owner actor
    if (!RootOwnerActor || !IsValid(RootOwnerActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Root owner actor is invalid, creating a new one"));

        // Make sure we have a valid world with a persistent level
        if (!PreviewWorld->PersistentLevel)
        {
            UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: World has no persistent level"));
            return nullptr;
        }

        // Create the root owner actor
        FActorSpawnParameters SpawnParams;
        SpawnParams.ObjectFlags = RF_Transient;
        SpawnParams.Name = FName(TEXT("CELootPointsRootOwner"));
        SpawnParams.bNoFail = true;

        // Create the actor with explicit class and transform
        RootOwnerActor = PreviewWorld->SpawnActor<ACELootPointsComponentOwner>(ACELootPointsComponentOwner::StaticClass(), FTransform::Identity, SpawnParams);

        if (!RootOwnerActor || !IsValid(RootOwnerActor))
        {
            UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: Failed to create root owner actor"));
            return nullptr;
        }

        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Created new root owner actor: %s"), *RootOwnerActor->GetName());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Using existing root owner actor: %s"), *RootOwnerActor->GetName());
    }

    // Safety check for root component
    if (!RootOwnerActor->GetRootComponent())
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: Root owner actor has no root component"));
        return nullptr;
    }

    // Create a new child actor component and attach it to the root owner actor
    UChildActorComponent* ChildActorComponent = NewObject<UChildActorComponent>(RootOwnerActor);
    if (!ChildActorComponent)
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: Failed to create child actor component"));
        return nullptr;
    }

    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Created child actor component: %s"), *ChildActorComponent->GetName());

    // Set the child actor class
    TSubclassOf<AActor> ActorClass = Cast<UClass>(Blueprint->GeneratedClass);
    ChildActorComponent->SetChildActorClass(ActorClass);

    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Set child actor class to %s"), *ActorClass->GetName());

    // Register the component with the root owner actor
    ChildActorComponent->RegisterComponent();
    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Registered component"));

    // Attach to the root component
    ChildActorComponent->AttachToComponent(RootOwnerActor->GetRootComponent(), FAttachmentTransformRules::KeepRelativeTransform);
    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Attached component to root"));

    // Set the component's transform
    ChildActorComponent->SetRelativeTransform(Transform);
    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Set transform: %s"), *Transform.ToString());

    // Make sure the component is visible
    ChildActorComponent->SetVisibility(true);
    ChildActorComponent->SetHiddenInGame(false);
    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Set visibility to true"));

    // Force the component to create its child actor immediately
    ChildActorComponent->CreateChildActor();
    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Forced creation of child actor"));

    // Check if the child actor was created
    AActor* ChildActor = ChildActorComponent->GetChildActor();
    if (ChildActor && IsValid(ChildActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Child actor created: %s"), *ChildActor->GetName());

        // Make sure the child actor is visible
        ChildActor->SetActorHiddenInGame(false);
        ChildActor->SetActorEnableCollision(true);
        ChildActor->SetActorTickEnabled(true);

        // Make sure all components of the child actor are visible
        TArray<USceneComponent*> ActorSceneComponents;
        ChildActor->GetComponents<USceneComponent>(ActorSceneComponents);
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Child actor has %d components"), ActorSceneComponents.Num());

        for (USceneComponent* Component : ActorSceneComponents)
        {
            if (Component)
            {
                Component->SetVisibility(true);
                Component->SetHiddenInGame(false);
                UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Set component %s visibility to true"), *Component->GetName());
            }
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("CreateChildActorFromP3DBlueprint: Failed to create child actor"));
    }

    // Log success
    UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromP3DBlueprint: Successfully created child actor for %s with root owner actor %s"),
        *Blueprint->GetName(), *RootOwnerActor->GetName());

    return ChildActorComponent;
}

bool FCELootPointsPreviewScene::UpdateP3DModelFromRootType()
{
    UE_LOG(LogTemp, Warning, TEXT("UpdateP3DModelFromRootType: Start"));

    // Check if we have a valid world
    UWorld* World = GetWorld();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: No valid world"));
        return false;
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateP3DModelFromRootType: Using world %s"), *World->GetName());
    }

    // Check if we have a valid asset
    if (!Asset)
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: No asset"));
        return false;
    }

    UE_LOG(LogTemp, Warning, TEXT("UpdateP3DModelFromRootType: Asset is %s"), *Asset->GetName());

    // Safety check for the asset items
    if (!Asset->Items.Num())
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: Asset has no items"));
        return false;
    }

    // Get the root item (always at index 0)
    const FCELootPointsItem& RootItem = Asset->Items[0];

    // Make sure it's a root item
    if (RootItem.ItemType != ECELootPointsItemType::Root)
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: First item is not a root item"));
        return false;
    }

    // Remove existing P3D model component if it exists
    if (P3DModelComponent && IsValid(P3DModelComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateP3DModelFromRootType: Found existing P3D model component %s"), *P3DModelComponent->GetName());

        // Check if the child actor is valid
        AActor* ChildActor = P3DModelComponent->GetChildActor();
        if (ChildActor && IsValid(ChildActor))
        {
            UE_LOG(LogTemp, Warning, TEXT("UpdateP3DModelFromRootType: Existing component has child actor %s"), *ChildActor->GetName());
        }

        // First set child actor class to null to prevent cascading deletion issues
        P3DModelComponent->SetChildActorClass(nullptr);

        // Unregister the component
        if (P3DModelComponent->IsRegistered())
        {
            P3DModelComponent->UnregisterComponent();
        }

        // Detach from parent
        P3DModelComponent->DetachFromComponent(FDetachmentTransformRules::KeepRelativeTransform);

        // Destroy the component
        P3DModelComponent->DestroyComponent();

        P3DModelComponent = nullptr;

        UE_LOG(LogTemp, Warning, TEXT("UpdateP3DModelFromRootType: Removed existing P3D model component"));
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateP3DModelFromRootType: No existing P3D model component"));
    }

    // Check if the root item has a valid Type property
    if (RootItem.RootData.Type.IsNull())
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: Root item has no Type property set"));
        return false;
    }

    UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Type property is set to %s"),
        *RootItem.RootData.Type.ToSoftObjectPath().ToString());

    // Load the ConfigClass asset
    UConfigClass* ConfigClass = RootItem.RootData.Type.LoadSynchronous();
    if (!ConfigClass)
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: Failed to load ConfigClass asset"));
        return false;
    }

    UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Loaded ConfigClass asset %s"), *ConfigClass->GetName());

    // Check if the ConfigClass has a valid Model property
    if (ConfigClass->Model.IsNull())
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: ConfigClass has no Model property set"));
        return false;
    }

    UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Model property is set to %s"),
        *ConfigClass->Model.ToSoftObjectPath().ToString());

    // Load the P3D Blueprint asset
    UP3DBlueprint* P3DBlueprint = ConfigClass->Model.LoadSynchronous();
    if (!P3DBlueprint)
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: Failed to load P3D Blueprint asset"));
        return false;
    }

    UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Loaded P3D Blueprint asset %s"), *P3DBlueprint->GetName());

    // Check if the P3D Blueprint has a generated class
    if (!P3DBlueprint->GeneratedClass)
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: P3D Blueprint has no generated class"));
        return false;
    }

    UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: P3D Blueprint has generated class %s"),
        *P3DBlueprint->GeneratedClass->GetName());

    // Create a child actor component for the P3D Blueprint
    FTransform Transform = FTransform::Identity;
    // Apply a 90-degree rotation around the Z axis
    Transform.SetRotation(FQuat(FRotator(0.0f, 180.0f, 0.0f)));
    UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Applied 90-degree Z-axis rotation to P3D model"));
    P3DModelComponent = CreateChildActorFromP3DBlueprint(P3DBlueprint, Transform);

    if (P3DModelComponent)
    {
        UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Successfully created P3D model component for %s"),
            *P3DBlueprint->GetName());

        // Check if the child actor was created
        AActor* ChildActor = P3DModelComponent->GetChildActor();
        if (ChildActor && IsValid(ChildActor))
        {
            UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Child actor created: %s"), *ChildActor->GetName());

            // Make sure the component is registered and visible
            if (!P3DModelComponent->IsRegistered())
            {
                P3DModelComponent->RegisterComponent();
                UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Registered P3D model component"));
            }

            // Make sure the component is visible
            P3DModelComponent->SetVisibility(true);
            P3DModelComponent->SetHiddenInGame(false);

            // Make sure the child actor is visible
            ChildActor->SetActorHiddenInGame(false);
            ChildActor->SetActorEnableCollision(true);
            ChildActor->SetActorTickEnabled(true);

            // Make sure all components of the child actor are visible
            TArray<USceneComponent*> ActorSceneComponents;
            ChildActor->GetComponents<USceneComponent>(ActorSceneComponents);
            for (USceneComponent* Component : ActorSceneComponents)
            {
                if (Component)
                {
                    Component->SetVisibility(true);
                    Component->SetHiddenInGame(false);

                    // Check if this component is a ChildActorComponent that might contain another P3D asset
                    UChildActorComponent* ChildActorComp = Cast<UChildActorComponent>(Component);
                    if (ChildActorComp && IsValid(ChildActorComp))
                    {
                        UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Found child actor component %s in the P3D asset"),
                            *ChildActorComp->GetName());

                        // Make sure the child actor component is visible
                        ChildActorComp->SetVisibility(true);
                        ChildActorComp->SetHiddenInGame(false);

                        // Force the component to create its child actor immediately if it hasn't already
                        if (!ChildActorComp->GetChildActor())
                        {
                            UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Forcing creation of nested child actor"));
                            ChildActorComp->CreateChildActor();
                        }

                        // Make sure the nested child actor is visible
                        AActor* NestedChildActor = ChildActorComp->GetChildActor();
                        if (NestedChildActor && IsValid(NestedChildActor))
                        {
                            UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Making nested child actor %s visible"),
                                *NestedChildActor->GetName());

                            NestedChildActor->SetActorHiddenInGame(false);
                            NestedChildActor->SetActorEnableCollision(true);
                            NestedChildActor->SetActorTickEnabled(true);

                            // Make sure all components of the nested child actor are visible
                            TArray<USceneComponent*> NestedActorComponents;
                            NestedChildActor->GetComponents<USceneComponent>(NestedActorComponents);

                            for (USceneComponent* NestedComponent : NestedActorComponents)
                            {
                                if (NestedComponent)
                                {
                                    NestedComponent->SetVisibility(true);
                                    NestedComponent->SetHiddenInGame(false);

                                    // Recursively handle any deeper nested child actor components
                                    UChildActorComponent* DeepNestedChildActorComp = Cast<UChildActorComponent>(NestedComponent);
                                    if (DeepNestedChildActorComp && IsValid(DeepNestedChildActorComp))
                                    {
                                        UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Found deep nested child actor component %s"),
                                            *DeepNestedChildActorComp->GetName());

                                        DeepNestedChildActorComp->SetVisibility(true);
                                        DeepNestedChildActorComp->SetHiddenInGame(false);

                                        if (!DeepNestedChildActorComp->GetChildActor())
                                        {
                                            DeepNestedChildActorComp->CreateChildActor();
                                        }

                                        AActor* DeepNestedActor = DeepNestedChildActorComp->GetChildActor();
                                        if (DeepNestedActor && IsValid(DeepNestedActor))
                                        {
                                            DeepNestedActor->SetActorHiddenInGame(false);
                                            DeepNestedActor->SetActorEnableCollision(true);
                                            DeepNestedActor->SetActorTickEnabled(true);

                                            TArray<USceneComponent*> DeepNestedComponents;
                                            DeepNestedActor->GetComponents<USceneComponent>(DeepNestedComponents);

                                            for (USceneComponent* DeepComponent : DeepNestedComponents)
                                            {
                                                if (DeepComponent)
                                                {
                                                    DeepComponent->SetVisibility(true);
                                                    DeepComponent->SetHiddenInGame(false);
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // Successfully created the P3D model
            UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Complete - Success"));
            return true;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: Child actor was not created"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateP3DModelFromRootType: Failed to create P3D model component"));
    }

    UE_LOG(LogTemp, Display, TEXT("UpdateP3DModelFromRootType: Complete - Failed"));
    return false;
}

#undef LOCTEXT_NAMESPACE
