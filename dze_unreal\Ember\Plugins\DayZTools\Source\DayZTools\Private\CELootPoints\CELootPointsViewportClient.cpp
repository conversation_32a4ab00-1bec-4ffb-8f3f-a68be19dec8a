// CELootPointsViewportClient.cpp
#include "CELootPoints/CELootPointsViewportClient.h"

#include "CELootPoints/CELootPointsPreviewScene.h"
#include "CELootPoints/CELootPointsEditorToolkit.h"
#include "CELootPoints/CELootPointsEditorViewport.h"
#include "CELootPoints/CELootPoint.h"
#include "Editor.h"

// Engine includes
#include "Engine/World.h"
#include "Utils.h"
#include "DrawDebugHelpers.h"
#include "Components/PrimitiveComponent.h"
#include "ComponentVisualizer.h"
#include "GameFramework/Actor.h"
#include "Engine/Canvas.h"
#include "EngineUtils.h"
#include "Editor/UnrealEdTypes.h"
#include "EditorModeManager.h"
#include "Engine/Selection.h"
#include "Components/ChildActorComponent.h"
#include "Components/SceneComponent.h"
#include "ScopedTransaction.h"
#include "EditorViewportClient.h"
#include "Framework/Application/SlateApplication.h"
#include "Widgets/SWidget.h"
#include "Widgets/Input/SButton.h"
#include "UnrealWidget.h"
#include "CanvasItem.h"
#include "SceneView.h"
#include "HitProxies.h"

// Implement the hit proxy
IMPLEMENT_HIT_PROXY(HCELootPointProxy, HHitProxy);

#define LOCTEXT_NAMESPACE "CELootPointsEditor"

FCELootPointsViewportClient::FCELootPointsViewportClient(
    const TSharedRef<IToolkitHost>& InToolkitHost,
    FCELootPointsPreviewScene* InPreviewScene,
    const TSharedRef<SEditorViewport>& InViewportWidget,
    FCELootPointsEditorToolkit* InEditorToolkit
)
    : FEditorViewportClient(&GLevelEditorModeTools(), InPreviewScene, InViewportWidget)
    , PreviewScenePtr(InPreviewScene)
    , EditorToolkit(InEditorToolkit)
    , bShouldFocusOnBounds(true)
    , SelectedItemIndex(INDEX_NONE)
    , bIsManipulating(false)
{
    UE_LOG(LogTemp, Warning, TEXT("ViewportClient constructor - Start"));

    SetViewMode(VMI_Lit);
    OverrideNearClipPlane(1.0f);
    bUsingOrbitCamera = false; // Use standard editor fly controls
    SetRealtime(true);
    SetCameraSpeedSetting(3);
    EngineShowFlags.SetCompositeEditorPrimitives(true);
    EngineShowFlags.SetSelectionOutline(true);
    EngineShowFlags.SetGrid(true);
    EngineShowFlags.SetModeWidgets(true); // Show gizmos

    // Initial camera position
    SetViewLocation(FVector(500.f, 500.f, 500.f));
    SetViewRotation(FRotator(-45.f, -45.f, 0.f));

    // Ensure the widget starts hidden and set defaults
    if (Widget) { Widget->SetDefaultVisibility(false); }
    if (ModeTools) {
        ModeTools->SetWidgetMode(UE::Widget::WM_Translate);
        ModeTools->SetCoordSystem(COORD_World);
    }
    else { UE_LOG(LogTemp, Error, TEXT("ModeTools is invalid in ViewportClient constructor!")); }

    UE_LOG(LogTemp, Warning, TEXT("ViewportClient constructor - Complete"));
}


void FCELootPointsViewportClient::Tick(float DeltaSeconds)
{
    FEditorViewportClient::Tick(DeltaSeconds);

    // Tick the preview scene world
    if (PreviewScenePtr)
    {
        // Initial focus logic
        if (bShouldFocusOnBounds)
        {
            FBoxSphereBounds Bounds = GetAllChildrenBounds();
            if (Bounds.SphereRadius > KINDA_SMALL_NUMBER)
            {
                FocusViewportOnBounds(Bounds, true);
            }
            bShouldFocusOnBounds = false;
        }

        // Use a try-catch ONLY if absolutely necessary for debugging stubborn crashes.
        try
        {
            if (PreviewScenePtr->GetWorld())
            {
                PreviewScenePtr->GetWorld()->Tick(LEVELTICK_All, DeltaSeconds);
            }
        }
        catch (...)
        {
            UE_LOG(LogTemp, Error, TEXT("Exception caught during world tick in viewport client"));
        }
    }
}

void FCELootPointsViewportClient::Draw(FViewport* InViewport, FCanvas* Canvas)
{
    EngineShowFlags.SetCollision(true);
    // Base class draw first (handles scene rendering, grid, gizmo etc.)
    FEditorViewportClient::Draw(InViewport, Canvas);

    // We don't need to manually add hit proxies here as Unreal handles this automatically
    // through the actor/component rendering system. The hit proxies are registered when
    // the actors are rendered.

    // If we need to debug the selection, we can draw bounds around the selected actor
    if (PreviewScenePtr && SelectedItemIndex != INDEX_NONE)
    {
        ACELootPoint* LootPoint = PreviewScenePtr->GetLootPointActor(SelectedItemIndex);
        if (LootPoint && IsValid(LootPoint))
        {
            // Draw the actor's bounds
            FBox Bounds = LootPoint->GetComponentsBoundingBox(true);
            if (!Bounds.IsValid)
            {
                // If the bounds are not valid, create a small box around the actor's location
                Bounds = FBox::BuildAABB(LootPoint->GetActorLocation(), FVector(50.0f));
            }

            // Draw debug bounds in the viewport
            DrawDebugBox(
                PreviewScenePtr->GetWorld(),
                Bounds.GetCenter(),
                Bounds.GetExtent(),
                FQuat::Identity,
                FColor::Green,
                false,
                0.0f,
                0,
                1.0f
            );
        }
    }
}

bool FCELootPointsViewportClient::InputKey(const FInputKeyEventArgs& EventArgs)
{
    // If we have a loot-point selected and you press E, just consume it so
    // the base class never switches into WM_Rotate.
    if ( SelectedItemIndex != INDEX_NONE
          && EventArgs.Event == IE_Pressed
          && EventArgs.Key == EKeys::E )
        {
            return true;  // handled (do nothing)
        }
    // Only allow switching to Translate (W) or Scale (R) — no Rotate
    if (SelectedItemIndex != INDEX_NONE && EventArgs.Event == IE_Pressed)
    {
        UE::Widget::EWidgetMode NewMode = GetWidgetMode();
        bool bChanged = false;

        if (EventArgs.Key == EKeys::W || EventArgs.Key == EKeys::SpaceBar)
        {
            NewMode = UE::Widget::WM_Translate;
            bChanged = true;
        }
        else if (EventArgs.Key == EKeys::R)
        {
            NewMode = UE::Widget::WM_Scale;
            bChanged = true;
        }
        // (E was already eaten above, so we never map to WM_Rotate)

        if (bChanged && NewMode != GetWidgetMode())
        {
            SetWidgetMode(NewMode);
            Invalidate();  // redraw gizmo
            return true;   // handled
        }
    }

    // F / focus key, etc remains unchanged…
    if (EventArgs.Event == IE_Pressed && EventArgs.Key == EKeys::F)
    {
        FBoxSphereBounds B = GetAllChildrenBounds();
        if (B.SphereRadius > KINDA_SMALL_NUMBER)
        {
            FocusViewportOnBounds(B, true);
        }
        return true;
    }

    return FEditorViewportClient::InputKey(EventArgs);
}

bool FCELootPointsViewportClient::InputAxis(FViewport* InViewport, FInputDeviceId DeviceID, FKey Key, float Delta, float DeltaTime, int32 NumSamples, bool bGamepad)
{
    // If we are manipulating the widget, let the base class handle it.
    if (bIsManipulating)
    {
        // Allow engine to process axis events for fine-grained widget interaction
        return FEditorViewportClient::InputAxis(InViewport, DeviceID, Key, Delta, DeltaTime, NumSamples, bGamepad);
    }

    // Otherwise (not manipulating), let the base class handle camera movement etc.
    return FEditorViewportClient::InputAxis(InViewport, DeviceID, Key, Delta, DeltaTime, NumSamples, bGamepad);
}

bool FCELootPointsViewportClient::ShouldOrbitCamera() const
{
    // Return false to use standard level editor camera controls
    return false;
}

void FCELootPointsViewportClient::FocusViewportOnBounds(const FBoxSphereBounds& Bounds, bool bInstant)
{
    if (!Viewport || Bounds.SphereRadius <= KINDA_SMALL_NUMBER) return;

    // Explicitly calculate MinDistance before declaring const
    float TmpMinDistance = 1.0f; // Default near clip value

    const float MinDistance = TmpMinDistance; // Now initialize the const variable

    // Calculate distance needed to see the entire sphere based on FOV
    const float TargetDistance = FMath::Max(Bounds.SphereRadius / FMath::Tan(FMath::DegreesToRadians(ViewFOV * 0.5f)), MinDistance);

    FViewportCameraTransform& Cam = GetViewTransform();
    Cam.SetLookAt(Bounds.Origin);
    FVector NewLocation = Bounds.Origin - GetViewRotation().Vector() * TargetDistance;

    if (bInstant) { Cam.SetLocation(NewLocation); }
    else { Cam.SetLocation(NewLocation); /* Add interpolation later if needed */ }

    Invalidate();
}

void FCELootPointsViewportClient::ProcessClick(FSceneView& View, HHitProxy* HitProxy, FKey Key, EInputEvent Event, uint32 HitX, uint32 HitY)
{
    UE_LOG(LogTemp, Warning, TEXT("ProcessClick: Event=%d, Key=%s, HitProxy=%s"),
        (int32)Event, *Key.ToString(), HitProxy ? TEXT("VALID") : TEXT("NULL"));

    // Only handle Left Mouse Button presses for selection
    if (Event == IE_Released && Key == EKeys::LeftMouseButton)
    {
        int32 ClickedItemIndex = INDEX_NONE;
        bool bHitGizmo = false;

        if (HitProxy)
        {
            // Check what type of hit proxy we're dealing with
            if (HitProxy->IsA(HWidgetUtilProxy::StaticGetType()))
            {
                UE_LOG(LogTemp, Warning, TEXT("Hit proxy is gizmo/widget"));
                bHitGizmo = true;
                FEditorViewportClient::ProcessClick(View, HitProxy, Key, Event, HitX, HitY);
                return;  // Let base class handle gizmo
            }
            else if (HitProxy->IsA(HCELootPointProxy::StaticGetType()))
            {
                UE_LOG(LogTemp, Warning, TEXT("Hit proxy is CELootPoint type"));
                HCELootPointProxy* LootPointProxy = static_cast<HCELootPointProxy*>(HitProxy);
                ClickedItemIndex = LootPointProxy->ItemIndex;
                UE_LOG(LogTemp, Warning, TEXT("Hit CELootPoint proxy with index %d"), ClickedItemIndex);
            }
            else if (HitProxy->IsA(HActor::StaticGetType()))
            {
                UE_LOG(LogTemp, Warning, TEXT("Hit proxy is Actor type"));
                HActor* ActorProxy = static_cast<HActor*>(HitProxy);
                AActor* HitActor = ActorProxy->Actor;

                if (HitActor && PreviewScenePtr)
                {
                    UE_LOG(LogTemp, Warning, TEXT("Hit actor: %s"), *HitActor->GetName());
                    const auto& LootPointActors = PreviewScenePtr->GetLootPointActors();

                    // Try to find which loot point matches this actor
                    for (int32 i = 0; i < LootPointActors.Num(); ++i)
                    {
                        ACELootPoint* LootPoint = LootPointActors[i];
                        if (LootPoint == HitActor)
                        {
                            ClickedItemIndex = LootPoint->GetItemIndex();
                            UE_LOG(LogTemp, Warning,
                                TEXT("Matched actor %s to item index %d"),
                                *HitActor->GetName(), ClickedItemIndex);
                            break;
                        }
                    }

                    if (ClickedItemIndex == INDEX_NONE)
                    {
                        UE_LOG(LogTemp, Warning,
                            TEXT("Actor %s not found in loot point actors"),
                            *HitActor->GetName());
                    }
                }
            }
            else if (HitProxy->IsA(HComponentVisProxy::StaticGetType()))
            {
                UE_LOG(LogTemp, Warning, TEXT("Hit proxy is Component type"));

                // For simplicity, select the first item when we hit any component
                if (PreviewScenePtr)
                {
                    const TArray<ACELootPoint*>& LootPointActors = PreviewScenePtr->GetLootPointActors();
                    if (LootPointActors.Num() > 0)
                    {
                        ClickedItemIndex = LootPointActors[0]->GetItemIndex();
                        UE_LOG(LogTemp, Warning, TEXT("Selected first loot point for testing"));
                    }
                }
            }
        }

        // Apply selection if we found something to select
        if (ClickedItemIndex != INDEX_NONE)
        {
            UE_LOG(LogTemp, Warning, TEXT("Selecting Item Index %d"), ClickedItemIndex);
            if (EditorToolkit)
            {
                EditorToolkit->SelectItem(ClickedItemIndex);
            }
            else
            {
                SetSelectedItemIndex(ClickedItemIndex);
            }
            Invalidate();
        }
        else if (!bHitGizmo)  // Only deselect if we didn't hit the gizmo
        {
            UE_LOG(LogTemp, Warning, TEXT("Deselecting"));
            if (EditorToolkit)
            {
                EditorToolkit->SelectItem(INDEX_NONE);
            }
            else
            {
                SetSelectedItemIndex(INDEX_NONE);
            }
            Invalidate();
        }
    }
    else
    {
        FEditorViewportClient::ProcessClick(View, HitProxy, Key, Event, HitX, HitY);
    }
}

bool FCELootPointsViewportClient::InputWidgetDelta(
    FViewport*        InViewport,
    EAxisList::Type   CurrentAxis,
    FVector&          Drag,
    FRotator&         Rot,
    FVector&          Scale)
{
    // If someone somehow got into Rotate mode, bail immediately
    if (GetWidgetMode() == UE::Widget::WM_Rotate)
    {
        return true;
    }
    UE_LOG(LogTemp, Warning, TEXT("InputWidgetDelta: Called. Axis=%d, bIsManipulating=%d, SelectedIndex=%d"),
        (int32)CurrentAxis, bIsManipulating, SelectedItemIndex);

    // Only handle when we're in the middle of a widget drag, have a valid selection,
    // and a non-zero axis request
    if (bIsManipulating
        && SelectedItemIndex != INDEX_NONE
        && PreviewScenePtr
        && CurrentAxis != EAxisList::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("InputWidgetDelta: Processing Delta. Drag=%s, Rot=%s, Scale=%s"),
            *Drag.ToString(), *Rot.ToString(), *Scale.ToString());

        UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedItemIndex);
        if (Comp && IsValid(Comp))
        {
            const UE::Widget::EWidgetMode WidgetMode = GetWidgetMode();
            const ECoordSystem CoordSystem = ModeTools ? ModeTools->GetCoordSystem() : COORD_World;

            // Grab the current relative transform
            FTransform CurrentRelative = Comp->GetRelativeTransform();
            FTransform NewRelative = CurrentRelative;

            // Find parent’s world transform so we can convert world drags back into local space
            const FTransform ParentWorldTransform = Comp->GetAttachParent()
                ? Comp->GetAttachParent()->GetComponentTransform()
                : FTransform::Identity;

            bool bChanged = false;

            // TRANSLATE
            if (WidgetMode == UE::Widget::WM_Translate && !Drag.IsZero())
            {
                FVector RelativeDrag;
                if (CoordSystem == COORD_World)
                {
                    RelativeDrag = ParentWorldTransform.InverseTransformVectorNoScale(Drag);
                }
                else
                {
                    // In local mode we assume Drag is already in component space
                    RelativeDrag = Drag;
                }
                NewRelative.AddToTranslation(RelativeDrag);
                bChanged = true;
            }
            // SCALE
            else if (WidgetMode == UE::Widget::WM_Scale && !Scale.IsZero())
            {
                // Get current scale
                FVector CurrScale = CurrentRelative.GetScale3D();

                // Calculate new scale with delta applied
                FVector DesiredScale = CurrScale + Scale;

                // For X and Y, use the larger of the two to ensure uniform scaling
                float AbsX = FMath::Abs(Scale.X);
                float AbsY = FMath::Abs(Scale.Y);

                // Determine which axis had the larger change
                float TargetXY = CurrScale.X;
                if (AbsY > AbsX && Scale.Y != 0.0f)
                {
                    // Y-axis had larger change, use Y as target
                    TargetXY = DesiredScale.Y;
                }
                else if (AbsX > AbsY && Scale.X != 0.0f)
                {
                    // X-axis had larger change, use X as target
                    TargetXY = DesiredScale.X;
                }
                else if (Scale.X != 0.0f)
                {
                    // Equal change or only X changed
                    TargetXY = DesiredScale.X;
                }
                else if (Scale.Y != 0.0f)
                {
                    // Only Y changed
                    TargetXY = DesiredScale.Y;
                }

                // Ensure minimum scale
                TargetXY = FMath::Max(TargetXY, 0.0001f);

                // Apply uniform X/Y scaling, independent Z scaling
                FVector NewScale(TargetXY, TargetXY, DesiredScale.Z);
                NewRelative.SetScale3D(NewScale);

                UE_LOG(LogTemp, Warning, TEXT("Scale: Original=%s, Delta=%s, Final=%s"),
                    *CurrScale.ToString(), *Scale.ToString(), *NewScale.ToString());

                bChanged = true;
            }

            // If something actually changed, apply it
            if (bChanged && !CurrentRelative.Equals(NewRelative, 0.0001f))
            {
                UE_LOG(LogTemp, Warning, TEXT("InputWidgetDelta: Applying Transform: %s"), *NewRelative.ToString());

                // Update the component in the scene
                Comp->SetRelativeTransform(NewRelative);

                // Also notify your preview‐scene/data model
                PreviewScenePtr->UpdateChildTransform(SelectedItemIndex, NewRelative);

                // And let the toolkit write back into the asset
                if (EditorToolkit)
                {
                    EditorToolkit->HandleTransformUpdate(SelectedItemIndex, NewRelative);
                }

                // Redraw
                Invalidate();
            }

            return true; // we handled it
        }
    }

    // Fallback to default behavior (camera pan/rotate etc.)
    return FEditorViewportClient::InputWidgetDelta(InViewport, CurrentAxis, Drag, Rot, Scale);
}


void FCELootPointsViewportClient::TrackingStarted(
    const FInputEventState& InInputState,
    bool                    bIsDraggingWidget,
    bool                    bNudge
)
{
    FEditorViewportClient::TrackingStarted(InInputState, bIsDraggingWidget, bNudge);

    if (bIsDraggingWidget && SelectedItemIndex != INDEX_NONE)
    {
        // Start undo transaction
        GEditor->BeginTransaction( LOCTEXT("TransformLootPoint","Transform Loot Point") );

        // Mark the asset dirty/undoable
        if (EditorToolkit && EditorToolkit->GetAsset())
        {
            EditorToolkit->GetAsset()->Modify();
        }

        // Mark the component undoable
        UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedItemIndex);
        if (Comp)
        {
            Comp->Modify();
        }

        bIsManipulating = true;
    }
}

void FCELootPointsViewportClient::TrackingStopped()
{
    FEditorViewportClient::TrackingStopped();

    // End our transaction
    if (GEditor->IsTransactionActive())
    {
        GEditor->EndTransaction();
    }

    bIsManipulating = false;
}

void FCELootPointsViewportClient::SetSelectedItemIndex(int32 InItemIndex)
{
    if (SelectedItemIndex == InItemIndex)
    {
        UE_LOG(LogTemp, Warning, TEXT("SetSelectedItemIndex: unchanged (%d)"), InItemIndex);
        return;
    }

    SelectedItemIndex = InItemIndex;
    UE_LOG(LogTemp, Warning, TEXT("SetSelectedItemIndex: %d"), SelectedItemIndex);

    // Only loot‐point items get a gizmo
    ACELootPoint* LootPoint = nullptr;
    if (PreviewScenePtr)
    {
        LootPoint = PreviewScenePtr->GetLootPointActor(SelectedItemIndex);
    }

    if (LootPoint)
    {
        // show and snap widget
        if (Widget)
        {
            Widget->SetDefaultVisibility(true);
            ShowWidget(true);
        }
        if (ModeTools)
        {
            ModeTools->SetWidgetMode(UE::Widget::WM_Translate);
        }
    }
    else
    {
        // hide gizmo on root/container
        if (Widget)
        {
            Widget->SetDefaultVisibility(false);
            ShowWidget(false);
        }
    }

    Invalidate();
}

FVector FCELootPointsViewportClient::GetWidgetLocation() const
{
    if (SelectedItemIndex != INDEX_NONE && PreviewScenePtr) {
        UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedItemIndex);
        if (Comp && IsValid(Comp)) {
            if (AActor* ChildActor = Comp->GetChildActor()) {
                if (IsValid(ChildActor)) {
                    if (USceneComponent* RootComp = ChildActor->GetRootComponent()) {
                        if (IsValid(RootComp)) { return RootComp->GetComponentLocation(); }
                    }
                    return ChildActor->GetActorLocation(); // Fallback to Actor location
                }
            }
            // Fallback to Component location if ChildActor invalid or not found
            return Comp->GetComponentLocation();
        }
    }
    // Provide a default location if nothing valid is selected
    return FVector::ZeroVector;
}

FMatrix FCELootPointsViewportClient::GetWidgetCoordSystem() const
{
    if (SelectedItemIndex != INDEX_NONE && PreviewScenePtr) {
        UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedItemIndex);
        if (Comp && IsValid(Comp)) {
            if (AActor* ChildActor = Comp->GetChildActor()) {
                if (IsValid(ChildActor)) {
                    if (USceneComponent* RootComp = ChildActor->GetRootComponent()) {
                        if (IsValid(RootComp)) { return FRotationMatrix::Make(RootComp->GetComponentQuat()); }
                    }
                    return FRotationMatrix::Make(ChildActor->GetActorQuat()); // Fallback to Actor rotation
                }
            }
            // Fallback to Component rotation if ChildActor invalid or not found
            return FRotationMatrix::Make(Comp->GetComponentQuat());
        }
    }
    // Default matrix if nothing valid is selected
    return FMatrix::Identity;
}

FBoxSphereBounds FCELootPointsViewportClient::GetAllChildrenBounds() const
{
    FBoxSphereBounds Bounds(ForceInit);

    if (PreviewScenePtr)
    {
        const TArray<ACELootPoint*>& LootPointActors = PreviewScenePtr->GetLootPointActors();

        for (ACELootPoint* LootPoint : LootPointActors)
        {
            if (LootPoint && IsValid(LootPoint))
            {
                FBoxSphereBounds ActorBounds = LootPoint->GetComponentsBoundingBox(true);
                if (ActorBounds.SphereRadius > 0.0f)
                {
                    if (Bounds.SphereRadius > 0.0f)
                    {
                        Bounds = Bounds + ActorBounds;
                    }
                    else
                    {
                        Bounds = ActorBounds;
                    }
                }
            }
        }
    }

    // If no valid bounds were found, create a default bounds at the origin
    if (Bounds.SphereRadius <= 0.0f)
    {
        Bounds = FBoxSphereBounds(FVector::ZeroVector, FVector(100.0f), 100.0f);
    }

    return Bounds;
}

#undef LOCTEXT_NAMESPACE
