// CECategoryAssetActions.cpp
#include "CEType/CECategoryAssetActions.h"

FCECategoryAssetActions::FCECategoryAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FCECategoryAssetActions::GetName() const
{
    return FText::FromString(TEXT("CE Category"));
}

FColor FCECategoryAssetActions::GetTypeColor() const
{
    // Use a distinctive color for CE Category assets
    return FColor(0, 200, 100); // Green
}

UClass* FCECategoryAssetActions::GetSupportedClass() const
{
    return UCECategory::StaticClass();
}

uint32 FCECategoryAssetActions::GetCategories()
{
    return AssetCategory;
}
