// CECategoryFactory.cpp
#include "CEType/CECategoryFactory.h"
#include "CEType/CECategory.h"

UCECategoryFactory::UCECategoryFactory()
{
    SupportedClass = UCECategory::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UCECategoryFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new CE Category asset
    UCECategory* NewAsset = NewObject<UCECategory>(InParent, Class, Name, Flags);
    return NewAsset;
}

bool UCECategoryFactory::ShouldShowInNewMenu() const
{
    return true;
}
