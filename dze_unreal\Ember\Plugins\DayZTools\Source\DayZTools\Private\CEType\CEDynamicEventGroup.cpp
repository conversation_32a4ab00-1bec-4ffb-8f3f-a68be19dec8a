// CEDynamicEventGroup.cpp
#include "CEType/CEDynamicEventGroup.h"

UCEDynamicEventGroup::UCEDynamicEventGroup(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Initialize with default values
    GroupName = TEXT("NewEventGroup");

    // Add a default child
    FCEDynamicEventGroupChild DefaultChild;
    Children.Add(DefaultChild);
}

FString UCEDynamicEventGroup::ExportToXML() const
{
    FString XMLOutput;

    // Start group tag with name (use group name or asset name if empty)
    XMLOutput += FString::Printf(TEXT("<group name=\"%s\">\n"), *GetName());

    // Add children if any
    if (Children.Num() > 0)
    {
        for (const FCEDynamicEventGroupChild& Child : Children)
        {
            // Get the type name from the ConfigClass asset
            FString TypeName = "NULL";

            if (!Child.Type.IsNull())
            {
                // Use the ConfigClass asset name directly
                TypeName = Child.Type.GetAssetName();
            }

            // Format the child XML tag with proper indentation (8 spaces)
            XMLOutput += FString::Printf(TEXT("        <child type=\"%s\" deloot=\"%d\" lootmax=\"%d\" lootmin=\"%d\" x=\"%.3f\" z=\"%.3f\" a=\"%.3f\" y=\"%.3f\"/>\n"),
                *TypeName,
                Child.bDeloot ? 1 : 0,
                Child.LootMax,
                Child.LootMin,
                Child.Y / 100.0f,
                Child.X / 100.0f,
                Child.A,
                Child.Z / 100.0f);
        }
    }

    // Close the group tag with indentation matching the example
    XMLOutput += TEXT("    </group>");

    return XMLOutput;
}
