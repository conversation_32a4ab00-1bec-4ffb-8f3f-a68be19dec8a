// CEDynamicEventGroupAssetActions.cpp
#include "CEType/CEDynamicEventGroupAssetActions.h"
#include "CEType/CEDynamicEventGroupEditorToolkit.h"

FCEDynamicEventGroupAssetActions::FCEDynamicEventGroupAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FCEDynamicEventGroupAssetActions::GetName() const
{
    return FText::FromString(TEXT("CE Dynamic Event Group"));
}

FColor FCEDynamicEventGroupAssetActions::GetTypeColor() const
{
    // Use a distinctive color for CE Dynamic Event Group assets
    return FColor(180, 80, 200); // Purple
}

UClass* FCEDynamicEventGroupAssetActions::GetSupportedClass() const
{
    return UCEDynamicEventGroup::StaticClass();
}

uint32 FCEDynamicEventGroupAssetActions::GetCategories()
{
    return AssetCategory;
}

void FCEDynamicEventGroupAssetActions::OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor)
{
    const EToolkitMode::Type Mode = EditWithinLevelEditor.IsValid() ? EToolkitMode::WorldCentric : EToolkitMode::Standalone;

    for (UObject* Object : InObjects)
    {
        UCEDynamicEventGroup* EventGroupAsset = Cast<UCEDynamicEventGroup>(Object);
        if (EventGroupAsset)
        {
            TSharedRef<FCEDynamicEventGroupEditorToolkit> NewEditor = MakeShareable(new FCEDynamicEventGroupEditorToolkit());
            NewEditor->InitCEDynamicEventGroupEditorToolkit(Mode, EditWithinLevelEditor, EventGroupAsset);
        }
    }
}
