// CEDynamicEventGroupChildCustomization.cpp
#include "CEType/CEDynamicEventGroupChildCustomization.h"
#include "CEType/CEDynamicEventGroupChildProxy.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"

TSharedRef<IDetailCustomization> FCEDynamicEventGroupChildCustomization::MakeInstance()
{
    return MakeShareable(new FCEDynamicEventGroupChildCustomization);
}

void FCEDynamicEventGroupChildCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // Get the currently selected objects
    TArray<TWeakObjectPtr<UObject>> SelectedObjects;
    DetailBuilder.GetObjectsBeingCustomized(SelectedObjects);

    if (SelectedObjects.Num() != 1)
    {
        return;
    }

    UCEDynamicEventGroupChildProxy* ChildProxy = Cast<UCEDynamicEventGroupChildProxy>(SelectedObjects[0].Get());
    if (!ChildProxy)
    {
        return;
    }

    // Get the Transform category and set its sort order to appear at the top
    IDetailCategoryBuilder& TransformCategory = DetailBuilder.EditCategory("Transform");
    TransformCategory.SetSortOrder(0);

    // Get the Child Settings category and set its sort order to appear after Transform
    IDetailCategoryBuilder& ChildSettingsCategory = DetailBuilder.EditCategory("Child Settings");
    ChildSettingsCategory.SetSortOrder(1);
}
