// CEDynamicEventGroupChildProxy.cpp
#include "CEType/CEDynamicEventGroupChildProxy.h"

UCEDynamicEventGroupChildProxy::UCEDynamicEventGroupChildProxy()
    : b<PERSON><PERSON>ot(false)
    , LootMin(1)
    , LootMax(3)
    , X(0.0f)
    , Z(0.0f)
    , Y(0.0f)
    , A(0.0f)
    , <PERSON><PERSON><PERSON><PERSON>(nullptr)
    , ChildIndex(-1)
{
}

void UCEDynamicEventGroupChildProxy::Initialize(UCEDynamicEventGroup* InParentAsset, int32 InChildIndex)
{
    ParentAsset = InParentAsset;
    ChildIndex = InChildIndex;

    // Sync properties from the child struct
    SyncFromChild();
}

void UCEDynamicEventGroupChildProxy::SyncFromChild()
{
    if (ParentAsset && ParentAsset->Children.IsValidIndex(ChildIndex))
    {
        const FCEDynamicEventGroupChild& Child = ParentAsset->Children[ChildIndex];

        // Copy properties from the child struct to the proxy - keeping "Type" naming
        Type = Child.Type;
        bDeloot = Child.bDeloot;
        LootMin = Child.LootMin;
        LootMax = Child.LootMax;
        X = Child.X;
        Z = Child.Z;
        Y = Child.Y;
        A = Child.A;
    }
}

void UCEDynamicEventGroupChildProxy::SyncToChild()
{
    if (ParentAsset && ParentAsset->Children.IsValidIndex(ChildIndex))
    {
        // Mark the parent asset as modified
        ParentAsset->Modify();

        // Copy properties from the proxy to the child struct - keeping "Type" naming
        FCEDynamicEventGroupChild& Child = ParentAsset->Children[ChildIndex];
        Child.Type = Type;
        Child.bDeloot = bDeloot;
        Child.LootMin = LootMin;
        Child.LootMax = LootMax;
        Child.X = X;
        Child.Z = Z;
        Child.Y = Y;
        Child.A = A;
    }
}

// Get the P3D model from the ConfigClass
UP3DBlueprint* UCEDynamicEventGroupChildProxy::GetModel() const
{
    if (!Type.IsNull())
    {
        UConfigClass* LoadedConfigClass = Type.LoadSynchronous();
        if (LoadedConfigClass && !LoadedConfigClass->Model.IsNull())
        {
            return LoadedConfigClass->Model.LoadSynchronous();
        }
    }
    return nullptr;
}

// Flag to prevent recursive property change notifications
static bool bIsProcessingPropertyChange = false;

void UCEDynamicEventGroupChildProxy::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    // Check if we're already processing a property change
    if (bIsProcessingPropertyChange)
    {
        UE_LOG(LogTemp, Display, TEXT("PostEditChangeProperty: Already processing a property change, skipping"));
        return;
    }

    // Set the flag to prevent recursive calls
    bIsProcessingPropertyChange = true;

    Super::PostEditChangeProperty(PropertyChangedEvent);

    // Get the name of the property that was changed
    FName PropertyName = (PropertyChangedEvent.Property != nullptr) ? PropertyChangedEvent.Property->GetFName() : NAME_None;

    // Sync changes back to the child struct
    SyncToChild();

    // Log for debugging
    UE_LOG(LogTemp, Display, TEXT("Child proxy property changed: %s"),
        PropertyChangedEvent.Property ? *PropertyChangedEvent.Property->GetName() : TEXT("Unknown"));

    // Reset the flag
    bIsProcessingPropertyChange = false;
}
