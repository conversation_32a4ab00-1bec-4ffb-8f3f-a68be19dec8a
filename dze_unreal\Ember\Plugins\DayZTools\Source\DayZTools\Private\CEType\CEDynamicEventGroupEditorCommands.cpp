// CEDynamicEventGroupEditorCommands.cpp
#include "CEType/CEDynamicEventGroupEditorCommands.h"

#define LOCTEXT_NAMESPACE "CEDynamicEventGroupEditor"

void FCEDynamicEventGroupEditorCommands::RegisterCommands()
{
    UI_COMMAND(ToggleGroundPlane, "Toggle Ground", "Toggle the visibility of the ground plane", EUserInterfaceActionType::ToggleButton, FInputChord());
    UI_COMMAND(ExportEventGroup, "Export", "Export the event group to XML", EUserInterfaceActionType::Button, FInputChord());
}

#undef LOCTEXT_NAMESPACE
