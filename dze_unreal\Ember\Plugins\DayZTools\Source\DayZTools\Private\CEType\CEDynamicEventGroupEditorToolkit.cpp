// CEDynamicEventGroupEditorToolkit.cpp
#include "CEType/CEDynamicEventGroupEditorToolkit.h"
#include "CEType/CEDynamicEventGroupEditorViewport.h"
#include "CEType/CEDynamicEventGroupPreviewScene.h"
#include "CEType/CEDynamicEventGroupHierarchy.h"
#include "CEType/CEDynamicEventGroupChildProxy.h"
#include "CEType/CEDynamicEventGroupEditorCommands.h"
#include "Widgets/Docking/SDockTab.h"
#include "PropertyEditorModule.h"
#include "EditorStyleSet.h"  // For FEditorStyle
#include "IDetailsView.h"
#include "Modules/ModuleManager.h"
#include "EditorStyleSet.h"
#include "Widgets/Images/SImage.h" // Needed for FSlateIcon
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Framework/MultiBox/MultiBoxExtender.h"
#include "DesktopPlatformModule.h"
#include "IDesktopPlatform.h"
#include "Misc/FileHelper.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Internationalization/Text.h" // Ensure FText is known

#define LOCTEXT_NAMESPACE "CEDynamicEventGroupEditor"

const FName FCEDynamicEventGroupEditorToolkit::ViewportTabID(TEXT("CEDynamicEventGroupEditor_Viewport"));
const FName FCEDynamicEventGroupEditorToolkit::DetailsTabID(TEXT("CEDynamicEventGroupEditor_Details"));
const FName FCEDynamicEventGroupEditorToolkit::HierarchyTabID(TEXT("CEDynamicEventGroupEditor_Hierarchy"));

FCEDynamicEventGroupEditorToolkit::FCEDynamicEventGroupEditorToolkit()
    : EventGroupAsset(nullptr), SelectedChildIndex(-1), SelectedChildProxy(nullptr)
{
}

FCEDynamicEventGroupEditorToolkit::~FCEDynamicEventGroupEditorToolkit()
{
    // Unregister from property changes
    FCoreUObjectDelegates::OnObjectPropertyChanged.RemoveAll(this);

    // Clear the details view to avoid dangling references
    if (DetailsView.IsValid())
    {
        DetailsView->SetObject(nullptr);
    }

    // Clean up the proxy object
    if (SelectedChildProxy)
    {
        SelectedChildProxy->RemoveFromRoot();
        SelectedChildProxy = nullptr;
    }
}



// Method to handle transform updates from the viewport
void FCEDynamicEventGroupEditorToolkit::HandleTransformUpdate(int32 ChildIndex, const FTransform& NewTransform)
{
    // Check if this is the currently selected child
    if (ChildIndex == SelectedChildIndex && SelectedChildProxy)
    {
        // Update the proxy object with the new transform values
        // Convert from Unreal coordinates to DayZ coordinates (divide by 100 to convert from cm to m)
        SelectedChildProxy->X = NewTransform.GetLocation().X / 100.0f;
        SelectedChildProxy->Z = NewTransform.GetLocation().Y / 100.0f;
        SelectedChildProxy->Y = NewTransform.GetLocation().Z / 100.0f;
        SelectedChildProxy->A = NewTransform.GetRotation().Rotator().Yaw;

        // Force the details panel to refresh
        if (DetailsView.IsValid())
        {
            DetailsView->ForceRefresh();
        }

        UE_LOG(LogTemp, Display, TEXT("HandleTransformUpdate: Updated proxy for child %d with transform %s"),
            ChildIndex, *NewTransform.ToString());
    }
}


void FCEDynamicEventGroupEditorToolkit::InitCEDynamicEventGroupEditorToolkit(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UCEDynamicEventGroup* InEventGroupAsset)
{
    EventGroupAsset = InEventGroupAsset;

    // --- Call these BEFORE InitAssetEditor ---
    // Bind our custom commands to the toolkit's command list
    BindCommands();

    // Register the extender that adds our custom buttons to the toolbar
    RegisterToolbarExtender();
    // ---

    // Initialize the editor layout
    TSharedRef<FTabManager::FLayout> Layout = CreateEditorLayout();

    // Initialize the asset editor toolkit
    const bool bCreateDefaultStandaloneMenu = true;
    const bool bCreateDefaultToolbar = true; // IMPORTANT: Must be true
    InitAssetEditor(Mode, InitToolkitHost, FName("CEDynamicEventGroupEditor"), Layout, bCreateDefaultStandaloneMenu, bCreateDefaultToolbar, EventGroupAsset);

    // --- Call these AFTER InitAssetEditor ---
    // Set the asset being edited
    if (GEditor && EventGroupAsset) // Added null check for GEditor
    {
        GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->NotifyAssetOpened(EventGroupAsset, this);
    }

    // Register for property changes
    FCoreUObjectDelegates::OnObjectPropertyChanged.AddRaw(this, &FCEDynamicEventGroupEditorToolkit::OnPropertyChanged);

    // Regenerate menus and toolbars to make sure extenders are applied
    RegenerateMenusAndToolbars();
    // ---
}

FName FCEDynamicEventGroupEditorToolkit::GetToolkitFName() const
{
    return FName("CEDynamicEventGroupEditor");
}

FText FCEDynamicEventGroupEditorToolkit::GetBaseToolkitName() const
{
    return LOCTEXT("AppLabel", "CE Dynamic Event Group Editor");
}

FString FCEDynamicEventGroupEditorToolkit::GetWorldCentricTabPrefix() const
{
    return LOCTEXT("WorldCentricTabPrefix", "CE Dynamic Event Group ").ToString();
}

FLinearColor FCEDynamicEventGroupEditorToolkit::GetWorldCentricTabColorScale() const
{
    return FLinearColor(0.7f, 0.3f, 0.7f, 0.5f); // Purple
}

void FCEDynamicEventGroupEditorToolkit::RegisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    WorkspaceMenuCategory = InTabManager->AddLocalWorkspaceMenuCategory(LOCTEXT("WorkspaceMenu_CEDynamicEventGroupEditor", "CE Dynamic Event Group Editor"));
    auto WorkspaceMenuCategoryRef = WorkspaceMenuCategory.ToSharedRef();

    FAssetEditorToolkit::RegisterTabSpawners(InTabManager);

    // Register the viewport tab
    InTabManager->RegisterTabSpawner(ViewportTabID, FOnSpawnTab::CreateSP(this, &FCEDynamicEventGroupEditorToolkit::SpawnViewportTab))
        .SetDisplayName(LOCTEXT("ViewportTab", "Viewport"))
        .SetGroup(WorkspaceMenuCategoryRef)
        .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Viewports"));

    // Register the details tab
    InTabManager->RegisterTabSpawner(DetailsTabID, FOnSpawnTab::CreateSP(this, &FCEDynamicEventGroupEditorToolkit::SpawnDetailsTab))
        .SetDisplayName(LOCTEXT("DetailsTab", "Details"))
        .SetGroup(WorkspaceMenuCategoryRef)
        .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Details"));

    // Register the hierarchy tab
    InTabManager->RegisterTabSpawner(HierarchyTabID, FOnSpawnTab::CreateSP(this, &FCEDynamicEventGroupEditorToolkit::SpawnHierarchyTab))
        .SetDisplayName(LOCTEXT("HierarchyTab", "Hierarchy"))
        .SetGroup(WorkspaceMenuCategoryRef)
        .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Outliner"));
}

void FCEDynamicEventGroupEditorToolkit::UnregisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::UnregisterTabSpawners(InTabManager);

    InTabManager->UnregisterTabSpawner(ViewportTabID);
    InTabManager->UnregisterTabSpawner(DetailsTabID);
    InTabManager->UnregisterTabSpawner(HierarchyTabID);
}

TSharedRef<FTabManager::FLayout> FCEDynamicEventGroupEditorToolkit::CreateEditorLayout()
{
    return FTabManager::NewLayout("Standalone_CEDynamicEventGroupEditor_Layout_v2")
        ->AddArea
        (
            FTabManager::NewPrimaryArea()->SetOrientation(Orient_Horizontal)
            ->Split
            (
                FTabManager::NewSplitter()->SetOrientation(Orient_Horizontal)
                ->SetSizeCoefficient(0.2f)
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(1.0f)
                    ->AddTab(HierarchyTabID, ETabState::OpenedTab)
                )
            )
            ->Split
            (
                FTabManager::NewSplitter()->SetOrientation(Orient_Horizontal)
                ->SetSizeCoefficient(0.6f)
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(1.0f)
                    ->AddTab(ViewportTabID, ETabState::OpenedTab)
                )
            )
            ->Split
            (
                FTabManager::NewSplitter()->SetOrientation(Orient_Horizontal)
                ->SetSizeCoefficient(0.2f)
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(1.0f)
                    ->AddTab(DetailsTabID, ETabState::OpenedTab)
                )
            )
        );
}

TSharedRef<SDockTab> FCEDynamicEventGroupEditorToolkit::SpawnViewportTab(const FSpawnTabArgs& Args)
{
    // Create a new advanced preview scene
    FAdvancedPreviewScene::ConstructionValues CVS;
    CVS.bCreatePhysicsScene = false;  // No physics needed
    CVS.bForceMipsResident = true;    // Better texture quality
    CVS.SkyBrightness = 1.0f;         // Standard brightness

    TSharedPtr<FCEDynamicEventGroupPreviewScene> PreviewScene = MakeShareable(new FCEDynamicEventGroupPreviewScene(CVS));

    // Set the editor toolkit reference
    PreviewScene->SetEditorToolkit(this);

    // Create the viewport widget
    ViewportWidget = SNew(SCEDynamicEventGroupEditorViewport)
        .PreviewScene(PreviewScene)
        .EventGroupAsset(EventGroupAsset)
        .EditorToolkit(this);

    // Log for debugging
    UE_LOG(LogTemp, Display, TEXT("SpawnViewportTab: Created viewport for asset %s"),
        EventGroupAsset ? *EventGroupAsset->GetName() : TEXT("NULL"));


    // Create an extender for the viewport toolbar
    TSharedPtr<FExtender> ToolbarExtender = MakeShareable(new FExtender);



    // Add the extender to the viewport
    ViewportWidget->AddViewportToolbarExtender(ToolbarExtender);

    return SNew(SDockTab)
        .Label(LOCTEXT("ViewportTitle", "Viewport"))
        [
            ViewportWidget.ToSharedRef()
        ];
}

TSharedRef<SDockTab> FCEDynamicEventGroupEditorToolkit::SpawnDetailsTab(const FSpawnTabArgs& Args)
{
    // Create a property details view
    FPropertyEditorModule& PropertyEditorModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");

    FDetailsViewArgs DetailsViewArgs;
    DetailsViewArgs.bUpdatesFromSelection = false;
    DetailsViewArgs.bLockable = false;
    DetailsViewArgs.bAllowSearch = true;
    DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
    DetailsViewArgs.bHideSelectionTip = true;
    DetailsViewArgs.bShowOptions = false;
    DetailsViewArgs.bShowModifiedPropertiesOption = false;
    DetailsViewArgs.bShowDifferingPropertiesOption = false;
    DetailsViewArgs.bAllowMultipleTopLevelObjects = false;
    DetailsViewArgs.bShowPropertyMatrixButton = false;

    DetailsView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);

    // Initially, no child is selected, so the details panel is empty
    DetailsView->SetObject(nullptr);

    return SNew(SDockTab)
        .Label(LOCTEXT("DetailsTitle", "Details"))
        [
            DetailsView.ToSharedRef()
        ];
}

TSharedRef<SDockTab> FCEDynamicEventGroupEditorToolkit::SpawnHierarchyTab(const FSpawnTabArgs& Args)
{
    // Create the hierarchy widget
    HierarchyWidget = SNew(SCEDynamicEventGroupHierarchy)
        .EventGroupAsset(EventGroupAsset);

    // Set the editor toolkit
    HierarchyWidget->SetEditorToolkit(this);

    // Log for debugging
    UE_LOG(LogTemp, Display, TEXT("SpawnHierarchyTab: Created hierarchy for asset %s"),
        EventGroupAsset ? *EventGroupAsset->GetName() : TEXT("NULL"));

    // Refresh the hierarchy
    HierarchyWidget->RefreshHierarchy();

    return SNew(SDockTab)
        .Label(LOCTEXT("HierarchyTitle", "Hierarchy"))
        [
            HierarchyWidget.ToSharedRef()
        ];
}

void FCEDynamicEventGroupEditorToolkit::CreateChildProxy(int32 ChildIndex)
{
    // Clean up any existing proxy
    if (SelectedChildProxy)
    {
        // Make sure we clear any references before removing from root
        if (DetailsView.IsValid())
        {
            DetailsView->SetObject(nullptr);
        }

        SelectedChildProxy->RemoveFromRoot();
        SelectedChildProxy = nullptr;
    }

    // Create a new proxy if we have a valid child index
    if (EventGroupAsset && ChildIndex >= 0 && ChildIndex < EventGroupAsset->Children.Num())
    {
        // Create a new proxy object
        SelectedChildProxy = NewObject<UCEDynamicEventGroupChildProxy>(GetTransientPackage(), NAME_None);
        SelectedChildProxy->AddToRoot(); // Prevent garbage collection
        SelectedChildProxy->Initialize(EventGroupAsset, ChildIndex);

        UE_LOG(LogTemp, Display, TEXT("Created proxy for child %d"), ChildIndex);
    }
}

void FCEDynamicEventGroupEditorToolkit::UpdateDetailsView()
{
    if (DetailsView.IsValid())
    {
        // Set the object in the details view
        DetailsView->SetObject(SelectedChildProxy);

        UE_LOG(LogTemp, Display, TEXT("Updated details view with %s"),
            SelectedChildProxy ? *SelectedChildProxy->GetName() : TEXT("nullptr"));
    }
}

void FCEDynamicEventGroupEditorToolkit::SelectChild(int32 ChildIndex)
{
    // First update the details view to null to avoid any dangling references
    if (DetailsView.IsValid())
    {
        DetailsView->SetObject(nullptr);
        UE_LOG(LogTemp, Display, TEXT("Cleared details view before selection change"));
    }

    // Update the selected index
    SelectedChildIndex = ChildIndex;

    // Update the hierarchy widget selection
    if (HierarchyWidget.IsValid())
    {
        HierarchyWidget->SetSelectedChildIndex(ChildIndex);
    }

    // Create a proxy object for the selected child and update the details view
    CreateChildProxy(ChildIndex);
    UpdateDetailsView();

    // Update the viewport selection to highlight the selected child
    // Note: We're not calling UpdatePreview() here, just updating the selection
    if (ViewportWidget.IsValid())
    {
        // Get the viewport client and update its selection
        TSharedPtr<FCEDynamicEventGroupViewportClient> ViewportClient =
            StaticCastSharedPtr<SCEDynamicEventGroupEditorViewport>(ViewportWidget)->GetViewportClient();

        if (ViewportClient.IsValid())
        {
            ViewportClient->SetSelectedChildIndex(ChildIndex);
            ViewportClient->Invalidate();
        }
    }
}

void FCEDynamicEventGroupEditorToolkit::RefreshEditor()
{
    // Refresh the hierarchy widget
    if (HierarchyWidget.IsValid())
    {
        HierarchyWidget->RefreshHierarchy();
    }

    // Refresh the viewport
    if (ViewportWidget.IsValid())
    {
        ViewportWidget->UpdatePreview();
    }
}

// Flag to prevent recursive property change handling
static bool bIsHandlingPropertyChange = false;

void FCEDynamicEventGroupEditorToolkit::OnPropertyChanged(UObject* ObjectBeingModified, FPropertyChangedEvent& PropertyChangedEvent)
{
    // Check if we're already handling a property change
    if (bIsHandlingPropertyChange)
    {
        UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: Already handling a property change, skipping"));
        return;
    }

    // Set the flag to prevent recursive calls
    bIsHandlingPropertyChange = true;

    // Check if the modified object is our asset
    if (ObjectBeingModified == EventGroupAsset)
    {
        // Get the name of the property that was changed
        FName PropertyName = (PropertyChangedEvent.Property != nullptr) ? PropertyChangedEvent.Property->GetFName() : NAME_None;

        // Log property changes for debugging
        UE_LOG(LogTemp, Display, TEXT("Asset property changed: %s"), *PropertyName.ToString());

        // If the children properties changed, update the editor
        if (PropertyName == GET_MEMBER_NAME_CHECKED(UCEDynamicEventGroup, Children))
        {
            RefreshEditor();

            // If we have a selected child, update the proxy
            if (SelectedChildProxy && SelectedChildIndex >= 0 && SelectedChildIndex < EventGroupAsset->Children.Num())
            {
                SelectedChildProxy->SyncFromChild();
            }

            UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: Refreshed editor"));
        }
    }
    // Check if the modified object is our proxy
    else if (ObjectBeingModified == SelectedChildProxy)
    {
        // Get the name of the property that was changed
        FName PropertyName = (PropertyChangedEvent.Property != nullptr) ? PropertyChangedEvent.Property->GetFName() : NAME_None;

        // Log property changes for debugging
        UE_LOG(LogTemp, Display, TEXT("Proxy property changed: %s"), *PropertyName.ToString());

        if (PropertyName == GET_MEMBER_NAME_CHECKED(UCEDynamicEventGroupChildProxy, Type))
        {
            UE_LOG(LogTemp, Display, TEXT("P3D asset reference changed, updating preview"));

            // Store the current selection
            int32 CurrentSelectedIndex = SelectedChildIndex;

            // Make sure the change is synced to the asset immediately
            if (SelectedChildProxy)
            {
                SelectedChildProxy->SyncToChild();

                // Refresh the hierarchy to show the updated type
                if (HierarchyWidget.IsValid())
                {
                    HierarchyWidget->RefreshHierarchy();
                }
            }

            // Update the viewport to reflect the changes
            if (ViewportWidget.IsValid())
            {
                // The UpdatePreview method will handle preserving the selection
                ViewportWidget->UpdatePreview();

                UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: Updated preview with selection preservation"));
            }

            // Ensure the selection is preserved after the update
            if (CurrentSelectedIndex >= 0 && CurrentSelectedIndex < EventGroupAsset->Children.Num())
            {
                // Force re-selection after a short delay to ensure it happens after any deselection
                FTimerHandle TimerHandle;
                GEditor->GetTimerManager()->SetTimer(TimerHandle, [this, CurrentSelectedIndex]()
                {
                    SelectChild(CurrentSelectedIndex);
                    UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: Force re-selected child index %d after Type change"), CurrentSelectedIndex);
                }, 0.01f, false);
            }
        }
        // Check if any transform property changed
        else if (PropertyName == GET_MEMBER_NAME_CHECKED(UCEDynamicEventGroupChildProxy, X) ||
                 PropertyName == GET_MEMBER_NAME_CHECKED(UCEDynamicEventGroupChildProxy, Y) ||
                 PropertyName == GET_MEMBER_NAME_CHECKED(UCEDynamicEventGroupChildProxy, Z) ||
                 PropertyName == GET_MEMBER_NAME_CHECKED(UCEDynamicEventGroupChildProxy, A))
        {
            UE_LOG(LogTemp, Display, TEXT("Transform property changed, updating component transform"));

            // Sync changes to the asset
            if (SelectedChildProxy)
            {
                SelectedChildProxy->SyncToChild();

                // Update the component transform in the viewport
                if (ViewportWidget.IsValid())
                {
                    TSharedPtr<FCEDynamicEventGroupPreviewScene> PreviewScene =
                        StaticCastSharedPtr<SCEDynamicEventGroupEditorViewport>(ViewportWidget)->GetPreviewScene();

                    if (PreviewScene.IsValid() && SelectedChildIndex >= 0)
                    {
                        // Convert from DayZ coordinates to Unreal coordinates (multiply by 100 to convert from m to cm)
                        FVector Location(
                            SelectedChildProxy->X * 100.0f,
                            SelectedChildProxy->Z * 100.0f,
                            SelectedChildProxy->Y * 100.0f
                        );

                        FRotator Rotation(0.0f, SelectedChildProxy->A, 0.0f);

                        FTransform NewTransform;
                        NewTransform.SetLocation(Location);
                        NewTransform.SetRotation(Rotation.Quaternion());

                        // Update the component transform
                        PreviewScene->UpdateChildTransform(SelectedChildIndex, NewTransform);

                        // Invalidate the viewport to reflect the changes
                        TSharedPtr<FCEDynamicEventGroupViewportClient> ViewportClient =
                            StaticCastSharedPtr<SCEDynamicEventGroupEditorViewport>(ViewportWidget)->GetViewportClient();

                        if (ViewportClient.IsValid())
                        {
                            ViewportClient->Invalidate();
                        }
                    }
                }
            }
        }
        else
        {
            // For other property changes, sync to the asset and update the viewport
            if (SelectedChildProxy)
            {
                SelectedChildProxy->SyncToChild();
            }

            // Store the current selection
            int32 CurrentSelectedIndex = SelectedChildIndex;

            // Update the viewport to reflect the changes
            if (ViewportWidget.IsValid())
            {
                // The UpdatePreview method will handle preserving the selection
                ViewportWidget->UpdatePreview();

                UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: Updated preview with selection preservation"));
            }

            // Ensure the selection is preserved after the update
            if (CurrentSelectedIndex >= 0 && CurrentSelectedIndex < EventGroupAsset->Children.Num())
            {
                // Force re-selection after a short delay to ensure it happens after any deselection
                FTimerHandle TimerHandle;
                GEditor->GetTimerManager()->SetTimer(TimerHandle, [this, CurrentSelectedIndex]()
                {
                    SelectChild(CurrentSelectedIndex);
                    UE_LOG(LogTemp, Display, TEXT("OnPropertyChanged: Force re-selected child index %d after property change"), CurrentSelectedIndex);
                }, 0.01f, false);
            }
        }
    }

    // Reset the flag
    bIsHandlingPropertyChange = false;
}

// --- Toolbar Button Handlers ---

void FCEDynamicEventGroupEditorToolkit::HandleToggleGroundPlane()
{
    if (ViewportWidget.IsValid())
    {
        ViewportWidget->ToggleGroundPlaneVisibility(); // Call the function on the viewport instance
    }
}

bool FCEDynamicEventGroupEditorToolkit::IsGroundPlaneVisible() const
{
    if (ViewportWidget.IsValid())
    {
        // **IMPORTANT**: Ensure SCEDynamicEventGroupEditorViewport has this function:
        // bool SCEDynamicEventGroupEditorViewport::IsGroundPlaneVisible() const { return bShowGroundPlane; }
        return ViewportWidget->IsGroundPlaneVisible();
    }
    return false;
}

void FCEDynamicEventGroupEditorToolkit::ExportEventGroup()
{
    if (!EventGroupAsset)
    {
        UE_LOG(LogTemp, Warning, TEXT("Cannot export: No event group asset loaded"));
        return;
    }

    IDesktopPlatform* DesktopPlatform = FDesktopPlatformModule::Get();
    if (!DesktopPlatform)
    {
        UE_LOG(LogTemp, Error, TEXT("Cannot export: Failed to get Desktop Platform"));
        return;
    }

    TArray<FString> SaveFilenames;
    FString DefaultPath = FPaths::ProjectSavedDir(); // Or a more specific default path
    FString DefaultFile = EventGroupAsset->GetName() + TEXT(".xml");
    const FString FileTypes = TEXT("XML Files (*.xml)|*.xml");

    bool bSaved = DesktopPlatform->SaveFileDialog(
        FSlateApplication::Get().FindBestParentWindowHandleForDialogs(nullptr),
        LOCTEXT("ExportDialogTitle", "Export Event Group to XML").ToString(),
        DefaultPath,
        DefaultFile,
        FileTypes,
        EFileDialogFlags::None, // Use ::None or ::OverwritePrompt
        SaveFilenames
    );

    if (bSaved && SaveFilenames.Num() > 0)
    {
        const FString FilePath = SaveFilenames[0];
        FString XmlContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n");
        XmlContent += TEXT("<eventposdef>\n");

        for (const FCEDynamicEventGroupChild& Child : EventGroupAsset->Children)
        {
            // Correct indentation
            XmlContent += TEXT("\t<event>\n");
            // Position uses DayZ coordinates directly from asset
            XmlContent += FString::Printf(TEXT("\t\t<pos x=\"%f\" z=\"%f\" y=\"%f\" a=\"%f\"/>\n"), Child.X, Child.Z, Child.Y, Child.A);
            // Type name if valid
            if (Child.Type)
            {
                // Make sure Type->GetName() returns a usable string (e.g., class name without prefix)
                // You might need a helper function or store the desired name in the UObject itself
                FString TypeName = Child.Type->GetName(); // Adjust if needed
                XmlContent += FString::Printf(TEXT("\t\t<type name=\"%s\"/>\n"), *TypeName);
            }
            // Other properties
            XmlContent += FString::Printf(TEXT("\t\t<deloot>%d</deloot>\n"), Child.bDeloot ? 1 : 0);
            XmlContent += FString::Printf(TEXT("\t\t<lootmin>%d</lootmin>\n"), Child.LootMin);
            XmlContent += FString::Printf(TEXT("\t\t<lootmax>%d</lootmax>\n"), Child.LootMax);
            XmlContent += TEXT("\t</event>\n");
        }
        XmlContent += TEXT("</eventposdef>");

        // Save the XML string to the file
        if (FFileHelper::SaveStringToFile(XmlContent, *FilePath))
        {
            FNotificationInfo Info(FText::Format(LOCTEXT("ExportSuccessNotify", "Event Group exported successfully to {0}"), FText::FromString(FilePath)));
            Info.ExpireDuration = 5.0f;
            FSlateNotificationManager::Get().AddNotification(Info);
            UE_LOG(LogTemp, Display, TEXT("Event Group exported to %s"), *FilePath);
        }
        else
        {
            FNotificationInfo Info(FText::Format(LOCTEXT("ExportFailNotify", "Failed to export Event Group to {0}"), FText::FromString(FilePath)));
            Info.ExpireDuration = 5.0f;
            Info.Hyperlink = FSimpleDelegate::CreateLambda([]() { FPlatformProcess::ExploreFolder(*FPaths::GetPath(FString(""))); }); // Example hyperlink
            Info.HyperlinkText = LOCTEXT("ShowInExplorer", "Show Containing Folder");
            FSlateNotificationManager::Get().AddNotification(Info);
            UE_LOG(LogTemp, Error, TEXT("Failed to export Event Group to %s"), *FilePath);
        }
    }
}

// --- Command and Toolbar Setup ---

void FCEDynamicEventGroupEditorToolkit::BindCommands()
{
    // Ensure the toolkit's command list is valid
    const TSharedRef<FUICommandList> Commands = GetToolkitCommands();

    // Use the correct command class you defined earlier
    const FCEDynamicEventGroupEditorViewportCommands& ViewportCommands = FCEDynamicEventGroupEditorViewportCommands::Get();

    // Map Export command
    Commands->MapAction(
        ViewportCommands.ExportEventGroup,
        FExecuteAction::CreateSP(this, &FCEDynamicEventGroupEditorToolkit::ExportEventGroup),
        FCanExecuteAction() // Add CanExecute if needed (e.g., return EventGroupAsset != nullptr;)
    );

    // Map Toggle Ground Plane command
    Commands->MapAction(
        ViewportCommands.ToggleGroundPlane,
        FExecuteAction::CreateSP(this, &FCEDynamicEventGroupEditorToolkit::HandleToggleGroundPlane),
        FCanExecuteAction::CreateLambda([this] { return ViewportWidget.IsValid(); }), // Only enable if viewport exists
        FIsActionChecked::CreateSP(this, &FCEDynamicEventGroupEditorToolkit::IsGroundPlaneVisible)
    );
}

void FCEDynamicEventGroupEditorToolkit::RegisterToolbarExtender()
{
    TSharedPtr<FExtender> ToolbarExtender = MakeShareable(new FExtender);

    ToolbarExtender->AddToolBarExtension(
        "Asset", // Hook into the 'Asset' section (standard hook)
        EExtensionHook::After, // Add after existing 'Asset' items
        GetToolkitCommands(), // Use the toolkit's command list
        FToolBarExtensionDelegate::CreateSP(this, &FCEDynamicEventGroupEditorToolkit::BuildToolbar) // Delegate to build the buttons
    );

    AddToolbarExtender(ToolbarExtender); // Add the extender to the toolkit's manager
}

void FCEDynamicEventGroupEditorToolkit::BuildToolbar(FToolBarBuilder& ToolBarBuilder)
{
    const FCEDynamicEventGroupEditorViewportCommands& ViewportCommands = FCEDynamicEventGroupEditorViewportCommands::Get();

    ToolBarBuilder.BeginSection("CustomAssetTools");
    {
        // Try with fully qualified namespace and explicit style reference
        ToolBarBuilder.AddToolBarButton(
            ViewportCommands.ExportEventGroup,
            NAME_None,
            LOCTEXT("ExportEventGroup_Label", "Export"),
            LOCTEXT("ExportEventGroup_Tooltip", "Export Event Group"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Save")  // Direct style reference
        );

        ToolBarBuilder.AddToolBarButton(
            ViewportCommands.ToggleGroundPlane,
            NAME_None,
            LOCTEXT("ToggleGroundPlane_Label", "Show Floor"),
            LOCTEXT("ToggleGroundPlane_Tooltip", "Toggle Floor Plane Visibility"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "BlueprintEditor.ShowFloor")  // Try more common icon name
        );
    }
    ToolBarBuilder.EndSection();
}

#undef LOCTEXT_NAMESPACE
