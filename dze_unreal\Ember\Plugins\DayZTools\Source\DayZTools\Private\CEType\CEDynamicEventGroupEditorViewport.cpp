// CEDynamicEventGroupEditorViewport.cpp
#include "CEType/CEDynamicEventGroupEditorViewport.h"
#include "CEType/CEDynamicEventGroup.h"
#include "CEType/CEDynamicEventGroupEditorToolkit.h"
#include "CEType/CEDynamicEventGroupEditorViewportToolBar.h"
#include "CEType/CEDynamicEventGroupEditorViewportCommands.h"
#include "EditorViewportClient.h"
#include "SEditorViewport.h"
#include "EditorStyleSet.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"

void SCEDynamicEventGroupEditorViewport::Construct(const FArguments& InArgs)
{
    PreviewScene = InArgs._PreviewScene;
    EventGroupAsset = InArgs._EventGroupAsset;
    EditorToolkit = InArgs._EditorToolkit;

    SEditorViewport::Construct(SEditorViewport::FArguments());

    // Log for debugging
    UE_LOG(LogTemp, Display, TEXT("Construct: Constructing editor viewport for asset %s"),
        EventGroupAsset ? *EventGroupAsset->GetName() : TEXT("NULL"));

    // Update the preview scene with the current asset
    UpdatePreview();
}

TSharedRef<FEditorViewportClient> SCEDynamicEventGroupEditorViewport::MakeEditorViewportClient()
{
    // Retrieve the toolkit host from the owning editor toolkit
    TSharedPtr<IToolkitHost> ToolkitHostPtr = EditorToolkit->GetToolkitHost();
    check(ToolkitHostPtr.IsValid());

    // Instantiate our viewport client with a valid toolkit host
    ViewportClient = MakeShareable(new FCEDynamicEventGroupViewportClient(
        ToolkitHostPtr.ToSharedRef(),
        PreviewScene.Get(),
        SharedThis(this),
        EditorToolkit
    ));

    return ViewportClient.ToSharedRef();
}

TSharedPtr<SWidget> SCEDynamicEventGroupEditorViewport::MakeViewportToolbar()
{
    // Create the toolbar
    return SNew(SCEDynamicEventGroupEditorViewportToolBar, SharedThis(this))
        .Cursor(EMouseCursor::Default);
}

TSharedRef<SEditorViewport> SCEDynamicEventGroupEditorViewport::GetViewportWidget()
{
    return SharedThis(this);
}

TSharedPtr<FExtender> SCEDynamicEventGroupEditorViewport::GetExtenders() const
{
    TSharedPtr<FExtender> CombinedExtender = MakeShareable(new FExtender);

    // Add all registered extenders
    for (const TSharedPtr<FExtender>& Extender : ViewportToolbarExtenders)
    {
        if (Extender.IsValid())
        {
            TArray<TSharedPtr<FExtender>> Extenders;
            Extenders.Add(Extender);
            CombinedExtender->Combine(Extenders);
        }
    }

    return CombinedExtender;
}

bool SCEDynamicEventGroupEditorViewport::IsGroundPlaneVisible() const
{
     return bShowGroundPlane; // Or however you track visibility
}

void SCEDynamicEventGroupEditorViewport::OnFloatingButtonClicked()
{
    // Handle floating button click if needed
}

void SCEDynamicEventGroupEditorViewport::ToggleGroundPlaneVisibility()
{
    if (PreviewScene.IsValid())
    {
        bShowGroundPlane = !bShowGroundPlane;
        PreviewScene->SetFloorVisibility(bShowGroundPlane);

        if (ViewportClient.IsValid())
        {
            ViewportClient->Invalidate();
        }
    }
}

void SCEDynamicEventGroupEditorViewport::ExportEventGroup()
{
    // Forward the export request to the editor toolkit
    if (EditorToolkit)
    {
        EditorToolkit->ExportEventGroup();
    }
}

void SCEDynamicEventGroupEditorViewport::UpdatePreview()
{
    if (!PreviewScene.IsValid() || !EventGroupAsset)
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdatePreview: Invalid preview scene or asset"));
        return;
    }

    // Set the floor visibility based on the current state
    PreviewScene->SetFloorVisibility(bShowGroundPlane);

    // Log for debugging
    UE_LOG(LogTemp, Display, TEXT("UpdatePreview: Updating preview for asset %s"), *EventGroupAsset->GetName());

    // Store the currently selected child index before updating
    int32 CurrentSelectedChildIndex = INDEX_NONE;
    if (ViewportClient.IsValid())
    {
        CurrentSelectedChildIndex = ViewportClient->GetSelectedChildIndex();
        UE_LOG(LogTemp, Display, TEXT("UpdatePreview: Preserving selection of child index %d"), CurrentSelectedChildIndex);
    }

    // Force update when any child has a valid P3D asset
    bool bForceUpdate = false;
    for (const FCEDynamicEventGroupChild& Child : EventGroupAsset->Children)
    {
        if (Child.Type.IsValid())
        {
            bForceUpdate = true;
            break;
        }
    }

    // Always update if we're forcing or if counts don't match
    if (bForceUpdate || EventGroupAsset->Children.Num() != PreviewScene->GetChildActorComponents().Num())
    {
        UE_LOG(LogTemp, Display, TEXT("UpdatePreview: Updating child actors (Force: %d, ChildCount: %d, ComponentCount: %d)"),
            bForceUpdate, EventGroupAsset->Children.Num(), PreviewScene->GetChildActorComponents().Num());

        // Clear existing actors first
        PreviewScene->ClearChildActors();

        // Update child actors in the preview scene
        PreviewScene->UpdateChildActors(EventGroupAsset);

        // Ensure the viewport gets refreshed
        if (ViewportClient.IsValid())
        {
            // Restore the selection if it was valid
            if (CurrentSelectedChildIndex != INDEX_NONE &&
                CurrentSelectedChildIndex < EventGroupAsset->Children.Num())
            {
                UE_LOG(LogTemp, Display, TEXT("UpdatePreview: About to restore selection to child index %d"), CurrentSelectedChildIndex);

                // First restore the selection in the editor toolkit if available
                if (EditorToolkit)
                {
                    EditorToolkit->SelectChild(CurrentSelectedChildIndex);
                    UE_LOG(LogTemp, Display, TEXT("UpdatePreview: Called EditorToolkit->SelectChild(%d)"), CurrentSelectedChildIndex);
                }
                else
                {
                    // If no editor toolkit, just restore the selection in the viewport client
                    ViewportClient->SetSelectedChildIndex(CurrentSelectedChildIndex);
                    UE_LOG(LogTemp, Display, TEXT("UpdatePreview: Called ViewportClient->SetSelectedChildIndex(%d)"), CurrentSelectedChildIndex);
                }

                UE_LOG(LogTemp, Display, TEXT("UpdatePreview: Restored selection to child index %d"), CurrentSelectedChildIndex);
            }

            ViewportClient->Invalidate();
            UE_LOG(LogTemp, Display, TEXT("UpdatePreview: Invalidated viewport client"));
        }
    }
}
