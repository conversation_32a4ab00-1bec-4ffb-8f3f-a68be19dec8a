// CEDynamicEventGroupEditorViewportCommands.cpp
#include "CEType/CEDynamicEventGroupEditorViewportCommands.h"

#define LOCTEXT_NAMESPACE "CEDynamicEventGroupEditorViewportCommands"

void FCEDynamicEventGroupEditorViewportCommands::RegisterCommands()
{
    UI_COMMAND(ToggleGroundPlane, "Toggle Ground", "Toggle the visibility of the ground plane", EUserInterfaceActionType::ToggleButton, FInputChord());
    UI_COMMAND(ExportEventGroup, "Export", "Export the event group to XML", EUserInterfaceActionType::Button, FInputChord());
}

#undef LOCTEXT_NAMESPACE
