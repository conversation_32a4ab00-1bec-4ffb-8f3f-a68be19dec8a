// CEDynamicEventGroupEditorViewportToolBar.cpp
#include "CEType/CEDynamicEventGroupEditorViewportToolBar.h"
#include "CEType/CEDynamicEventGroupEditorViewport.h"
#include "CEType/CEDynamicEventGroupEditorViewportCommands.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"


void SCEDynamicEventGroupEditorViewportToolBar::Construct(const FArguments& InArgs, TSharedPtr<SCEDynamicEventGroupEditorViewport> InViewport)
{
    EditorViewport = InViewport;
    SCommonEditorViewportToolbarBase::Construct(SCommonEditorViewportToolbarBase::FArguments(), InViewport);
}

TSharedRef<SWidget> SCEDynamicEventGroupEditorViewportToolBar::MakeToolbar(const TSharedPtr<FExtender> InExtenders)
{
    // Create a command list
    TSharedPtr<FUICommandList> CommandList = MakeShareable(new FUICommandList);

    // Bind the toggle ground plane command
    CommandList->MapAction(
        FCEDynamicEventGroupEditorViewportCommands::Get().ToggleGroundPlane,
        FExecuteAction::CreateSP(EditorViewport.ToSharedRef(), &SCEDynamicEventGroupEditorViewport::ToggleGroundPlaneVisibility),
        FCanExecuteAction(),
        FIsActionChecked::CreateSP(EditorViewport.ToSharedRef(), &SCEDynamicEventGroupEditorViewport::IsGroundPlaneVisible)
    );

    // Bind the export event group command
    CommandList->MapAction(
        FCEDynamicEventGroupEditorViewportCommands::Get().ExportEventGroup,
        FExecuteAction::CreateSP(EditorViewport.ToSharedRef(), &SCEDynamicEventGroupEditorViewport::ExportEventGroup),
        FCanExecuteAction()
    );

    // Get the extenders from the viewport
    TSharedPtr<FExtender> CombinedExtender = InExtenders;
    if (EditorViewport.IsValid())
    {
        // Get any additional extenders from the viewport
        TSharedPtr<FExtender> ViewportExtenders = EditorViewport->GetExtenders();
        if (ViewportExtenders.IsValid())
        {
            // Combine the extenders
            if (!CombinedExtender.IsValid())
            {
                CombinedExtender = ViewportExtenders;
            }
            else
            {
                TArray<TSharedPtr<FExtender>> Extenders;
                Extenders.Add(ViewportExtenders);
                CombinedExtender->Combine(Extenders);
            }
        }
    }

    // Create a toolbar builder with the combined extenders
    FToolBarBuilder ToolbarBuilder(CommandList, FMultiBoxCustomization::None, CombinedExtender);

    // Add the ground plane toggle button
    ToolbarBuilder.BeginSection("Options");
    {
        ToolbarBuilder.AddToolBarButton(
            FCEDynamicEventGroupEditorViewportCommands::Get().ToggleGroundPlane,
            NAME_None,
            NSLOCTEXT("CEDynamicEventGroupEditorViewport", "GroundPlane", "Ground"),
            NSLOCTEXT("CEDynamicEventGroupEditorViewport", "GroundPlaneTooltip", "Toggle the visibility of the ground plane"),
            FSlateIcon()
        );
    }
    ToolbarBuilder.EndSection();

    // Add the export button
    ToolbarBuilder.BeginSection("Export");
    {
        ToolbarBuilder.AddToolBarButton(
            FCEDynamicEventGroupEditorViewportCommands::Get().ExportEventGroup,
            NAME_None,
            NSLOCTEXT("CEDynamicEventGroupEditorViewport", "ExportEventGroup", "Export"),
            NSLOCTEXT("CEDynamicEventGroupEditorViewport", "ExportEventGroupTooltip", "Export the event group to XML"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.SaveIcon")
        );
    }
    ToolbarBuilder.EndSection();

    return ToolbarBuilder.MakeWidget();
}
