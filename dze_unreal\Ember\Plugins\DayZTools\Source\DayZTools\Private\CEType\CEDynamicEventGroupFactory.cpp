// CEDynamicEventGroupFactory.cpp
#include "CEType/CEDynamicEventGroupFactory.h"
#include "CEType/CEDynamicEventGroup.h"

UCEDynamicEventGroupFactory::UCEDynamicEventGroupFactory()
{
    SupportedClass = UCEDynamicEventGroup::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UCEDynamicEventGroupFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new CE Dynamic Event Group asset
    UCEDynamicEventGroup* NewAsset = NewObject<UCEDynamicEventGroup>(InParent, Class, Name, Flags);
    return NewAsset;
}

bool UCEDynamicEventGroupFactory::ShouldShowInNewMenu() const
{
    return true;
}
