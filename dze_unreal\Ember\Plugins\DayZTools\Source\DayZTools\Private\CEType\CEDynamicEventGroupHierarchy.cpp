// CEDynamicEventGroupHierarchy.cpp
#include "CEType/CEDynamicEventGroupHierarchy.h"
#include "CEType/CEDynamicEventGroupEditorToolkit.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Layout/SSplitter.h"
#include "Widgets/Layout/SWrapBox.h"
#include "Widgets/Images/SImage.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Styling/AppStyle.h"
#include "Engine/StreamableManager.h"

void SCEDynamicEventGroupHierarchy::Construct(const FArguments& InArgs)
{
    EventGroupAsset = InArgs._EventGroupAsset;
    EditorToolkit = nullptr;
    SelectedChildIndex = -1;
    SearchFilterText = FText::GetEmpty();

    // Create the list view
    ListView = SNew(SListView<TSharedPtr<FCEDynamicEventGroupHierarchyItem>>)
        .ItemHeight(24)
        .ListItemsSource(&HierarchyItems)
        .OnGenerateRow(this, &SCEDynamicEventGroupHierarchy::OnGenerateRow)
        .OnSelectionChanged(this, &SCEDynamicEventGroupHierarchy::OnSelectionChanged)
        .OnContextMenuOpening(this, &SCEDynamicEventGroupHierarchy::OnContextMenuOpening)
        .SelectionMode(ESelectionMode::Single)
        .HeaderRow
        (
            SNew(SHeaderRow)
            + SHeaderRow::Column("Name")
            .DefaultLabel(NSLOCTEXT("CEDynamicEventGroupHierarchy", "Name", "Name"))
            .FillWidth(0.7f)
            + SHeaderRow::Column("Type")
            .DefaultLabel(NSLOCTEXT("CEDynamicEventGroupHierarchy", "Type", "Type"))
            .FillWidth(0.3f)
        );

    // Create the search box
    SearchBox = SNew(SSearchBox)
        .OnTextChanged(this, &SCEDynamicEventGroupHierarchy::OnSearchTextChanged)
        .DelayChangeNotificationsWhileTyping(true);

    // Create the child widget
    ChildSlot
    [
        SNew(SVerticalBox)

        // Toolbar
        + SVerticalBox::Slot()
        .AutoHeight()
        .Padding(2)
        [
            SNew(SHorizontalBox)

            // Add button
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(2, 0)
            [
                SNew(SButton)
                .ButtonStyle(FAppStyle::Get(), "SimpleButton")
                .ToolTipText(NSLOCTEXT("CEDynamicEventGroupHierarchy", "AddChild", "Add a new child"))
                .OnClicked(this, &SCEDynamicEventGroupHierarchy::OnAddChildClicked)
                .ContentPadding(FMargin(1, 0))
                [
                    SNew(SImage)
                    .Image(FAppStyle::Get().GetBrush("Icons.PlusCircle"))
                    .ColorAndOpacity(FSlateColor::UseForeground())
                ]
            ]

            // Remove button
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(2, 0)
            [
                SNew(SButton)
                .ButtonStyle(FAppStyle::Get(), "SimpleButton")
                .ToolTipText(NSLOCTEXT("CEDynamicEventGroupHierarchy", "RemoveChild", "Remove the selected child"))
                .OnClicked_Lambda([this]() { OnRemoveChildClicked(); return FReply::Handled(); })
                .ContentPadding(FMargin(1, 0))
                [
                    SNew(SImage)
                    .Image(FAppStyle::Get().GetBrush("Icons.Delete"))
                    .ColorAndOpacity(FSlateColor::UseForeground())
                ]
            ]

            // Move up button
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(2, 0)
            [
                SNew(SButton)
                .ButtonStyle(FAppStyle::Get(), "SimpleButton")
                .ToolTipText(NSLOCTEXT("CEDynamicEventGroupHierarchy", "MoveUp", "Move the selected child up"))
                .OnClicked_Lambda([this]() { OnMoveChildUpClicked(); return FReply::Handled(); })
                .ContentPadding(FMargin(1, 0))
                [
                    SNew(SImage)
                    .Image(FAppStyle::Get().GetBrush("Icons.ArrowUp"))
                    .ColorAndOpacity(FSlateColor::UseForeground())
                ]
            ]

            // Move down button
            + SHorizontalBox::Slot()
            .AutoWidth()
            .Padding(2, 0)
            [
                SNew(SButton)
                .ButtonStyle(FAppStyle::Get(), "SimpleButton")
                .ToolTipText(NSLOCTEXT("CEDynamicEventGroupHierarchy", "MoveDown", "Move the selected child down"))
                .OnClicked_Lambda([this]() { OnMoveChildDownClicked(); return FReply::Handled(); })
                .ContentPadding(FMargin(1, 0))
                [
                    SNew(SImage)
                    .Image(FAppStyle::Get().GetBrush("Icons.ArrowDown"))
                    .ColorAndOpacity(FSlateColor::UseForeground())
                ]
            ]

            // Search box
            + SHorizontalBox::Slot()
            .FillWidth(1.0f)
            .Padding(5, 0, 0, 0)
            .VAlign(VAlign_Center)
            [
                SearchBox.ToSharedRef()
            ]
        ]

        // List view
        + SVerticalBox::Slot()
        .FillHeight(1.0f)
        .Padding(0, 2, 0, 0)
        [
            SNew(SBorder)
            .BorderImage(FAppStyle::GetBrush("ToolPanel.GroupBorder"))
            .Padding(0)
            [
                ListView.ToSharedRef()
            ]
        ]
    ];

    // Refresh the hierarchy
    RefreshHierarchy();
}

void SCEDynamicEventGroupHierarchy::RefreshHierarchy()
{
    // Clear the existing hierarchy items
    HierarchyItems.Empty();

    // Add the hierarchy items
    if (EventGroupAsset)
    {
        for (int32 i = 0; i < EventGroupAsset->Children.Num(); i++)
        {
            const FCEDynamicEventGroupChild& Child = EventGroupAsset->Children[i];
            FString DisplayName;

            if (Child.Type.IsNull())
            {
                DisplayName = FString::Printf(TEXT("Child %d (No Type)"), i);
            }
            else
            {
                // Get the actual asset name from the soft object reference
                FString TypeName = Child.Type.GetAssetName();
                DisplayName = FString::Printf(TEXT("Child %d (%s)"), i, *TypeName);
            }

            TSharedPtr<FCEDynamicEventGroupHierarchyItem> Item = MakeShareable(
                new FCEDynamicEventGroupHierarchyItem(i, DisplayName, Child.Type));

            if (PassesFilter(Item))
            {
                HierarchyItems.Add(Item);
            }
        }
    }

    // Refresh the list view
    if (ListView.IsValid())
    {
        ListView->RequestListRefresh();
    }
}

TSharedRef<ITableRow> SCEDynamicEventGroupHierarchy::OnGenerateRow(TSharedPtr<FCEDynamicEventGroupHierarchyItem> Item, const TSharedRef<STableViewBase>& OwnerTable)
{
    // Get the asset name for display
    FString AssetName = TEXT("None");
    if (!Item->Type.IsNull())
    {
        AssetName = Item->Type.GetAssetName();
    }

    return SNew(STableRow<TSharedPtr<FCEDynamicEventGroupHierarchyItem>>, OwnerTable)
        [
            SNew(SHorizontalBox)

            // Name column
            + SHorizontalBox::Slot()
            .FillWidth(0.7f)
            .Padding(5, 0, 0, 0)
            .VAlign(VAlign_Center)
            [
                SNew(STextBlock)
                .Text(FText::FromString(Item->DisplayName))
            ]

            // Type column
            + SHorizontalBox::Slot()
            .FillWidth(0.3f)
            .Padding(5, 0, 0, 0)
            .VAlign(VAlign_Center)
            [
                SNew(STextBlock)
                .Text(FText::FromString(AssetName))
                .ColorAndOpacity(Item->Type.IsNull() ? FLinearColor(0.5f, 0.5f, 0.5f, 1.0f) : FLinearColor::White)
            ]
        ];
}

void SCEDynamicEventGroupHierarchy::OnSelectionChanged(TSharedPtr<FCEDynamicEventGroupHierarchyItem> Item, ESelectInfo::Type SelectInfo)
{
    if (Item.IsValid())
    {
        SelectedChildIndex = Item->ChildIndex;

        // Notify the editor toolkit that the selection has changed
        if (EditorToolkit)
        {
            EditorToolkit->SelectChild(SelectedChildIndex);
        }
    }
    else
    {
        SelectedChildIndex = -1;

        // Notify the editor toolkit that the selection has been cleared
        if (EditorToolkit)
        {
            EditorToolkit->SelectChild(-1);
        }
    }
}

TSharedPtr<SWidget> SCEDynamicEventGroupHierarchy::OnContextMenuOpening()
{
    FMenuBuilder MenuBuilder(true, nullptr);

    MenuBuilder.BeginSection("CEDynamicEventGroupHierarchyActions", NSLOCTEXT("CEDynamicEventGroupHierarchy", "Actions", "Actions"));
    {
        MenuBuilder.AddMenuEntry(
            NSLOCTEXT("CEDynamicEventGroupHierarchy", "AddChild", "Add Child"),
            NSLOCTEXT("CEDynamicEventGroupHierarchy", "AddChildTooltip", "Add a new child to the event group"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.PlusCircle"),
            FUIAction(
                FExecuteAction::CreateLambda([this]() { OnAddChildClicked(); }),
                FCanExecuteAction()
            )
        );

        MenuBuilder.AddMenuEntry(
            NSLOCTEXT("CEDynamicEventGroupHierarchy", "RemoveChild", "Remove Child"),
            NSLOCTEXT("CEDynamicEventGroupHierarchy", "RemoveChildTooltip", "Remove the selected child from the event group"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Delete"),
            FUIAction(
                FExecuteAction::CreateLambda([this]() { OnRemoveChildClicked(); }),
                FCanExecuteAction::CreateLambda([this]() { return SelectedChildIndex != -1; })
            )
        );
    }
    MenuBuilder.EndSection();

    return MenuBuilder.MakeWidget();
}

FReply SCEDynamicEventGroupHierarchy::OnAddChildClicked()
{
    if (EventGroupAsset)
    {
        // Add a new child
        FCEDynamicEventGroupChild NewChild;
        EventGroupAsset->Children.Add(NewChild);

        // Mark the asset as modified
        EventGroupAsset->Modify();

        // Refresh the hierarchy
        RefreshHierarchy();

        // Select the new child
        if (HierarchyItems.Num() > 0)
        {
            ListView->SetSelection(HierarchyItems.Last());
        }
    }

    return FReply::Handled();
}

void SCEDynamicEventGroupHierarchy::OnRemoveChildClicked()
{
    if (EventGroupAsset && SelectedChildIndex != -1 && SelectedChildIndex < EventGroupAsset->Children.Num())
    {
        // Remove the selected child
        EventGroupAsset->Children.RemoveAt(SelectedChildIndex);

        // Mark the asset as modified
        EventGroupAsset->Modify();

        // Refresh the hierarchy
        RefreshHierarchy();

        // Clear the selection
        ListView->ClearSelection();
        SelectedChildIndex = -1;
    }
}

void SCEDynamicEventGroupHierarchy::OnMoveChildUpClicked()
{
    if (EventGroupAsset && SelectedChildIndex > 0 && SelectedChildIndex < EventGroupAsset->Children.Num())
    {
        // Swap the selected child with the one above it
        EventGroupAsset->Children.Swap(SelectedChildIndex, SelectedChildIndex - 1);

        // Mark the asset as modified
        EventGroupAsset->Modify();

        // Update the selection index
        SelectedChildIndex--;

        // Refresh the hierarchy
        RefreshHierarchy();

        // Update the selection in the list view
        SetSelectedChildIndex(SelectedChildIndex);
    }
}

void SCEDynamicEventGroupHierarchy::OnMoveChildDownClicked()
{
    if (EventGroupAsset && SelectedChildIndex != -1 && SelectedChildIndex < EventGroupAsset->Children.Num() - 1)
    {
        // Swap the selected child with the one below it
        EventGroupAsset->Children.Swap(SelectedChildIndex, SelectedChildIndex + 1);

        // Mark the asset as modified
        EventGroupAsset->Modify();

        // Update the selection index
        SelectedChildIndex++;

        // Refresh the hierarchy
        RefreshHierarchy();

        // Update the selection in the list view
        SetSelectedChildIndex(SelectedChildIndex);
    }
}

void SCEDynamicEventGroupHierarchy::SetSelectedChildIndex(int32 InChildIndex)
{
    if (InChildIndex != SelectedChildIndex)
    {
        SelectedChildIndex = InChildIndex;

        // Find the item with the matching child index
        TSharedPtr<FCEDynamicEventGroupHierarchyItem> ItemToSelect;
        for (const TSharedPtr<FCEDynamicEventGroupHierarchyItem>& Item : HierarchyItems)
        {
            if (Item->ChildIndex == InChildIndex)
            {
                ItemToSelect = Item;
                break;
            }
        }

        if (ItemToSelect.IsValid())
        {
            ListView->SetSelection(ItemToSelect);
        }
        else
        {
            ListView->ClearSelection();
        }
    }
}

void SCEDynamicEventGroupHierarchy::SetEditorToolkit(FCEDynamicEventGroupEditorToolkit* InEditorToolkit)
{
    EditorToolkit = InEditorToolkit;
}

void SCEDynamicEventGroupHierarchy::OnSearchTextChanged(const FText& InFilterText)
{
    SearchFilterText = InFilterText;
    RefreshHierarchy();
}

bool SCEDynamicEventGroupHierarchy::PassesFilter(const TSharedPtr<FCEDynamicEventGroupHierarchyItem>& Item) const
{
    if (!Item.IsValid())
    {
        return false;
    }

    if (SearchFilterText.IsEmpty())
    {
        return true;
    }

    const FString& FilterString = SearchFilterText.ToString();

    // Check if the display name contains the filter string
    if (Item->DisplayName.Contains(FilterString, ESearchCase::IgnoreCase))
    {
        return true;
    }

    // Check if the asset name contains the filter string
    if (!Item->Type.IsNull() && Item->Type.GetAssetName().Contains(FilterString, ESearchCase::IgnoreCase))
    {
        return true;
    }

    return false;
}
