// CEDynamicEventGroupPreviewScene.cpp
#include "CEType/CEDynamicEventGroupPreviewScene.h"
#include "CEType/CEDynamicEventGroup.h"
#include "CEType/DynamicEventComponentOwner.h" // Include the owner actor class
#include "CEType/CEDynamicEventGroupEditorToolkit.h"
#include "Components/ChildActorComponent.h"
#include "Components/SceneComponent.h"
#include "Engine/StreamableManager.h"
#include "AssetViewerSettings.h"
#include "Engine/World.h"

FCEDynamicEventGroupPreviewScene::FCEDynamicEventGroupPreviewScene(ConstructionValues CVS)
    : FAdvancedPreviewScene(CVS)
{
    EventGroupAsset = nullptr;
    RootOwnerActor = nullptr;

    // Create a simple sky light
    SkyLight = NewObject<USkyLightComponent>();
    SkyLight->Intensity = 1.0f;
    SkyLight->SetMobility(EComponentMobility::Movable);
    AddComponent(SkyLight, FTransform::Identity);

    // Create the root owner actor
    UWorld* World = GetWorld();
    if (World)
    {
        FActorSpawnParameters SpawnParams;
        SpawnParams.ObjectFlags = RF_Transient;
        SpawnParams.Name = FName(TEXT("DynamicEventRootOwner"));

        RootOwnerActor = World->SpawnActor<ADynamicEventComponentOwner>(SpawnParams);
        if (RootOwnerActor)
        {
            UE_LOG(LogTemp, Display, TEXT("Created root owner actor: %s"), *RootOwnerActor->GetName());
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("Failed to create root owner actor"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to get world in constructor"));
    }
}

FCEDynamicEventGroupPreviewScene::~FCEDynamicEventGroupPreviewScene()
{
    // Ensure child actors are cleaned up
    if (GEngine && !GExitPurge)  // Check if engine is still valid and we're not in shutdown
    {
        ClearChildActors();

        // Destroy the root owner actor
        if (RootOwnerActor && IsValid(RootOwnerActor))
        {
            UE_LOG(LogTemp, Display, TEXT("Destroying root owner actor: %s"), *RootOwnerActor->GetName());
            RootOwnerActor->Destroy();
            RootOwnerActor = nullptr;
        }
    }
}

UChildActorComponent* FCEDynamicEventGroupPreviewScene::GetChildActorComponent(int32 Index) const
{
    if (ChildActorComponents.IsValidIndex(Index))
    {
        UChildActorComponent* Component = ChildActorComponents[Index];
        if (Component && IsValid(Component))
        {
            return Component;
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("GetChildActorComponent: Component at index %d is invalid"), Index);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("GetChildActorComponent: Invalid index %d (array size: %d)"),
            Index, ChildActorComponents.Num());
    }
    return nullptr;
}

void FCEDynamicEventGroupPreviewScene::UpdateChildTransform(int32 Index, const FTransform& NewTransform)
{
    // Safety checks
    if (!ChildActorComponents.IsValidIndex(Index))
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Invalid child index %d"), Index);
        return;
    }

    if (!EventGroupAsset)
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: EventGroupAsset is null"));
        return;
    }

    if (!EventGroupAsset->Children.IsValidIndex(Index))
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Invalid child index %d in asset"), Index);
        return;
    }

    UChildActorComponent* ChildComponent = ChildActorComponents[Index];
    if (!ChildComponent || !IsValid(ChildComponent))
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildTransform: Child component is invalid"));
        return;
    }

    // Update the component transform
    ChildComponent->SetRelativeTransform(NewTransform);

    // Update the asset data
    FCEDynamicEventGroupChild& Child = EventGroupAsset->Children[Index];

    // Convert from Unreal coordinates to DayZ coordinates (divide by 100 to convert from cm to m)
    Child.X = NewTransform.GetLocation().X / 100.0f;
    Child.Z = NewTransform.GetLocation().Y / 100.0f;
    Child.Y = NewTransform.GetLocation().Z / 100.0f;

    // Get rotation around Z axis (yaw)
    Child.A = NewTransform.GetRotation().Rotator().Yaw;

    // Notify the editor toolkit about the transform change
    if (EditorToolkit)
    {
        EditorToolkit->HandleTransformUpdate(Index, NewTransform);
    }

    // We don't need to broadcast property changes here as it would trigger a full viewport update
    // which would cause the selection to be lost. The HandleTransformUpdate method above
    // will update the details panel directly.
}

void FCEDynamicEventGroupPreviewScene::UpdateChildActors(UCEDynamicEventGroup* InEventGroupAsset)
{
    // Safety check for the input asset
    if (!InEventGroupAsset)
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateChildActors: Input asset is null"));
        return;
    }

    // Store the event group asset
    EventGroupAsset = InEventGroupAsset;

    // We'll use a more selective approach to updating child actors
    // First, create a map of existing components by index
    TMap<int32, UChildActorComponent*> ExistingComponents;
    for (int32 i = 0; i < ChildActorComponents.Num(); ++i)
    {
        if (ChildActorComponents[i] && IsValid(ChildActorComponents[i]))
        {
            ExistingComponents.Add(i, ChildActorComponents[i]);
        }
    }

    // Clear the array but don't destroy the components yet
    ChildActorComponents.Empty();

    // If there are no children, we're done
    if (EventGroupAsset->Children.Num() == 0)
    {
        UE_LOG(LogTemp, Display, TEXT("UpdateChildActors: Asset has no children"));

        // Now remove any existing components since we have no children
        for (auto& Pair : ExistingComponents)
        {
            if (Pair.Value && IsValid(Pair.Value))
            {
                // First set child actor class to null to prevent cascading deletion issues
                Pair.Value->SetChildActorClass(nullptr);

                // Make sure the component is still valid after setting class to null
                if (IsValid(Pair.Value))
                {
                    // Unregister first
                    if (Pair.Value->IsRegistered())
                    {
                        Pair.Value->UnregisterComponent();
                    }

                    // Then remove
                    RemoveComponent(Pair.Value);
                }
            }
        }

        return;
    }

    // Create or update child actors for each child in the event group
    for (int32 ChildIndex = 0; ChildIndex < EventGroupAsset->Children.Num(); ChildIndex++)
    {
        // Safety check to make sure the asset is still valid
        if (!IsValid(EventGroupAsset))
        {
            UE_LOG(LogTemp, Warning, TEXT("UpdateChildActors: Asset became invalid during processing"));
            break;
        }

        const FCEDynamicEventGroupChild& Child = EventGroupAsset->Children[ChildIndex];
        UChildActorComponent* ExistingComponent = ExistingComponents.FindRef(ChildIndex);

        // If the child has no ConfigClass asset, remove any existing component
        if (Child.Type.IsNull())
        {
            if (ExistingComponent)
            {
                // First set child actor class to null to prevent cascading deletion issues
                ExistingComponent->SetChildActorClass(nullptr);

                // Make sure the component is still valid after setting class to null
                if (IsValid(ExistingComponent))
                {
                    // Unregister first
                    if (ExistingComponent->IsRegistered())
                    {
                        ExistingComponent->UnregisterComponent();
                    }

                    // Then remove
                    RemoveComponent(ExistingComponent);
                }

                // Remove from the map
                ExistingComponents.Remove(ChildIndex);
            }

            // Add a null entry to maintain the index mapping
            ChildActorComponents.Add(nullptr);
            continue;
        }

        // Load the ConfigClass asset first
        FSoftObjectPath ConfigClassPath = Child.Type.ToSoftObjectPath();
        UConfigClass* ConfigClassAsset = nullptr;
        
        if (ConfigClassPath.IsValid())
        {
            ConfigClassAsset = Cast<UConfigClass>(ConfigClassPath.TryLoad());
        }
        
        if (!ConfigClassAsset || ConfigClassAsset->Model.IsNull())
        {
            UE_LOG(LogTemp, Warning, TEXT("UpdateChildActors: Invalid ConfigClass asset or no model set for child %d"), ChildIndex);
            
            // Add a null entry to maintain the index mapping
            ChildActorComponents.Add(nullptr);
            continue;
        }
        
        // Try to load the child P3D Blueprint asset from the ConfigClass's model property
        FSoftObjectPath AssetPath = ConfigClassAsset->Model.ToSoftObjectPath();
        UP3DBlueprint* Blueprint = nullptr;

        // Safety check for the asset path
        if (AssetPath.IsValid())
        {
            Blueprint = Cast<UP3DBlueprint>(AssetPath.TryLoad());

            if (!Blueprint)
            {
                // Try an alternative loading method
                static FStreamableManager StreamableManager;
                Blueprint = Cast<UP3DBlueprint>(StreamableManager.LoadSynchronous(AssetPath));
            }
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("UpdateChildActors: Invalid asset path for ConfigClass model child %d"), ChildIndex);

            // Add a null entry to maintain the index mapping
            ChildActorComponents.Add(nullptr);
            continue;
        }

        if (Blueprint && Blueprint->GeneratedClass && Blueprint->GeneratedClass->IsChildOf(AActor::StaticClass()))
        {
            // Set the transform based on the child's coordinates
            FTransform ChildTransform;
            ChildTransform.SetLocation(FVector(Child.X * 100.0f, Child.Z * 100.0f, Child.Y * 100.0f)); // Convert to Unreal coordinates (cm)
            ChildTransform.SetRotation(FQuat(FRotator(0.0f, Child.A, 0.0f))); // Set rotation around Z axis

            // If we have an existing component, update it
            if (ExistingComponent)
            {
                // Update the transform
                ExistingComponent->SetRelativeTransform(ChildTransform);

                // Update the child actor class if needed
                TSubclassOf<AActor> ActorClass = Cast<UClass>(Blueprint->GeneratedClass);
                if (ExistingComponent->GetChildActorClass() != ActorClass)
                {
                    ExistingComponent->SetChildActorClass(ActorClass);
                }

                // Add to the array for tracking
                ChildActorComponents.Add(ExistingComponent);

                // Remove from the map
                ExistingComponents.Remove(ChildIndex);

                UE_LOG(LogTemp, Display, TEXT("Updated existing child actor: %s at location (%f, %f, %f)"),
                    *Blueprint->GetName(),
                    ChildTransform.GetLocation().X,
                    ChildTransform.GetLocation().Y,
                    ChildTransform.GetLocation().Z);
            }
            else
            {
                // Create and add a new child actor component
                UChildActorComponent* ChildActorComponent = CreateChildActorFromBlueprint(Blueprint, ChildTransform);
                if (ChildActorComponent)
                {
                    // Add to the array for tracking
                    ChildActorComponents.Add(ChildActorComponent);

                    UE_LOG(LogTemp, Display, TEXT("Added child actor: %s at location (%f, %f, %f)"),
                        *Blueprint->GetName(),
                        ChildTransform.GetLocation().X,
                        ChildTransform.GetLocation().Y,
                        ChildTransform.GetLocation().Z);
                }
                else
                {
                    // Add a null entry to maintain the index mapping
                    ChildActorComponents.Add(nullptr);

                    UE_LOG(LogTemp, Warning, TEXT("Failed to create child actor component for blueprint: %s"), *Blueprint->GetName());
                }
            }
        }
        else
        {
            // Add a null entry to maintain the index mapping
            ChildActorComponents.Add(nullptr);

            if (!Blueprint)
            {
                UE_LOG(LogTemp, Warning, TEXT("Failed to load blueprint for asset path: %s"), *AssetPath.ToString());
            }
            else if (!Blueprint->GeneratedClass)
            {
                UE_LOG(LogTemp, Warning, TEXT("Blueprint has no generated class: %s"), *Blueprint->GetName());
            }
            else if (!Blueprint->GeneratedClass->IsChildOf(AActor::StaticClass()))
            {
                UE_LOG(LogTemp, Warning, TEXT("Blueprint generated class is not an actor: %s"), *Blueprint->GetName());
            }
        }
    }

    // Remove any remaining components that weren't reused
    for (auto& Pair : ExistingComponents)
    {
        if (Pair.Value && IsValid(Pair.Value))
        {
            // First set child actor class to null to prevent cascading deletion issues
            Pair.Value->SetChildActorClass(nullptr);

            // Make sure the component is still valid after setting class to null
            if (IsValid(Pair.Value))
            {
                // Unregister first
                if (Pair.Value->IsRegistered())
                {
                    Pair.Value->UnregisterComponent();
                }

                // Then remove
                RemoveComponent(Pair.Value);
            }
        }
    }

    UE_LOG(LogTemp, Display, TEXT("Updated child actors. Total count: %d"), ChildActorComponents.Num());
}

UChildActorComponent* FCEDynamicEventGroupPreviewScene::CreateChildActorFromBlueprint(UP3DBlueprint* Blueprint, const FTransform& Transform)
{
    if (!Blueprint)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromBlueprint: Blueprint is null"));
        return nullptr;
    }

    if (!Blueprint->GeneratedClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromBlueprint: Blueprint %s has no generated class"), *Blueprint->GetName());
        return nullptr;
    }

    if (!Blueprint->GeneratedClass->IsChildOf(AActor::StaticClass()))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromBlueprint: Blueprint %s generated class is not an actor"), *Blueprint->GetName());
        return nullptr;
    }

    // Check if we have a valid root owner actor
    if (!RootOwnerActor || !IsValid(RootOwnerActor))
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromBlueprint: Root owner actor is invalid, creating a new one"));

        // Get the world from the preview scene
        UWorld* World = GetWorld();
        if (!World)
        {
            UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromBlueprint: Failed to get world"));
            return nullptr;
        }

        // Create the root owner actor
        FActorSpawnParameters SpawnParams;
        SpawnParams.ObjectFlags = RF_Transient;
        SpawnParams.Name = FName(TEXT("DynamicEventRootOwner"));

        RootOwnerActor = World->SpawnActor<ADynamicEventComponentOwner>(SpawnParams);
        if (!RootOwnerActor)
        {
            UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromBlueprint: Failed to create root owner actor"));
            return nullptr;
        }

        UE_LOG(LogTemp, Display, TEXT("Created new root owner actor: %s"), *RootOwnerActor->GetName());
    }

    // Create a new child actor component and attach it to the root owner actor
    UChildActorComponent* ChildActorComponent = NewObject<UChildActorComponent>(RootOwnerActor);
    if (!ChildActorComponent)
    {
        UE_LOG(LogTemp, Warning, TEXT("CreateChildActorFromBlueprint: Failed to create child actor component"));
        return nullptr;
    }

    // Set the child actor class
    TSubclassOf<AActor> ActorClass = Cast<UClass>(Blueprint->GeneratedClass);
    ChildActorComponent->SetChildActorClass(ActorClass);

    // Register the component with the root owner actor
    ChildActorComponent->RegisterComponent();
    ChildActorComponent->AttachToComponent(RootOwnerActor->GetRootComponent(), FAttachmentTransformRules::KeepRelativeTransform);

    // Set the component's transform
    ChildActorComponent->SetRelativeTransform(Transform);

    // Log success
    UE_LOG(LogTemp, Display, TEXT("CreateChildActorFromBlueprint: Successfully created child actor for %s with root owner actor %s"),
        *Blueprint->GetName(), *RootOwnerActor->GetName());

    return ChildActorComponent;
}

void FCEDynamicEventGroupPreviewScene::ClearChildActors()
{
    UE_LOG(LogTemp, Display, TEXT("ClearChildActors: Beginning cleanup. Component count: %d"), ChildActorComponents.Num());

    // If we have a valid root owner actor, remove all child components
    if (RootOwnerActor && IsValid(RootOwnerActor))
    {
        UE_LOG(LogTemp, Display, TEXT("Removing components from root owner actor: %s"), *RootOwnerActor->GetName());

        // Get all child components
        TArray<USceneComponent*> ChildComponents;
        RootOwnerActor->GetRootComponent()->GetChildrenComponents(true, ChildComponents);

        // Remove each child component
        for (USceneComponent* Component : ChildComponents)
        {
            if (Component && IsValid(Component))
            {
                UChildActorComponent* ChildActorComponent = Cast<UChildActorComponent>(Component);
                if (ChildActorComponent)
                {
                    // First set child actor class to null to prevent cascading deletion issues
                    ChildActorComponent->SetChildActorClass(nullptr);

                    // Unregister the component
                    if (ChildActorComponent->IsRegistered())
                    {
                        ChildActorComponent->UnregisterComponent();
                    }

                    // Detach from parent
                    ChildActorComponent->DetachFromComponent(FDetachmentTransformRules::KeepRelativeTransform);

                    // Destroy the component
                    ChildActorComponent->DestroyComponent();

                    UE_LOG(LogTemp, Display, TEXT("Removed child actor component: %s"), *ChildActorComponent->GetName());
                }
            }
        }
    }
    else
    {
        // If we don't have a root owner actor, process components the old way
        for (int32 i = 0; i < ChildActorComponents.Num(); i++)
        {
            UChildActorComponent* Component = ChildActorComponents[i];
            if (!Component || !IsValid(Component))
            {
                UE_LOG(LogTemp, Warning, TEXT("Skipping null/invalid component at index %d"), i);
                continue;
            }

            // Log component details
            FString ComponentName = Component->GetName();
            FString OwnerName = Component->GetOwner() ? Component->GetOwner()->GetName() : TEXT("NO_OWNER");
            FString ChildActorName = Component->GetChildActor() ? Component->GetChildActor()->GetName() : TEXT("NO_CHILD_ACTOR");
            UE_LOG(LogTemp, Display, TEXT("Processing component [%d]: Name=%s, Owner=%s, ChildActor=%s"),
                i, *ComponentName, *OwnerName, *ChildActorName);

            // If the component has a child actor, destroy it first
            if (AActor* ChildActor = Component->GetChildActor())
            {
                ChildActor->Destroy();
            }

            // If the component is registered but has no owner, we need to handle it carefully
            if (Component->IsRegistered() && !Component->GetOwner())
            {
                UE_LOG(LogTemp, Display, TEXT("Component [%s] is registered but has no owner, handling specially"), *ComponentName);

                // Manually clear the child actor class to prevent the problematic OnUnregister call
                Component->SetChildActorClass(nullptr);

                // Force detach from parent
                Component->DetachFromParent();

                // Unregister the component using the public API
                if (Component->IsRegistered())
                {
                    Component->UnregisterComponent();
                }
            }

            // Remove from scene
            RemoveComponent(Component);
            UE_LOG(LogTemp, Display, TEXT("Removed component [%s]"), *ComponentName);
        }
    }

    // Clear the array
    ChildActorComponents.Empty();

    UE_LOG(LogTemp, Display, TEXT("ClearChildActors: Finished cleanup"));
}