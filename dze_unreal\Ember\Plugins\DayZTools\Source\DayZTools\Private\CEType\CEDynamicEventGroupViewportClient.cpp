// Copyright YourCompany, Inc. All Rights Reserved.

#include "CEType/CEDynamicEventGroupViewportClient.h" // This must be first

#include "CEType/CEDynamicEventGroupPreviewScene.h"
#include "CEType/CEDynamicEventGroupEditorToolkit.h"
#include "CEType/CEDynamicEventGroup.h"
#include "Editor.h"


// Engine includes
#include "Engine/World.h"
#include "DrawDebugHelpers.h"
#include "Components/PrimitiveComponent.h" // Keep specific includes needed
#include "ComponentVisualizer.h"
#include "GameFramework/Actor.h"          // Keep specific includes needed
#include "Engine/Canvas.h"                // Keep specific includes needed
// Add necessary includes if not already present
#include "EngineUtils.h"
#include "IPersonaPreviewScene.h"
// #include "HitProxies/WidgetHitProxy.h" // REMOVED - Not needed / Invalid path
#include "Editor/UnrealEdTypes.h"
#include "EditorModeManager.h"
#include "Engine/Selection.h"
#include "Components/ChildActorComponent.h"
#include "Components/SceneComponent.h"
#include "ScopedTransaction.h"
#include "EditorViewportClient.h" // Includes HWidgetUtilProxy declaration
#include "Framework/Application/SlateApplication.h" // Required for GetModifierKeys if needed
#include "Widgets/SWidget.h"
#include "Widgets/Input/SButton.h"
#include "UnrealWidget.h"
#include "CanvasItem.h" // For FCanvas primitives if needed later
#include "SceneView.h" // For FSceneView in ProcessClick

// Required Includes (Add any others you might need)
#include "HitProxies.h"

// --- Hit Proxy Implementation ---
// This line is critical! It implements the hit proxy functions declared with DECLARE_HIT_PROXY()
IMPLEMENT_HIT_PROXY(HCEChildActorProxy, HHitProxy);

// --- Hit Proxy Definition ---
// IMPORTANT: This struct definition should ideally live in a shared header file (.h)
// included by both this viewport client and your preview scene code.


// Define a unique namespace for your editor module's localized text
#define LOCTEXT_NAMESPACE "CEDynamicEventGroupEditor"

FCEDynamicEventGroupViewportClient::FCEDynamicEventGroupViewportClient(
	const TSharedRef<IToolkitHost>& InToolkitHost,
	FCEDynamicEventGroupPreviewScene* InPreviewScene,
	const TSharedRef<SEditorViewport>& InViewportWidget,
	FCEDynamicEventGroupEditorToolkit* InEditorToolkit
)
	: FEditorViewportClient(&GLevelEditorModeTools(), InPreviewScene, InViewportWidget)
	, PreviewScenePtr(InPreviewScene)
	, EditorToolkit(InEditorToolkit)
	, bShouldFocusOnBounds(true)
	, SelectedChildIndex(INDEX_NONE)
	, bIsManipulating(false) // Initialize manipulation flag
{
	UE_LOG(LogTemp, Warning, TEXT("ViewportClient constructor - Start"));

	SetViewMode(VMI_Lit);
	OverrideNearClipPlane(1.0f);
	bUsingOrbitCamera = false; // Use standard editor fly controls
	SetRealtime(true);
	SetCameraSpeedSetting(3);
	EngineShowFlags.SetCompositeEditorPrimitives(true);
	EngineShowFlags.SetSelectionOutline(true);
	EngineShowFlags.SetGrid(true);
	EngineShowFlags.SetModeWidgets(true); // Show gizmos
	//EngineShowFlags.SetHitProxies(true);
	SetRequiredCursorOverride(false);

	// Initial camera position
	SetViewLocation(FVector(500.f, 500.f, 500.f));
	SetViewRotation(FRotator(-45.f, -45.f, 0.f));

	// Ensure the widget starts hidden and set defaults
	if (Widget) { Widget->SetDefaultVisibility(false); }
	if (ModeTools) {
		ModeTools->SetWidgetMode(UE::Widget::WM_Translate);
		ModeTools->SetCoordSystem(COORD_World);
	}
	else { UE_LOG(LogTemp, Error, TEXT("ModeTools is invalid in ViewportClient constructor!")); }

	UE_LOG(LogTemp, Warning, TEXT("ViewportClient constructor - Complete"));
}

void FCEDynamicEventGroupViewportClient::Tick(float DeltaSeconds)
{
	FEditorViewportClient::Tick(DeltaSeconds);

	// Tick the preview scene world
	if (PreviewScenePtr)
	{
		// Initial focus logic
		if (bShouldFocusOnBounds)
		{
			FBoxSphereBounds Bounds = GetAllChildrenBounds();
			if (Bounds.SphereRadius > KINDA_SMALL_NUMBER)
			{
				FocusViewportOnBounds(Bounds, true);
			}
			bShouldFocusOnBounds = false;
		}

		// Use a try-catch ONLY if absolutely necessary for debugging stubborn crashes.
		// AIM TO REMOVE THIS once the underlying cause is found.
		try
		{
			if (PreviewScenePtr->GetWorld())
			{
				PreviewScenePtr->GetWorld()->Tick(LEVELTICK_All, DeltaSeconds);
			}
		}
		catch (...)
		{
			UE_LOG(LogTemp, Error, TEXT("Exception caught during world tick in viewport client - INVESTIGATE AND FIX ROOT CAUSE!"));
		}
	}
}

void FCEDynamicEventGroupViewportClient::Draw(FViewport* InViewport, FCanvas* Canvas)
{
	// Base class draw first (handles scene rendering, grid, gizmo etc.)
	FEditorViewportClient::Draw(InViewport, Canvas);

	// Add any custom 2D drawing here if needed
	// Example:
	// if (SelectedChildIndex != INDEX_NONE)
	// {
	//     FString Text = FString::Printf(TEXT("Selected Index: %d"), SelectedChildIndex);
	// 		FCanvasTextItem TextItem(FVector2D(10, 10), FText::FromString(Text), GEngine->GetSmallFont(), FLinearColor::White);
	// 		Canvas->DrawItem(TextItem);
	// }
}

bool FCEDynamicEventGroupViewportClient::InputKey(const FInputKeyEventArgs& EventArgs)
{
	// Allow switching widget modes only when a child is selected and the widget is visible
	if (SelectedChildIndex != INDEX_NONE && EventArgs.Event == IE_Pressed)
	{
		UE::Widget::EWidgetMode NewMode = GetWidgetMode();
		bool bChangedMode = false;
		if (EventArgs.Key == EKeys::W || EventArgs.Key == EKeys::SpaceBar)
		{
			NewMode = UE::Widget::WM_Translate;
			bChangedMode = true;
		}
		else if (EventArgs.Key == EKeys::E)
		{
			NewMode = UE::Widget::WM_Rotate;
			bChangedMode = true;
		}
		else if (EventArgs.Key == EKeys::R)
		{
			NewMode = UE::Widget::WM_Scale;
			bChangedMode = true;
		}

		if (bChangedMode && NewMode != GetWidgetMode())
		{
			SetWidgetMode(NewMode);
			Invalidate(); // Redraw with new widget mode
			return true; // Handled
		}
	}

	// Handle focus key
	if (EventArgs.Event == IE_Pressed && EventArgs.Key == EKeys::F)
	{
		FBoxSphereBounds Bounds = GetAllChildrenBounds();
		if (Bounds.SphereRadius > KINDA_SMALL_NUMBER)
		{
			FocusViewportOnBounds(Bounds, true);
		}
		return true; // Handled
	}

	// Let the base class handle other keys (including starting/stopping widget manipulation via mouse clicks)
	return FEditorViewportClient::InputKey(EventArgs);
}

bool FCEDynamicEventGroupViewportClient::InputAxis(
	FViewport* InViewport,
	FInputDeviceId DeviceId,
	FKey Key,
	float Delta,
	float DeltaTime,
	int32 NumSamples,
	bool bGamepad)
{
	// If we are manipulating the widget, let the base class handle it.
	// The base class InputAxis is responsible for routing input to the gizmo
	// AND preventing camera movement during manipulation.
	if (bIsManipulating)
	{
		// Allow engine to process axis events for fine-grained widget interaction
		return FEditorViewportClient::InputAxis(InViewport, DeviceId, Key, Delta, DeltaTime, NumSamples, bGamepad); // <<<----- UNCOMMENT THIS
		// return true; // REMOVE OR COMMENT OUT THIS LINE
	}

	// Otherwise (not manipulating), let the base class handle camera movement etc.
	return FEditorViewportClient::InputAxis(InViewport, DeviceId, Key, Delta, DeltaTime, NumSamples, bGamepad);
}

bool FCEDynamicEventGroupViewportClient::ShouldOrbitCamera() const
{
	// Return false to use standard level editor camera controls (matching constructor)
	return false;
}

void FCEDynamicEventGroupViewportClient::FocusViewportOnBounds(const FBoxSphereBounds& Bounds, bool bInstant)
{
	if (!Viewport || Bounds.SphereRadius <= KINDA_SMALL_NUMBER) return;

	// Explicitly calculate MinDistance before declaring const
	float TmpMinDistance = 1.0f; // Default near clip value

	const float MinDistance = TmpMinDistance; // Now initialize the const variable

	// Calculate distance needed to see the entire sphere based on FOV
	const float TargetDistance = FMath::Max(Bounds.SphereRadius / FMath::Tan(FMath::DegreesToRadians(ViewFOV * 0.5f)), MinDistance);

	FViewportCameraTransform& Cam = GetViewTransform();
	Cam.SetLookAt(Bounds.Origin);
	FVector NewLocation = Bounds.Origin - GetViewRotation().Vector() * TargetDistance;

	if (bInstant) { Cam.SetLocation(NewLocation); }
	else { Cam.SetLocation(NewLocation); /* Add interpolation later if needed */ }

	Invalidate();
}


void FCEDynamicEventGroupViewportClient::ProcessClick(
	FSceneView& View,
	HHitProxy* HitProxy,
	FKey Key,
	EInputEvent Event,
	uint32 HitX,
	uint32 HitY
)
{
	UE_LOG(LogTemp, Warning, TEXT("ProcessClick: Event=%d, Key=%s, HitProxy=%s"),
		(int32)Event, *Key.ToString(), HitProxy ? TEXT("VALID") : TEXT("NULL"));

	// Only handle Left Mouse Button presses for selection
	if (Event == IE_Released && Key == EKeys::LeftMouseButton)
	{
		int32 ClickedChildIndex = INDEX_NONE;
		bool bHitGizmo = false;

		if (HitProxy)
		{
			// Check what type of hit proxy we're dealing with
			if (HitProxy->IsA(HWidgetUtilProxy::StaticGetType()))
			{
				UE_LOG(LogTemp, Warning, TEXT("Hit proxy is gizmo/widget"));
				bHitGizmo = true;
				FEditorViewportClient::ProcessClick(View, HitProxy, Key, Event, HitX, HitY);
				return;  // Let base class handle gizmo
			}
			else if (HitProxy->IsA(HActor::StaticGetType()))
			{
				UE_LOG(LogTemp, Warning, TEXT("Hit proxy is Actor type"));
				HActor * ActorProxy = static_cast<HActor*>(HitProxy);
				AActor * HitActor = ActorProxy->Actor;

					if (HitActor && PreviewScenePtr)
					 {
					UE_LOG(LogTemp, Warning, TEXT("Hit actor: %s"), *HitActor->GetName());
					const auto & ChildComponents = PreviewScenePtr->GetChildActorComponents();

						                    // Try to find which child matches this actor
						for (int32 i = 0; i < ChildComponents.Num(); ++i)
						 {
						UChildActorComponent * CAC = ChildComponents[i];
						if (CAC && CAC->GetChildActor() == HitActor)
							 {
							ClickedChildIndex = i;
							UE_LOG(LogTemp, Warning,
								TEXT("Matched actor %s to child index %d"),
								*HitActor->GetName(), i);
							break;
							}
						 }

						if (ClickedChildIndex == INDEX_NONE)
						 {
						UE_LOG(LogTemp, Warning,
							TEXT("Actor %s not found in child components"),
							*HitActor->GetName());
						}
					 }
			}
			else if (HitProxy->IsA(HComponentVisProxy::StaticGetType()))
			{
				UE_LOG(LogTemp, Warning, TEXT("Hit proxy is Component type"));

				// For testing, select the first component when we hit any component
				if (PreviewScenePtr)
				{
					const TArray<UChildActorComponent*>& ChildComponents = PreviewScenePtr->GetChildActorComponents();
					if (ChildComponents.Num() > 0)
					{
						ClickedChildIndex = 0;
						UE_LOG(LogTemp, Warning, TEXT("Selected first child for testing"));
					}
				}
			}
			else
			{
				UE_LOG(LogTemp, Warning, TEXT("Unknown hit proxy type"));
			}
		}

		// Apply selection if we found something to select
		if (ClickedChildIndex != INDEX_NONE)
		{
			UE_LOG(LogTemp, Warning, TEXT("Selecting Child Index %d"), ClickedChildIndex);
			if (EditorToolkit)
			{
				EditorToolkit->SelectChild(ClickedChildIndex);
			}
			else
			{
				SetSelectedChildIndex(ClickedChildIndex);
			}
			Invalidate();
		}
		else if (!bHitGizmo)  // Only deselect if we didn't hit the gizmo
		{
			UE_LOG(LogTemp, Warning, TEXT("Deselecting"));
			if (EditorToolkit)
			{
				EditorToolkit->SelectChild(INDEX_NONE);
			}
			else
			{
				SetSelectedChildIndex(INDEX_NONE);
			}
			Invalidate();
		}
	}
	else
	{
		FEditorViewportClient::ProcessClick(View, HitProxy, Key, Event, HitX, HitY);
	}
}

void FCEDynamicEventGroupViewportClient::SetSelectedChildIndex(int32 InChildIndex)
{
	// Only update if selection changed
	if (SelectedChildIndex == InChildIndex)
	{
		UE_LOG(LogTemp, Warning, TEXT("SetSelectedChildIndex: Selection unchanged (%d)"), InChildIndex);
		return;
	}

	SelectedChildIndex = InChildIndex;

	UE_LOG(LogTemp, Warning, TEXT("SetSelectedChildIndex: %d"), SelectedChildIndex);

	// Show/hide widget based on selection
	if (SelectedChildIndex != INDEX_NONE)
	{
		if (Widget)
		{
			UE_LOG(LogTemp, Warning, TEXT("SetSelectedChildIndex: Making widget visible"));
			Widget->SetDefaultVisibility(true);
			ShowWidget(true);

			// Set widget mode to translate by default
			if (ModeTools)
			{
				ModeTools->SetWidgetMode(UE::Widget::WM_Translate);
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("SetSelectedChildIndex: Widget is NULL!"));
		}
	}
	else
	{
		if (Widget)
		{
			UE_LOG(LogTemp, Warning, TEXT("SetSelectedChildIndex: Hiding widget"));
			Widget->SetDefaultVisibility(false);
			ShowWidget(false);
		}
	}

	Invalidate();
}

//-----------------------------------------------------------------------------
// Gizmo Handling Overrides
//-----------------------------------------------------------------------------

FVector FCEDynamicEventGroupViewportClient::GetWidgetLocation() const
{
	if (SelectedChildIndex != INDEX_NONE && PreviewScenePtr) {
		if (UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedChildIndex)) {
			if (AActor* ChildActor = Comp->GetChildActor()) {
				if (IsValid(ChildActor)) {
					if (USceneComponent* RootComp = ChildActor->GetRootComponent()) {
						if (IsValid(RootComp)) { return RootComp->GetComponentLocation(); }
					}
					return ChildActor->GetActorLocation(); // Fallback to Actor location
				}
			}
			// Fallback to Component location if ChildActor invalid or not found
			if (IsValid(Comp)) { return Comp->GetComponentLocation(); }
		}
	}
	// Provide a default location if nothing valid is selected
	return FVector::ZeroVector;
}

FMatrix FCEDynamicEventGroupViewportClient::GetWidgetCoordSystem() const
{
	if (SelectedChildIndex != INDEX_NONE && PreviewScenePtr) {
		if (UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedChildIndex)) {
			if (AActor* ChildActor = Comp->GetChildActor()) {
				if (IsValid(ChildActor)) {
					if (USceneComponent* RootComp = ChildActor->GetRootComponent()) {
						if (IsValid(RootComp)) { return FRotationMatrix::Make(RootComp->GetComponentQuat()); }
					}
					return FRotationMatrix::Make(ChildActor->GetActorQuat()); // Fallback to Actor rotation
				}
			}
			// Fallback to Component rotation if ChildActor invalid or not found
			if (IsValid(Comp)) { return FRotationMatrix::Make(Comp->GetComponentQuat()); }
		}
	}
	// Default matrix if nothing valid is selected
	return FMatrix::Identity;
}

bool FCEDynamicEventGroupViewportClient::InputWidgetDelta(FViewport* InViewport, EAxisList::Type CurrentAxis, FVector& Drag, FRotator& Rot, FVector& Scale)
{
	// *** ADD THIS LOG ***
	UE_LOG(LogTemp, Warning, TEXT("InputWidgetDelta: Called. Axis=%d, bIsManipulating=%d, SelectedIndex=%d"), (int32)CurrentAxis, bIsManipulating, SelectedChildIndex);

	if (bIsManipulating && SelectedChildIndex != INDEX_NONE && PreviewScenePtr && CurrentAxis != EAxisList::None)
	{
		// *** ADD THIS LOG ***
		UE_LOG(LogTemp, Warning, TEXT("InputWidgetDelta: Processing Delta. Drag=%s, Rot=%s, Scale=%s"), *Drag.ToString(), *Rot.ToString(), *Scale.ToString());

		UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedChildIndex);

		if (Comp && IsValid(Comp))
		{
			const UE::Widget::EWidgetMode WidgetMode = GetWidgetMode();
			// Use ModeTools getter which should be valid if constructor checks passed
			const ECoordSystem CoordSystem = ModeTools ? ModeTools->GetCoordSystem() : COORD_World;

			FTransform CurrentRelativeTransform = Comp->GetRelativeTransform();
			FTransform NewRelativeTransform = CurrentRelativeTransform;

			// Get parent transform safely
			const FTransform ParentWorldTransform = Comp->GetAttachParent() ? Comp->GetAttachParent()->GetComponentTransform() : FTransform::Identity;

			bool bChanged = false;

			if (WidgetMode == UE::Widget::WM_Translate && !Drag.IsZero())
			{
				FVector RelativeDrag;
				if (CoordSystem == COORD_World)
				{
					RelativeDrag = ParentWorldTransform.InverseTransformVectorNoScale(Drag);
				}
				else // COORD_Local
				{
					// Drag is in the gizmo's local space, which matches the component's space when COORD_Local is set
					RelativeDrag = Drag; // Assume Drag is already in the correct local space
				}
				NewRelativeTransform.AddToTranslation(RelativeDrag);
				bChanged = true;
			}
			else if (WidgetMode == UE::Widget::WM_Rotate && !Rot.IsZero())
			{
				FQuat WorldRotDelta = Rot.Quaternion(); // Rotation delta is almost always world space from the gizmo
				FQuat NewRelativeQuat;

				// Apply rotation delta in world space to the component's world rotation
				FQuat CurrentWorldQuat = Comp->GetComponentQuat();
				FQuat NewWorldQuat = WorldRotDelta * CurrentWorldQuat;

				// Convert back to relative rotation
				FQuat ParentWorldQuat = ParentWorldTransform.GetRotation();
				NewRelativeQuat = ParentWorldQuat.Inverse() * NewWorldQuat;

				NewRelativeTransform.SetRotation(NewRelativeQuat.GetNormalized()); // Ensure normalization
				bChanged = true;
			}
			else if (WidgetMode == UE::Widget::WM_Scale && !Scale.IsZero())
			{
				// Apply additive scale directly in relative space (common gizmo behavior)
				NewRelativeTransform.SetScale3D(CurrentRelativeTransform.GetScale3D() + Scale);
				// Add clamping if needed: FVector MinScale(0.01f); NewRelativeTransform.SetScale3D(NewRelativeTransform.GetScale3D().ComponentMax(MinScale));
				bChanged = true;
			}

			// Apply the new transform if it actually changed
			if (bChanged && !CurrentRelativeTransform.Equals(NewRelativeTransform, 0.0001f))
			{
				// *** ADD THIS LOG ***
				UE_LOG(LogTemp, Warning, TEXT("InputWidgetDelta: Applying Transform: %s"), *NewRelativeTransform.ToString());
				Comp->SetRelativeTransform(NewRelativeTransform);
				Invalidate(); // Redraw the viewport to reflect the change
			}
			return true; // We handled the delta
		}
		else
		{
			// Component became invalid during drag, stop manipulation cleanly
			if (bIsManipulating) // Check flag before ending transaction
			{
				UE_LOG(LogTemp, Warning, TEXT("InputWidgetDelta: Component became invalid during drag. Ending transaction."));
				if (GEditor->IsTransactionActive()) GEditor->EndTransaction();
				bIsManipulating = false; // Reset flag
			}
			return false; // Indicate we didn't handle this delta
		}
		//UE_LOG(LogTemp, Warning, TEXT("InputWidgetDelta: Conditions not met, returning false. Axis=%d, bIsManipulating=%d, SelectedIndex=%d"), (int32)CurrentAxis, bIsManipulating, SelectedChildIndex);
	}
	return false; // We didn't handle it
}

//-----------------------------------------------------------------------------
// Gizmo Tracking Overrides (Replaced Begin/EndTransform)
//-----------------------------------------------------------------------------

void FCEDynamicEventGroupViewportClient::TrackingStarted(const FInputEventState& InInputState, bool bIsDraggingWidget, bool bNudge)
{
	// Check if tracking started specifically by dragging the widget (gizmo)
	// and only if we have a valid selection.
	if (bIsDraggingWidget && SelectedChildIndex != INDEX_NONE)
	{
		// Check if we are already manipulating (safety check)
		if (bIsManipulating)
		{
			UE_LOG(LogTemp, Warning, TEXT("TrackingStarted called while already manipulating! Ending previous transaction."));
			if (GEditor->IsTransactionActive()) GEditor->EndTransaction();
			bIsManipulating = false; // Reset just in case
		}

		UE_LOG(LogTemp, Log, TEXT("TrackingStarted: Starting manipulation for index %d"), SelectedChildIndex);

		// Begin transaction before Modify()
		GEditor->BeginTransaction(LOCTEXT("CEDynamicEventGroupViewportClient_Transform", "Transform Child Actor"));

		UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedChildIndex);
		if (Comp && IsValid(Comp))
		{
			Comp->Modify(); // Mark component for undo buffer
			bIsManipulating = true; // Set the flag AFTER checks and Modify()

			// If PreviewScenePtr holds a UObject that stores the definitive transforms, modify it too:
			// Example: if (auto* DataAsset = PreviewScenePtr->GetDataSource()) { DataAsset->Modify(); }
		}
		else
		{
			UE_LOG(LogTemp, Warning, TEXT("TrackingStarted: Component invalid for index %d, ending transaction."), SelectedChildIndex);
			GEditor->EndTransaction(); // End the transaction properly since we already started it
			bIsManipulating = false; // Do not set manipulating state
		}
	}
	else
	{
		// If tracking started but not by dragging our widget for our selection, pass to base class
		// This allows other viewport interactions (like camera movement start) to work
		FEditorViewportClient::TrackingStarted(InInputState, bIsDraggingWidget, bNudge);
	}
}

void FCEDynamicEventGroupViewportClient::TrackingStopped()
{
	// Only process if we were actually manipulating the widget
	if (bIsManipulating)
	{
		UE_LOG(LogTemp, Log, TEXT("TrackingStopped: Ending manipulation for index %d"), SelectedChildIndex);

		// Final update to data model if needed (the actual undo state is handled by the transaction)
		if (SelectedChildIndex != INDEX_NONE && PreviewScenePtr)
		{
			if (UChildActorComponent* Comp = PreviewScenePtr->GetChildActorComponent(SelectedChildIndex))
			{
				if (IsValid(Comp))
				{
					// Ensure PreviewScene has this function implemented
					PreviewScenePtr->UpdateChildTransform(SelectedChildIndex, Comp->GetRelativeTransform());
					UE_LOG(LogTemp, Display, TEXT("TrackingStopped: Final transform updated for child %d"), SelectedChildIndex);
				}
			}
		}

		// End the transaction if one is active
		if (GEditor->IsTransactionActive())
		{
			GEditor->EndTransaction();
		}
		else {
			UE_LOG(LogTemp, Warning, TEXT("TrackingStopped: Was manipulating but no transaction was active!"));
		}

		bIsManipulating = false; // Reset the flag AFTER processing
	}
	else
	{
		// If tracking stopped and we weren't manipulating our gizmo, pass to base class
		// This allows camera movement stop etc.
		FEditorViewportClient::TrackingStopped();
	}
}

//-----------------------------------------------------------------------------
// Utility Functions
//-----------------------------------------------------------------------------

FBoxSphereBounds FCEDynamicEventGroupViewportClient::GetAllChildrenBounds() const
{
	if (!PreviewScenePtr)
	{
		UE_LOG(LogTemp, Warning, TEXT("GetAllChildrenBounds: PreviewScenePtr is null"));
		return FBoxSphereBounds(FSphere(FVector::ZeroVector, 100.f)); // Default bounds
	}

	FBoxSphereBounds CombinedBounds(ForceInit);
	bool bFoundValidBounds = false;

	const TArray<UChildActorComponent*>& Components = PreviewScenePtr->GetChildActorComponents();
	for (int32 i = 0; i < Components.Num(); ++i)
	{
		UChildActorComponent* Comp = Components[i];
		if (Comp && IsValid(Comp)) // Check component validity
		{
			AActor* ChildActor = Comp->GetChildActor();
			if (ChildActor && IsValid(ChildActor)) // Check actor validity
			{
				// UE_LOG(LogTemp, Verbose, TEXT("GetAllChildrenBounds: Getting bounds for Actor: %s (Index: %d)"), *ChildActor->GetName(), i);
				FBoxSphereBounds ActorBounds;
				bool bIncludeNonColliding = true;

				// Add extra safety specifically around the bounds calculation if it's crashing
				try
				{
					ActorBounds = ChildActor->GetComponentsBoundingBox(bIncludeNonColliding);
				}
				catch (...)
				{
					UE_LOG(LogTemp, Error, TEXT("GetAllChildrenBounds: Exception getting bounds for Actor: %s (Index: %d)"), *ChildActor->GetName(), i);
					continue; // Skip this actor if bounds calculation fails
				}

				if (ActorBounds.SphereRadius > KINDA_SMALL_NUMBER)
				{
					CombinedBounds = bFoundValidBounds ? CombinedBounds + ActorBounds : ActorBounds;
					bFoundValidBounds = true;
					// UE_LOG(LogTemp, Verbose, TEXT("GetAllChildrenBounds: Added bounds for %s. Combined Radius: %f"), *ChildActor->GetName(), CombinedBounds.SphereRadius);
				}
				// else { UE_LOG(LogTemp, Verbose, TEXT("GetAllChildrenBounds: Actor %s has zero radius bounds."), *ChildActor->GetName()); }
			}
			// else { UE_LOG(LogTemp, Warning, TEXT("GetAllChildrenBounds: Child Actor is invalid for component %s (Index: %d)"), *Comp->GetName(), i); }
		}
		// else { UE_LOG(LogTemp, Warning, TEXT("GetAllChildrenBounds: Component at index %d is invalid."), i); }
	}

	if (!bFoundValidBounds)
	{
		UE_LOG(LogTemp, Warning, TEXT("GetAllChildrenBounds: No valid bounds found for any child actors. Returning default bounds."));
		return FBoxSphereBounds(FSphere(FVector::ZeroVector, 100.f)); // Default bounds if none found
	}

	// UE_LOG(LogTemp, Display, TEXT("GetAllChildrenBounds: Final Combined Bounds Origin=(%s), Extent=(%s), Radius=%f"),
	//	*CombinedBounds.Origin.ToString(), *CombinedBounds.BoxExtent.ToString(), CombinedBounds.SphereRadius);

	return CombinedBounds;
}

// Make sure to undefine the namespace at the end of the file
#undef LOCTEXT_NAMESPACE