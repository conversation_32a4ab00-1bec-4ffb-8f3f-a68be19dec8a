// CEItemDefinitionEditor.cpp
#include "CEType/CEItemDefinitionEditor.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/AssetManager.h"

UCEItemDefinitionEditor::UCEItemDefinitionEditor(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Initialize with default values
    ActiveFilterCategory = nullptr;
    ActiveFilterUsage = nullptr;
    ActiveFilterValue = nullptr;
    SearchText = TEXT("");

    NominalMultiplier = 1.0f;
    MinMultiplier = 1.0f;
    LifetimeMultiplier = 1.0f;
    RestockMultiplier = 1.0f;
    BulkEditOperation = EBulkEditOperation::Multiply;
}

TArray<UCEType*> UCEItemDefinitionEditor::LoadAllCETypeAssets()
{
    TArray<UCEType*> Result;

    // Get the asset registry module
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");

    // Create a filter to find all CE Type assets
    FARFilter Filter;
    // Use ClassPaths instead of ClassNames (which is deprecated)
    Filter.ClassPaths.Add(UCEType::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;

    // Get all assets matching the filter
    TArray<FAssetData> AssetData;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    // Load each asset
    for (const FAssetData& Asset : AssetData)
    {
        UCEType* CETypeAsset = Cast<UCEType>(Asset.GetAsset());
        if (CETypeAsset)
        {
            Result.Add(CETypeAsset);
        }
    }

    return Result;
}

TArray<UCEType*> UCEItemDefinitionEditor::FilterCETypeAssets(const TArray<UCEType*>& Assets)
{
    TArray<UCEType*> FilteredAssets;

    for (UCEType* Asset : Assets)
    {
        // Skip if null
        if (!Asset)
        {
            continue;
        }

        // Filter by category if specified
        if (!ActiveFilterCategory.IsNull())
        {
            // Get the category path for comparison
            FString FilterCategoryPath = ActiveFilterCategory.ToString();

            if (!Asset->Category.IsNull())
            {
                FString AssetCategoryPath = Asset->Category.ToString();
                if (AssetCategoryPath != FilterCategoryPath)
                {
                    continue;
                }
            }
            else // Asset has no category but we're filtering by category
            {
                continue;
            }
        }

        // Filter by usage if specified
        if (!ActiveFilterUsage.IsNull())
        {
            // Get the usage path for comparison
            FString FilterUsagePath = ActiveFilterUsage.ToString();

            // Check if the asset has this usage
            bool bHasUsage = false;
            for (const TSoftObjectPtr<UCEUsage>& Usage : Asset->Usage)
            {
                if (!Usage.IsNull())
                {
                    FString AssetUsagePath = Usage.ToString();
                    if (AssetUsagePath == FilterUsagePath)
                    {
                        bHasUsage = true;
                        break;
                    }
                }
            }

            if (!bHasUsage)
            {
                continue;
            }
        }

        // Filter by value if specified
        if (!ActiveFilterValue.IsNull())
        {
            // Get the value path for comparison
            FString FilterValuePath = ActiveFilterValue.ToString();

            // Check if the asset has this value
            bool bHasValue = false;
            for (const TSoftObjectPtr<UCEValue>& Value : Asset->Value)
            {
                if (!Value.IsNull())
                {
                    FString AssetValuePath = Value.ToString();
                    if (AssetValuePath == FilterValuePath)
                    {
                        bHasValue = true;
                        break;
                    }
                }
            }

            if (!bHasValue)
            {
                continue;
            }
        }

        // Filter by name if specified
        if (!SearchText.IsEmpty())
        {
            if (!Asset->GetName().Contains(SearchText))
            {
                continue;
            }
        }

        // Asset passed all filters
        FilteredAssets.Add(Asset);
    }

    return FilteredAssets;
}

void UCEItemDefinitionEditor::ApplyBulkEdit(const TArray<UCEType*>& Assets)
{
    for (UCEType* Asset : Assets)
    {
        if (!Asset)
        {
            continue;
        }

        // Apply edits based on the selected operation
        switch (BulkEditOperation)
        {
            case EBulkEditOperation::Multiply:
                // Apply multipliers to the properties
                if (NominalMultiplier != 1.0f)
                {
                    Asset->Nominal = FMath::Max(0, FMath::RoundToInt(Asset->Nominal * NominalMultiplier));
                }

                if (MinMultiplier != 1.0f)
                {
                    Asset->Min = FMath::Max(0, FMath::RoundToInt(Asset->Min * MinMultiplier));
                }

                if (LifetimeMultiplier != 1.0f)
                {
                    Asset->Lifetime = FMath::Max(0, FMath::RoundToInt(Asset->Lifetime * LifetimeMultiplier));
                }

                if (RestockMultiplier != 1.0f)
                {
                    Asset->Restock = FMath::Max(0, FMath::RoundToInt(Asset->Restock * RestockMultiplier));
                }
                break;

            case EBulkEditOperation::Add:
                // Add values (using multipliers as the values to add)
                Asset->Nominal = FMath::Max(0, Asset->Nominal + FMath::RoundToInt(NominalMultiplier * 10));
                Asset->Min = FMath::Max(0, Asset->Min + FMath::RoundToInt(MinMultiplier * 10));
                Asset->Lifetime = FMath::Max(0, Asset->Lifetime + FMath::RoundToInt(LifetimeMultiplier * 100));
                Asset->Restock = FMath::Max(0, Asset->Restock + FMath::RoundToInt(RestockMultiplier * 10));
                break;

            case EBulkEditOperation::Set:
                // Set to fixed values (using multipliers as the values to set)
                Asset->Nominal = FMath::Max(0, FMath::RoundToInt(NominalMultiplier * 10));
                Asset->Min = FMath::Max(0, FMath::RoundToInt(MinMultiplier * 10));
                Asset->Lifetime = FMath::Max(0, FMath::RoundToInt(LifetimeMultiplier * 1000));
                Asset->Restock = FMath::Max(0, FMath::RoundToInt(RestockMultiplier * 100));
                break;
        }

        // Mark the asset as dirty
        Asset->MarkPackageDirty();
    }
}

void UCEItemDefinitionEditor::SaveChanges(const TArray<UCEType*>& Assets)
{
    for (UCEType* Asset : Assets)
    {
        if (!Asset)
        {
            continue;
        }

        // Mark the asset as dirty to ensure it gets saved
        Asset->MarkPackageDirty();
    }
}
