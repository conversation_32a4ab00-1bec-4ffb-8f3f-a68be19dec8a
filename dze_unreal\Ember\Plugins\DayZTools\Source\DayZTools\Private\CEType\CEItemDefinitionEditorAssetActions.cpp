// CEItemDefinitionEditorAssetActions.cpp
#include "CEType/CEItemDefinitionEditorAssetActions.h"
#include "CEType/CEItemDefinitionEditor.h"
#include "CEType/CEItemDefinitionEditorToolkit.h"

FCEItemDefinitionEditorAssetActions::FCEItemDefinitionEditorAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FCEItemDefinitionEditorAssetActions::GetName() const
{
    return FText::FromString(TEXT("CE Item Definition Editor"));
}

FColor FCEItemDefinitionEditorAssetActions::GetTypeColor() const
{
    // Use a distinctive color for CE Item Definition Editor assets
    return FColor(255, 140, 0); // Orange
}

UClass* FCEItemDefinitionEditorAssetActions::GetSupportedClass() const
{
    return UCEItemDefinitionEditor::StaticClass();
}

uint32 FCEItemDefinitionEditorAssetActions::GetCategories()
{
    return AssetCategory;
}

void FCEItemDefinitionEditorAssetActions::OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor)
{
    const EToolkitMode::Type Mode = EditWithinLevelEditor.IsValid() ? EToolkitMode::WorldCentric : EToolkitMode::Standalone;

    for (UObject* Object : InObjects)
    {
        UCEItemDefinitionEditor* EditorAsset = Cast<UCEItemDefinitionEditor>(Object);
        if (EditorAsset)
        {
            TSharedRef<FCEItemDefinitionEditorToolkit> NewEditor = MakeShareable(new FCEItemDefinitionEditorToolkit());
            NewEditor->InitCEItemDefinitionEditorToolkit(Mode, EditWithinLevelEditor, EditorAsset);
        }
    }
}
