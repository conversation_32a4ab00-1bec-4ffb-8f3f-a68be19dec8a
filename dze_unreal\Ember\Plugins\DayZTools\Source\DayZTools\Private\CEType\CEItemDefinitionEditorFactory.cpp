// CEItemDefinitionEditorFactory.cpp
#include "CEType/CEItemDefinitionEditorFactory.h"
#include "CEType/CEItemDefinitionEditor.h"

UCEItemDefinitionEditorFactory::UCEItemDefinitionEditorFactory()
{
    SupportedClass = UCEItemDefinitionEditor::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UCEItemDefinitionEditorFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new CE Item Definition Editor asset
    UCEItemDefinitionEditor* NewAsset = NewObject<UCEItemDefinitionEditor>(InParent, Class, Name, Flags);
    return NewAsset;
}

bool UCEItemDefinitionEditorFactory::ShouldShowInNewMenu() const
{
    return true;
}
