// CEItemDefinitionEditorToolkit.cpp
#include "CEType/CEItemDefinitionEditorToolkit.h"
#include "Widgets/Docking/SDockTab.h"
#include "PropertyEditorModule.h"
#include "Modules/ModuleManager.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SBorder.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SSearchBox.h"  // Add search box widget
#include "EditorStyleSet.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Framework/Application/SlateApplication.h"
#include "IDetailsView.h"
#include "DayZToolsSettings.h"
#include "Misc/FileHelper.h"
#include "HAL/PlatformFileManager.h"
#include "GenericPlatform/GenericPlatformFile.h"
#include "Misc/MessageDialog.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Styling/AppStyle.h"

#define LOCTEXT_NAMESPACE "CEItemDefinitionEditorToolkit"

// Define the tab identifiers
const FName FCEItemDefinitionEditorToolkit::ItemsTabID(TEXT("Items"));
const FName FCEItemDefinitionEditorToolkit::DetailsTabID(TEXT("Details"));
const FName FCEItemDefinitionEditorToolkit::FiltersTabID(TEXT("Filters"));
const FName FCEItemDefinitionEditorToolkit::BulkEditTabID(TEXT("BulkEdit"));

FCEItemDefinitionEditorToolkit::FCEItemDefinitionEditorToolkit()
    : EditorAsset(nullptr)
    , SortMode(EColumnSortMode::None)
    , SortColumn(NAME_None)
{
    StatusMessage = LOCTEXT("InitialStatus", "Ready to load items");
}

FCEItemDefinitionEditorToolkit::~FCEItemDefinitionEditorToolkit()
{
}

void FCEItemDefinitionEditorToolkit::InitCEItemDefinitionEditorToolkit(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UCEItemDefinitionEditor* InEditorAsset)
{
    EditorAsset = InEditorAsset;

    // Initialize the editor layout
    TSharedRef<FTabManager::FLayout> Layout = CreateEditorLayout();

    // Initialize the asset editor toolkit
    InitAssetEditor(Mode, InitToolkitHost, FName("CEItemDefinitionEditorToolkit"), Layout, true, true, EditorAsset);

    // Load all CE Type assets
    RefreshItemList();
}

TSharedRef<FTabManager::FLayout> FCEItemDefinitionEditorToolkit::CreateEditorLayout()
{
    return FTabManager::NewLayout("Standalone_CEItemDefinitionEditorToolkit_Layout")
        ->AddArea
        (
            FTabManager::NewPrimaryArea()
            ->SetOrientation(Orient_Vertical)
            ->Split
            (
                FTabManager::NewSplitter()
                ->SetOrientation(Orient_Horizontal)
                ->SetSizeCoefficient(0.9f)
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.6f)
                    ->AddTab(ItemsTabID, ETabState::OpenedTab)  // Items tab
                )
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.4f)
                    ->AddTab(DetailsTabID, ETabState::OpenedTab)  // Details tab
                )
            )
            ->Split
            (
                FTabManager::NewStack()
                ->SetSizeCoefficient(0.1f)
                ->AddTab(BulkEditTabID, ETabState::OpenedTab)  // Bulk Edit tab only
            )
        );
}

void FCEItemDefinitionEditorToolkit::RegisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::RegisterTabSpawners(InTabManager);

    InTabManager->RegisterTabSpawner(ItemsTabID, FOnSpawnTab::CreateSP(this, &FCEItemDefinitionEditorToolkit::SpawnItemsTab))
        .SetDisplayName(LOCTEXT("ItemsTab", "Items"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef());

    InTabManager->RegisterTabSpawner(DetailsTabID, FOnSpawnTab::CreateSP(this, &FCEItemDefinitionEditorToolkit::SpawnDetailsTab))
        .SetDisplayName(LOCTEXT("DetailsTab", "Details"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef());

    InTabManager->RegisterTabSpawner(BulkEditTabID, FOnSpawnTab::CreateSP(this, &FCEItemDefinitionEditorToolkit::SpawnBulkEditTab))
        .SetDisplayName(LOCTEXT("BulkEditTab", "Bulk Edit"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef());
}

void FCEItemDefinitionEditorToolkit::UnregisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::UnregisterTabSpawners(InTabManager);

    InTabManager->UnregisterTabSpawner(ItemsTabID);
    InTabManager->UnregisterTabSpawner(DetailsTabID);
    InTabManager->UnregisterTabSpawner(BulkEditTabID);
}

TSharedRef<SDockTab> FCEItemDefinitionEditorToolkit::SpawnItemsTab(const FSpawnTabArgs& Args)
{
    // Create the list view for CE Type assets
    ItemListView = SNew(SListView<UCEType*>)
        .ListItemsSource(&FilteredItems)
        .OnGenerateRow(this, &FCEItemDefinitionEditorToolkit::OnGenerateRow)
        .OnSelectionChanged(this, &FCEItemDefinitionEditorToolkit::OnSelectionChanged)
        .SelectionMode(ESelectionMode::Multi)
        .HeaderRow
        (
            SNew(SHeaderRow)
            + SHeaderRow::Column("Name")
            .DefaultLabel(LOCTEXT("NameColumn", "Name"))
            .FillWidth(0.2f)
            .SortMode_Lambda([this]() { return SortColumn == "Name" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)

            + SHeaderRow::Column("Category")
            .DefaultLabel(LOCTEXT("CategoryColumn", "Category"))
            .FillWidth(0.1f)
            .SortMode_Lambda([this]() { return SortColumn == "Category" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)

            + SHeaderRow::Column("Nominal")
            .DefaultLabel(LOCTEXT("NominalColumn", "Nominal"))
            .FillWidth(0.07f)
            .SortMode_Lambda([this]() { return SortColumn == "Nominal" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)

            + SHeaderRow::Column("Min")
            .DefaultLabel(LOCTEXT("MinColumn", "Min"))
            .FillWidth(0.07f)
            .SortMode_Lambda([this]() { return SortColumn == "Min" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)

            + SHeaderRow::Column("Lifetime")
            .DefaultLabel(LOCTEXT("LifetimeColumn", "Lifetime"))
            .FillWidth(0.08f)
            .SortMode_Lambda([this]() { return SortColumn == "Lifetime" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)

            + SHeaderRow::Column("Restock")
            .DefaultLabel(LOCTEXT("RestockColumn", "Restock"))
            .FillWidth(0.1f)
            .SortMode_Lambda([this]() { return SortColumn == "Restock" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)

            + SHeaderRow::Column("Cost")
            .DefaultLabel(LOCTEXT("CostColumn", "Cost"))
            .FillWidth(0.1f)
            .SortMode_Lambda([this]() { return SortColumn == "Cost" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)

            + SHeaderRow::Column("Usage")
            .DefaultLabel(LOCTEXT("UsageColumn", "Usage"))
            .FillWidth(0.15f)
            .SortMode_Lambda([this]() { return SortColumn == "Usage" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)

            + SHeaderRow::Column("Value")
            .DefaultLabel(LOCTEXT("ValueColumn", "Value"))
            .FillWidth(0.15f)
            .SortMode_Lambda([this]() { return SortColumn == "Value" ? SortMode : EColumnSortMode::None; })
            .OnSort(this, &FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged)
        );

    // Create the filters panel
    SAssignNew(FiltersPanelContainer, SBorder)
        .BorderImage(FAppStyle::Get().GetBrush("ToolPanel.GroupBorder"))
        .Padding(4.0f)
        .Visibility(EVisibility::Collapsed)
        [
            SNew(SBox)
            .MinDesiredWidth(200.0f)
            [
                SAssignNew(FiltersPanel, SVerticalBox)
            ]
        ];

    // Create and initialize the filters panel
    RebuildFiltersPanel();

    return SNew(SDockTab)
        .Label(LOCTEXT("ItemsTabLabel", "Items"))
        [
            SNew(SVerticalBox)

            // Add toolbar with buttons
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(2)
            [
                SNew(SHorizontalBox)
                + SHorizontalBox::Slot()
                .AutoWidth()
                .Padding(2)
                [
                    SNew(SButton)
                    .Text(LOCTEXT("RefreshButton", "Refresh"))
                    .ToolTipText(LOCTEXT("RefreshButtonTooltip", "Refresh the list of CE Type assets"))
                    .OnClicked_Lambda([this]() {
                        RefreshItemList();
                        return FReply::Handled();
                    })
                ]

                + SHorizontalBox::Slot()
                .AutoWidth()
                .Padding(2)
                [
                    SNew(SButton)
                    .Text(LOCTEXT("ExportXMLButton", "Export XML"))
                    .ToolTipText(LOCTEXT("ExportXMLButtonTooltip", "Export all CE Type assets to types.xml"))
                    .OnClicked_Lambda([this]() {
                        ExportTypesToXML();
                        return FReply::Handled();
                    })
                ]

                // Add filter button
                + SHorizontalBox::Slot()
                .AutoWidth()
                .Padding(2, 0)
                .VAlign(VAlign_Center)
                [
                    SNew(SComboButton)
                    .ComboButtonStyle(FAppStyle::Get(), "ToolbarComboButton")
                    .ButtonStyle(FAppStyle::Get(), "ToggleButton")
                    .ForegroundColor(FSlateColor::UseForeground())
                    .ContentPadding(FMargin(1, 0))
                    .OnGetMenuContent(this, &FCEItemDefinitionEditorToolkit::MakeFilterMenu)
                    .HasDownArrow(true)
                    .ButtonContent()
                    [
                        SNew(SHorizontalBox)
                        + SHorizontalBox::Slot()
                        .AutoWidth()
                        .VAlign(VAlign_Center)
                        [
                            SNew(SImage)
                            .Image(FAppStyle::Get().GetBrush("Icons.Filter"))
                            .ColorAndOpacity(FSlateColor::UseForeground())
                        ]
                        + SHorizontalBox::Slot()
                        .AutoWidth()
                        .Padding(2, 0, 0, 0)
                        .VAlign(VAlign_Center)
                        [
                            SNew(STextBlock)
                            .Text(LOCTEXT("FiltersButtonText", "Filters"))
                        ]
                    ]
                ]

                // Add search box
                + SHorizontalBox::Slot()
                .FillWidth(1.0f)
                .Padding(5, 0, 5, 0)
                .VAlign(VAlign_Center)
                [
                    SAssignNew(SearchBox, SSearchBox)
                    .HintText(LOCTEXT("SearchItemsHint", "Search items..."))
                    .OnTextChanged(this, &FCEItemDefinitionEditorToolkit::OnSearchTextChanged)
                    .DelayChangeNotificationsWhileTyping(false) // Immediate updates while typing
                ]

                + SHorizontalBox::Slot()
                .AutoWidth()
                .HAlign(HAlign_Right)
                .Padding(2)
                [
                    SAssignNew(StatusTextBlock, STextBlock)
                    .Text_Lambda([this]() {
                        if (SelectedItems.Num() > 0)
                        {
                            return FText::Format(LOCTEXT("ItemCountWithSelectionAndStatus", "{0} | Selected: {1} | Total: {2}"),
                                StatusMessage,
                                FText::AsNumber(SelectedItems.Num()),
                                FText::AsNumber(FilteredItems.Num()));
                        }
                        else
                        {
                            return FText::Format(LOCTEXT("ItemCountWithStatus", "{0} | Items: {1}"),
                                StatusMessage,
                                FText::AsNumber(FilteredItems.Num()));
                        }
                    })
                ]
            ]

            // Add the main content area
            + SVerticalBox::Slot()
            .FillHeight(1.0f)
            .Padding(2)
            [
                SNew(SHorizontalBox)

                // Add the filters panel on the left (hidden by default)
                + SHorizontalBox::Slot()
                .AutoWidth()
                [
                    FiltersPanelContainer.ToSharedRef()
                ]

                // Add the list view
                + SHorizontalBox::Slot()
                .FillWidth(1.0f)
                [
                    ItemListView.ToSharedRef()
                ]
            ]
        ];
}

TSharedRef<SDockTab> FCEItemDefinitionEditorToolkit::SpawnDetailsTab(const FSpawnTabArgs& Args)
{
    // Create the details view for the selected CE Type asset
    ItemDetailsView = CreateDetailsView(nullptr);

    // Set up the details view to hide the filter settings
    ItemDetailsView->SetIsPropertyVisibleDelegate(FIsPropertyVisible::CreateLambda([](const FPropertyAndParent& PropertyAndParent) {
        // Get the category name
        FString CategoryName = PropertyAndParent.Property.GetMetaData("Category");

        // Hide properties in the "Filter Settings" category since we now use the new content browser-style filtering
        if (CategoryName.Equals("Filter Settings"))
        {
            return false;
        }
        
        return true;
    }));

    return SNew(SDockTab)
        .Label(LOCTEXT("DetailsTabLabel", "Details"))
        [
            ItemDetailsView.ToSharedRef()
        ];
}

TSharedRef<SDockTab> FCEItemDefinitionEditorToolkit::SpawnFiltersTab(const FSpawnTabArgs& Args)
{
    // Create a custom details view that only shows filter settings
    FPropertyEditorModule& PropertyEditorModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");

    FDetailsViewArgs DetailsViewArgs;
    DetailsViewArgs.bUpdatesFromSelection = false;
    DetailsViewArgs.bLockable = false;
    DetailsViewArgs.bAllowSearch = false;
    DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
    DetailsViewArgs.bHideSelectionTip = true;
    DetailsViewArgs.bShowPropertyMatrixButton = false;

    FiltersView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);

    // Set the object
    FiltersView->SetObject(EditorAsset);

    // Set up the filter view to only show filter settings
    // We can't hide categories directly, so we'll use a custom filter
    FiltersView->SetIsPropertyVisibleDelegate(FIsPropertyVisible::CreateLambda([](const FPropertyAndParent& PropertyAndParent) {
        // Get the category name
        FString CategoryName = PropertyAndParent.Property.GetMetaData("Category");
        
        // Get the property name
        FString PropertyName = PropertyAndParent.Property.GetName();
        
        // Skip the NameFilter property since we moved it to the Items tab
        if (PropertyName.Equals("NameFilter"))
        {
            return false;
        }

        // Only show properties in the "Filter Settings" category
        // Make sure to use exact match to avoid showing properties from other categories
        return CategoryName.Equals("Filter Settings");
    }));

    return SNew(SDockTab)
        .Label(LOCTEXT("FiltersTabLabel", "Filters"))
        [
            SNew(SVerticalBox)

            // Add the filters view
            + SVerticalBox::Slot()
            .FillHeight(1.0f)
            .Padding(2)
            [
                FiltersView.ToSharedRef()
            ]

            // Add apply filters button
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(2)
            [
                SNew(SButton)
                .Text(LOCTEXT("ApplyFiltersButton", "Apply Filters"))
                .OnClicked_Lambda([this]() {
                    FilterItems();
                    return FReply::Handled();
                })
            ]
        ];
}

TSharedRef<SDockTab> FCEItemDefinitionEditorToolkit::SpawnBulkEditTab(const FSpawnTabArgs& Args)
{
    // Create a custom details view that only shows bulk edit settings
    FPropertyEditorModule& PropertyEditorModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");

    FDetailsViewArgs DetailsViewArgs;
    DetailsViewArgs.bUpdatesFromSelection = false;
    DetailsViewArgs.bLockable = false;
    DetailsViewArgs.bAllowSearch = false;
    DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
    DetailsViewArgs.bHideSelectionTip = true;
    DetailsViewArgs.bShowPropertyMatrixButton = false;

    BulkEditView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);

    // Set the object
    BulkEditView->SetObject(EditorAsset);

    // Set up the bulk edit view to ONLY show bulk edit settings and NOTHING else
    BulkEditView->SetIsPropertyVisibleDelegate(FIsPropertyVisible::CreateLambda([](const FPropertyAndParent& PropertyAndParent) {
        FString CategoryName = PropertyAndParent.Property.GetMetaData("Category");
        
        // ONLY show properties that are explicitly in the "Bulk Edit Settings" category
        // And explicitly reject any properties from "Filter Settings"
        return (CategoryName.Equals("Bulk Edit Settings") && !PropertyAndParent.Property.GetName().Contains("Filter"));
    }));

    return SNew(SDockTab)
        .Label(LOCTEXT("BulkEditTabLabel", "Bulk Edit"))
        [
            SNew(SVerticalBox)

            // Add the bulk edit view
            + SVerticalBox::Slot()
            .FillHeight(1.0f)
            .Padding(2)
            [
                BulkEditView.ToSharedRef()
            ]

            // Add apply bulk edits button
            + SVerticalBox::Slot()
            .AutoHeight()
            .Padding(2)
            [
                SNew(SHorizontalBox)
                + SHorizontalBox::Slot()
                .AutoWidth()
                .Padding(2)
                [
                    SNew(SButton)
                    .Text(LOCTEXT("ApplyBulkEditsButton", "Apply Bulk Edits"))
                    .OnClicked_Lambda([this]() {
                        ApplyBulkEdits();
                        return FReply::Handled();
                    })
                ]

                + SHorizontalBox::Slot()
                .AutoWidth()
                .Padding(2)
                [
                    SNew(SButton)
                    .Text(LOCTEXT("SaveChangesButton", "Save Changes"))
                    .OnClicked_Lambda([this]() {
                        SaveChanges();
                        return FReply::Handled();
                    })
                ]
            ]
        ];
}

FName FCEItemDefinitionEditorToolkit::GetToolkitFName() const
{
    return FName("CEItemDefinitionEditorToolkit");
}

FText FCEItemDefinitionEditorToolkit::GetBaseToolkitName() const
{
    return LOCTEXT("AppLabel", "CE Item Definition Editor");
}

FString FCEItemDefinitionEditorToolkit::GetWorldCentricTabPrefix() const
{
    return TEXT("CEItemDefinitionEditor");
}

FLinearColor FCEItemDefinitionEditorToolkit::GetWorldCentricTabColorScale() const
{
    return FLinearColor(1.0f, 0.5f, 0.0f);
}

void FCEItemDefinitionEditorToolkit::RefreshItemList()
{
    // Update status
    StatusMessage = LOCTEXT("LoadingStatus", "Loading assets...");

    // Use a task to load assets asynchronously
    AsyncTask(ENamedThreads::GameThread, [this]() {
        // Load all CE Type assets
        LoadedItems = EditorAsset->LoadAllCETypeAssets();

        // Update status
        StatusMessage = LOCTEXT("LoadedStatus", "Assets loaded");

        // Apply filters
        FilterItems();
    });
}

void FCEItemDefinitionEditorToolkit::ApplyBulkEdits()
{
    // Use the selected items directly
    if (EditorAsset && SelectedItems.Num() > 0)
    {
        // Update status
        StatusMessage = LOCTEXT("ApplyingEditsStatus", "Applying bulk edits...");

        // Apply bulk edits to selected items
        EditorAsset->ApplyBulkEdit(SelectedItems);

        // Update status
        FText EditMessage = FText::Format(LOCTEXT("EditsAppliedStatus", "Bulk edits applied to {0} items"), FText::AsNumber(SelectedItems.Num()));
        StatusMessage = EditMessage;

        // Refresh the list view to show updated values
        ItemListView->RebuildList();
    }
    else
    {
        // No items selected
        StatusMessage = LOCTEXT("NoItemsSelectedForEdit", "No items selected for bulk edit");
    }
}

void FCEItemDefinitionEditorToolkit::SaveChanges()
{
    // Use selected items directly
    TArray<UCEType*> ItemsToSave = SelectedItems;

    // If no items are selected, save all filtered items
    if (ItemsToSave.Num() == 0 && FilteredItems.Num() > 0)
    {
        ItemsToSave = FilteredItems;
    }

    if (EditorAsset && ItemsToSave.Num() > 0)
    {
        // Update status
        StatusMessage = LOCTEXT("SavingStatus", "Saving changes...");

        // Save changes to items
        EditorAsset->SaveChanges(ItemsToSave);

        // Update status
        FText SaveMessage = FText::Format(LOCTEXT("SavedStatus", "Changes saved for {0} items"), FText::AsNumber(ItemsToSave.Num()));
        StatusMessage = SaveMessage;

        // Refresh the list view to show updated values
        ItemListView->RebuildList();
    }
    else
    {
        // No items to save
        StatusMessage = LOCTEXT("NoItemsToSave", "No items to save");
    }
}

void FCEItemDefinitionEditorToolkit::FilterItems()
{
    if (EditorAsset)
    {
        // Update status
        StatusMessage = LOCTEXT("FilteringStatus", "Filtering items...");

        // Apply filters to loaded items
        FilteredItems = EditorAsset->FilterCETypeAssets(LoadedItems);

        // Sort items if a sort column is set
        if (SortColumn != NAME_None)
        {
            SortItems();
        }

        // Update status
        FText FilterMessage = FText::Format(LOCTEXT("FilteredStatus", "Filtered {0} items"), FText::AsNumber(FilteredItems.Num()));
        StatusMessage = FilterMessage;

        // Refresh the list view
        ItemListView->RequestListRefresh();
    }
}

TSharedRef<ITableRow> FCEItemDefinitionEditorToolkit::OnGenerateRow(UCEType* Item, const TSharedRef<STableViewBase>& OwnerTable)
{
    // Create a row with custom styling for selected items
    return SNew(STableRow<UCEType*>, OwnerTable)
        .Style(FCoreStyle::Get(), "TableView.Row")
        [
            SNew(SHorizontalBox)

            // Name column
            + SHorizontalBox::Slot()
            .FillWidth(0.2f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    return FText::FromString(Item->GetName());
                })
            ]

            // Category column
            + SHorizontalBox::Slot()
            .FillWidth(0.1f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    if (Item && !Item->Category.IsNull())
                    {
                        // Get the category name from the soft object path
                        FString CategoryPath = Item->Category.ToString();
                        FString CategoryName = FPaths::GetBaseFilename(CategoryPath);

                        // Try to load the category asset if possible
                        UCECategory* Category = Item->Category.LoadSynchronous();
                        if (Category)
                        {
                            return FText::FromString(Category->GetName());
                        }

                        // Fall back to the path name if loading fails
                        return FText::FromString(CategoryName);
                    }
                    return FText::FromString(TEXT("None"));
                })
            ]

            // Nominal column
            + SHorizontalBox::Slot()
            .FillWidth(0.07f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    return FText::AsNumber(Item->Nominal);
                })
            ]

            // Min column
            + SHorizontalBox::Slot()
            .FillWidth(0.07f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    return FText::AsNumber(Item->Min);
                })
            ]

            // Lifetime column
            + SHorizontalBox::Slot()
            .FillWidth(0.08f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    return FText::AsNumber(Item->Lifetime);
                })
            ]

            // Restock column
            + SHorizontalBox::Slot()
            .FillWidth(0.1f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    return FText::AsNumber(Item->Restock);
                })
            ]

            // Cost column
            + SHorizontalBox::Slot()
            .FillWidth(0.1f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    return FText::AsNumber(Item->Cost);
                })
            ]

            // Usage column
            + SHorizontalBox::Slot()
            .FillWidth(0.15f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    FString UsageText;
                    for (int32 i = 0; i < Item->Usage.Num(); i++)
                    {
                        if (!Item->Usage[i].IsNull())
                        {
                            // Get the usage name from the path
                            FString UsagePath = Item->Usage[i].ToString();
                            FString UsageName = FPaths::GetBaseFilename(UsagePath);

                            // Add to the text
                            if (i > 0)
                            {
                                UsageText += TEXT(", ");
                            }
                            UsageText += UsageName;
                        }
                    }
                    return FText::FromString(UsageText);
                })
            ]

            // Value column
            + SHorizontalBox::Slot()
            .FillWidth(0.15f)
            .Padding(2)
            [
                SNew(STextBlock)
                .Text_Lambda([Item]() {
                    FString ValueText;
                    for (int32 i = 0; i < Item->Value.Num(); i++)
                    {
                        if (!Item->Value[i].IsNull())
                        {
                            // Get the value name from the path
                            FString ValuePath = Item->Value[i].ToString();
                            FString ValueName = FPaths::GetBaseFilename(ValuePath);

                            // Add to the text
                            if (i > 0)
                            {
                                ValueText += TEXT(", ");
                            }
                            ValueText += ValueName;
                        }
                    }
                    return FText::FromString(ValueText);
                })
            ]
        ];
}

void FCEItemDefinitionEditorToolkit::OnSelectionChanged(UCEType* Item, ESelectInfo::Type SelectInfo)
{
    // Get all selected items
    SelectedItems.Empty();
    ItemListView->GetSelectedItems(SelectedItems);

    // Update the details view
    if (ItemDetailsView.IsValid())
    {
        if (SelectedItems.Num() == 1)
        {
            // If only one item is selected, show its details
            ItemDetailsView->SetObject(SelectedItems[0]);
        }
        else if (SelectedItems.Num() > 1)
        {
            // If multiple items are selected, show a message
            ItemDetailsView->SetObject(nullptr);

            // Update status to show selection count
            StatusMessage = FText::Format(LOCTEXT("MultipleItemsSelected", "{0} items selected"), FText::AsNumber(SelectedItems.Num()));
        }
        else
        {
            // No items selected
            ItemDetailsView->SetObject(nullptr);
            StatusMessage = LOCTEXT("NoItemsSelected", "No items selected");
        }
    }
}

void FCEItemDefinitionEditorToolkit::OnColumnSortModeChanged(const EColumnSortPriority::Type SortPriority, const FName& ColumnId, const EColumnSortMode::Type InSortMode)
{
    // If we're clicking the same column that's already sorted and we're in descending mode,
    // clear the sort (third click behavior)
    if (SortColumn == ColumnId && SortMode == EColumnSortMode::Descending && InSortMode == EColumnSortMode::Ascending)
    {
        // Clear sorting
        SortColumn = NAME_None;
        SortMode = EColumnSortMode::None;

        // Refresh the list view with original order
        FilterItems();
    }
    else
    {
        // Store the sort column and mode
        SortColumn = ColumnId;
        SortMode = InSortMode;

        // Sort the items
        SortItems();

        // Refresh the list view
        ItemListView->RequestListRefresh();
    }
}

void FCEItemDefinitionEditorToolkit::SortItems()
{
    if (SortColumn == NAME_None || FilteredItems.Num() == 0)
    {
        return;
    }

    // Update status
    StatusMessage = LOCTEXT("SortingStatus", "Sorting items...");

    // Sort the items based on the selected column
    FilteredItems.Sort([this](const UCEType& A, const UCEType& B) {
        bool bResult = false;

        if (SortColumn == "Name")
        {
            bResult = A.GetName() < B.GetName();
        }
        else if (SortColumn == "Category")
        {
            FString CategoryA = "None";
            FString CategoryB = "None";

            if (!A.Category.IsNull())
            {
                // Get the category name from the path
                FString CategoryPathA = A.Category.ToString();
                CategoryA = FPaths::GetBaseFilename(CategoryPathA);

                // Try to load the category asset if possible
                UCECategory* CategoryAssetA = A.Category.LoadSynchronous();
                if (CategoryAssetA)
                {
                    CategoryA = CategoryAssetA->GetName();
                }
            }

            if (!B.Category.IsNull())
            {
                // Get the category name from the path
                FString CategoryPathB = B.Category.ToString();
                CategoryB = FPaths::GetBaseFilename(CategoryPathB);

                // Try to load the category asset if possible
                UCECategory* CategoryAssetB = B.Category.LoadSynchronous();
                if (CategoryAssetB)
                {
                    CategoryB = CategoryAssetB->GetName();
                }
            }

            bResult = CategoryA < CategoryB;
        }
        else if (SortColumn == "Nominal")
        {
            bResult = A.Nominal < B.Nominal;
        }
        else if (SortColumn == "Min")
        {
            bResult = A.Min < B.Min;
        }
        else if (SortColumn == "Lifetime")
        {
            bResult = A.Lifetime < B.Lifetime;
        }
        else if (SortColumn == "Restock")
        {
            bResult = A.Restock < B.Restock;
        }
        else if (SortColumn == "Cost")
        {
            bResult = A.Cost < B.Cost;
        }
        else if (SortColumn == "Usage")
        {
            // Sort by the first usage name
            FString UsageA = "None";
            FString UsageB = "None";

            if (A.Usage.Num() > 0 && !A.Usage[0].IsNull())
            {
                FString UsagePathA = A.Usage[0].ToString();
                UsageA = FPaths::GetBaseFilename(UsagePathA);
            }

            if (B.Usage.Num() > 0 && !B.Usage[0].IsNull())
            {
                FString UsagePathB = B.Usage[0].ToString();
                UsageB = FPaths::GetBaseFilename(UsagePathB);
            }

            bResult = UsageA < UsageB;
        }
        else if (SortColumn == "Value")
        {
            // Sort by the first value name
            FString ValueA = "None";
            FString ValueB = "None";

            if (A.Value.Num() > 0 && !A.Value[0].IsNull())
            {
                FString ValuePathA = A.Value[0].ToString();
                ValueA = FPaths::GetBaseFilename(ValuePathA);
            }

            if (B.Value.Num() > 0 && !B.Value[0].IsNull())
            {
                FString ValuePathB = B.Value[0].ToString();
                ValueB = FPaths::GetBaseFilename(ValuePathB);
            }

            bResult = ValueA < ValueB;
        }

        // Invert the result if sorting in descending order
        return SortMode == EColumnSortMode::Descending ? !bResult : bResult;
    });

    // Update status
    StatusMessage = LOCTEXT("SortedStatus", "Items sorted");
}

void FCEItemDefinitionEditorToolkit::ExportTypesToXML()
{
    // Update status
    StatusMessage = LOCTEXT("ExportingStatus", "Exporting to XML...");

    // Get the mission path from settings
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    if (!Settings)
    {
        StatusMessage = LOCTEXT("ExportErrorStatus", "Error: Could not access DayZ Tools settings");
        FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("SettingsError", "Could not access DayZ Tools settings"));
        return;
    }

    FString MissionPath = Settings->MissionPath;
    if (MissionPath.IsEmpty())
    {
        StatusMessage = LOCTEXT("ExportErrorStatus", "Error: Mission Path is not set in DayZ Tools settings");
        FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("MissionPathError", "Mission Path is not set in DayZ Tools settings. Please set it in Project Settings > Plugins > DayZ Tools."));
        return;
    }

    // Create the db directory if it doesn't exist
    FString DbPath = FPaths::Combine(MissionPath, TEXT("db"));
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*DbPath))
    {
        if (!PlatformFile.CreateDirectory(*DbPath))
        {
            StatusMessage = LOCTEXT("ExportErrorStatus", "Error: Could not create db directory");
            FMessageDialog::Open(EAppMsgType::Ok, FText::Format(LOCTEXT("CreateDirError", "Could not create directory: {0}"), FText::FromString(DbPath)));
            return;
        }
    }

    // Set the full path for types.xml
    FString TypesXmlPath = FPaths::Combine(DbPath, TEXT("types.xml"));

    // Load all CE Type assets
    TArray<UCEType*> AllTypes;
    if (EditorAsset)
    {
        AllTypes = EditorAsset->LoadAllCETypeAssets();
    }
    else
    {
        StatusMessage = LOCTEXT("ExportErrorStatus", "Error: Editor asset is invalid");
        FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("EditorAssetError", "Editor asset is invalid"));
        return;
    }

    if (AllTypes.Num() == 0)
    {
        StatusMessage = LOCTEXT("ExportErrorStatus", "Error: No CE Type assets found");
        FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("NoTypesError", "No CE Type assets found to export"));
        return;
    }

    // Build the XML content
    FString XmlContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<types>\n");

    for (UCEType* Type : AllTypes)
    {
        if (!Type)
        {
            continue;
        }

        // Use the existing ExportToXML function
        FString TypeXml = Type->ExportToXML();

        // Add indentation and newline
        TypeXml = TEXT("    ") + TypeXml.Replace(TEXT("\n"), TEXT("\n    "));

        // Add to the XML content
        XmlContent += TypeXml + TEXT("\n");
    }

    // Close the types element
    XmlContent += TEXT("</types>\n");

    // Write the XML file
    if (FFileHelper::SaveStringToFile(XmlContent, *TypesXmlPath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
    {
        StatusMessage = FText::Format(LOCTEXT("ExportSuccessStatus", "Successfully exported {0} types to {1}"),
            FText::AsNumber(AllTypes.Num()),
            FText::FromString(TypesXmlPath));

        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            LOCTEXT("ExportSuccess", "Successfully exported {0} types to:\n{1}"),
            FText::AsNumber(AllTypes.Num()),
            FText::FromString(TypesXmlPath)));
    }
    else
    {
        StatusMessage = LOCTEXT("ExportErrorStatus", "Error: Failed to write XML file");
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            LOCTEXT("WriteFileError", "Failed to write XML file to:\n{0}"),
            FText::FromString(TypesXmlPath)));
    }
}

TSharedRef<IDetailsView> FCEItemDefinitionEditorToolkit::CreateDetailsView(UObject* Object, bool bAllowSearch)
{
    FPropertyEditorModule& PropertyEditorModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");

    FDetailsViewArgs DetailsViewArgs;
    DetailsViewArgs.bUpdatesFromSelection = false;
    DetailsViewArgs.bLockable = false;
    DetailsViewArgs.bAllowSearch = bAllowSearch;
    DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
    DetailsViewArgs.bHideSelectionTip = true;

    TSharedRef<IDetailsView> DetailsView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);

    // Set up the details view to hide filter settings properties
    DetailsView->SetIsPropertyVisibleDelegate(FIsPropertyVisible::CreateLambda([](const FPropertyAndParent& PropertyAndParent) {
        // Get the category name
        FString CategoryName = PropertyAndParent.Property.GetMetaData("Category");

        // Hide properties in the "Filter Settings" category since we now use the new content browser-style filtering
        if (CategoryName.Equals("Filter Settings"))
        {
            return false;
        }
        
        return true;
    }));

    if (Object)
    {
        DetailsView->SetObject(Object);
    }

    return DetailsView;
}

void FCEItemDefinitionEditorToolkit::OnSearchTextChanged(const FText& InSearchText)
{
    // Update the search text
    SearchText = InSearchText.ToString();

    // Update the editor asset's search text property
    if (EditorAsset)
    {
        EditorAsset->SearchText = SearchText;
    }

    // Apply the filter immediately
    FilterItems();
}

void FCEItemDefinitionEditorToolkit::AddCategoryFilter(TSoftObjectPtr<UCECategory> Category)
{
    if (Category.IsNull())
    {
        return;
    }

    // Try to load the category to get its name
    UCECategory* CategoryObj = Category.LoadSynchronous();
    if (!CategoryObj)
    {
        return;
    }

    // Check if this filter already exists
    FName CategoryName = *CategoryObj->GetName();
    FActiveFilter NewFilter(FActiveFilter::EFilterType::Category, CategoryName, Category);

    int32 ExistingIndex = ActiveFilters.IndexOfByPredicate([&NewFilter](const FActiveFilter& Filter) {
        return Filter == NewFilter;
    });

    if (ExistingIndex != INDEX_NONE)
    {
        // Filter already exists, toggle it
        ActiveFilters[ExistingIndex].bEnabled = !ActiveFilters[ExistingIndex].bEnabled;
    }
    else
    {
        // Add the new filter
        ActiveFilters.Add(NewFilter);
    }

    // Apply filters and update UI
    ApplyActiveFiltersToSettings();
    RebuildFiltersPanel();
    FilterItems();
}

void FCEItemDefinitionEditorToolkit::AddUsageFilter(TSoftObjectPtr<UCEUsage> Usage)
{
    if (Usage.IsNull())
    {
        return;
    }

    // Try to load the usage to get its name
    UCEUsage* UsageObj = Usage.LoadSynchronous();
    if (!UsageObj)
    {
        return;
    }

    // Check if this filter already exists
    FName UsageName = *UsageObj->GetName();
    FActiveFilter NewFilter(FActiveFilter::EFilterType::Usage, UsageName, Usage);

    int32 ExistingIndex = ActiveFilters.IndexOfByPredicate([&NewFilter](const FActiveFilter& Filter) {
        return Filter == NewFilter;
    });

    if (ExistingIndex != INDEX_NONE)
    {
        // Filter already exists, toggle it
        ActiveFilters[ExistingIndex].bEnabled = !ActiveFilters[ExistingIndex].bEnabled;
    }
    else
    {
        // Add the new filter
        ActiveFilters.Add(NewFilter);
    }

    // Apply filters and update UI
    ApplyActiveFiltersToSettings();
    RebuildFiltersPanel();
    FilterItems();
}

void FCEItemDefinitionEditorToolkit::AddValueFilter(TSoftObjectPtr<UCEValue> Value)
{
    if (Value.IsNull())
    {
        return;
    }

    // Try to load the value to get its name
    UCEValue* ValueObj = Value.LoadSynchronous();
    if (!ValueObj)
    {
        return;
    }

    // Check if this filter already exists
    FName ValueName = *ValueObj->GetName();
    FActiveFilter NewFilter(FActiveFilter::EFilterType::Value, ValueName, Value);

    int32 ExistingIndex = ActiveFilters.IndexOfByPredicate([&NewFilter](const FActiveFilter& Filter) {
        return Filter == NewFilter;
    });

    if (ExistingIndex != INDEX_NONE)
    {
        // Filter already exists, toggle it
        ActiveFilters[ExistingIndex].bEnabled = !ActiveFilters[ExistingIndex].bEnabled;
    }
    else
    {
        // Add the new filter
        ActiveFilters.Add(NewFilter);
    }

    // Apply filters and update UI
    ApplyActiveFiltersToSettings();
    RebuildFiltersPanel();
    FilterItems();
}

void FCEItemDefinitionEditorToolkit::ToggleFilterState(int32 FilterIndex)
{
    if (ActiveFilters.IsValidIndex(FilterIndex))
    {
        // Toggle the enabled state
        ActiveFilters[FilterIndex].bEnabled = !ActiveFilters[FilterIndex].bEnabled;
        
        // Apply filters and update UI
        ApplyActiveFiltersToSettings();
        RebuildFiltersPanel();
        FilterItems();
    }
}

void FCEItemDefinitionEditorToolkit::RemoveFilter(int32 FilterIndex)
{
    if (ActiveFilters.IsValidIndex(FilterIndex))
    {
        // Remove the filter
        ActiveFilters.RemoveAt(FilterIndex);
        
        // Apply filters and update UI
        ApplyActiveFiltersToSettings();
        RebuildFiltersPanel();
        FilterItems();
    }
}

void FCEItemDefinitionEditorToolkit::ClearAllFilters()
{
    // Clear all filters
    ActiveFilters.Empty();
    
    // Apply the change to filter settings
    if (EditorAsset)
    {
        EditorAsset->ActiveFilterCategory = nullptr;
        EditorAsset->ActiveFilterUsage = nullptr;
        EditorAsset->ActiveFilterValue = nullptr;
    }
    
    // Update UI
    RebuildFiltersPanel();
    FilterItems();
}

void FCEItemDefinitionEditorToolkit::ApplyActiveFiltersToSettings()
{
    if (!EditorAsset)
    {
        return;
    }

    // Reset all filter settings
    EditorAsset->ActiveFilterCategory = nullptr;
    EditorAsset->ActiveFilterUsage = nullptr;
    EditorAsset->ActiveFilterValue = nullptr;

    // Apply enabled filters
    for (const FActiveFilter& Filter : ActiveFilters)
    {
        if (!Filter.bEnabled)
        {
            continue;
        }

        switch (Filter.Type)
        {
        case FActiveFilter::EFilterType::Category:
            EditorAsset->ActiveFilterCategory = Cast<UCECategory>(Filter.Asset.LoadSynchronous());
            break;
        case FActiveFilter::EFilterType::Usage:
            EditorAsset->ActiveFilterUsage = Cast<UCEUsage>(Filter.Asset.LoadSynchronous());
            break;
        case FActiveFilter::EFilterType::Value:
            EditorAsset->ActiveFilterValue = Cast<UCEValue>(Filter.Asset.LoadSynchronous());
            break;
        }
    }
}

void FCEItemDefinitionEditorToolkit::RebuildFiltersPanel()
{
    if (!FiltersPanelContainer.IsValid())
    {
        return;
    }

    TSharedRef<SVerticalBox> NewFiltersPanel = SNew(SVerticalBox);

    // Add header if we have filters
    if (ActiveFilters.Num() > 0)
    {
        NewFiltersPanel->AddSlot()
            .AutoHeight()
            .Padding(5, 5, 5, 5)
            [
                SNew(STextBlock)
                .Text(LOCTEXT("ActiveFiltersHeader", "Active Filters"))
                .Font(FCoreStyle::GetDefaultFontStyle("Bold", 11))
            ];

        // Add filter buttons
        for (int32 i = 0; i < ActiveFilters.Num(); i++)
        {
            const FActiveFilter& Filter = ActiveFilters[i];
            
            FString TypeName;
            switch (Filter.Type)
            {
            case FActiveFilter::EFilterType::Category:
                TypeName = "Category";
                break;
            case FActiveFilter::EFilterType::Usage:
                TypeName = "Usage";
                break;
            case FActiveFilter::EFilterType::Value:
                TypeName = "Value";
                break;
            }

            NewFiltersPanel->AddSlot()
                .AutoHeight()
                .Padding(5, 2, 5, 2)
                [
                    SNew(SHorizontalBox)
                    // Toggle button (checkbox style)
                    + SHorizontalBox::Slot()
                    .AutoWidth()
                    .Padding(0, 0, 5, 0)
                    .VAlign(VAlign_Center)
                    [
                        SNew(SCheckBox)
                        .IsChecked(Filter.bEnabled ? ECheckBoxState::Checked : ECheckBoxState::Unchecked)
                        .OnCheckStateChanged_Lambda([this, i](ECheckBoxState NewState) {
                            ToggleFilterState(i);
                        })
                    ]
                    // Filter text
                    + SHorizontalBox::Slot()
                    .FillWidth(1.0f)
                    .VAlign(VAlign_Center)
                    [
                        SNew(STextBlock)
                        .Text(FText::Format(LOCTEXT("FilterButtonText", "{0}: {1}"), 
                            FText::FromString(TypeName), 
                            FText::FromName(Filter.Name)))
                        .ColorAndOpacity(Filter.bEnabled ? FLinearColor::White : FLinearColor(0.5f, 0.5f, 0.5f, 1.0f))
                    ]
                    // Remove button
                    + SHorizontalBox::Slot()
                    .AutoWidth()
                    .Padding(5, 0, 0, 0)
                    .VAlign(VAlign_Center)
                    [
                        SNew(SButton)
                        .ButtonStyle(FAppStyle::Get(), "SimpleButton")
                        .ContentPadding(FMargin(1, 0))
                        .OnClicked_Lambda([this, i]() {
                            RemoveFilter(i);
                            return FReply::Handled();
                        })
                        .ToolTipText(LOCTEXT("RemoveFilterTooltip", "Remove this filter"))
                        [
                            SNew(STextBlock)
                            .Text(FText::FromString(TEXT("\xD7"))) // Unicode 'X'
                            .Font(FCoreStyle::GetDefaultFontStyle("Bold", 12))
                        ]
                    ]
                ];
        }
    }

    // Update the filters panel
    FiltersPanel = NewFiltersPanel;
    
    // Update the container visibility
    FiltersPanelContainer->SetVisibility(ActiveFilters.Num() > 0 ? EVisibility::Visible : EVisibility::Collapsed);
    
    // Use SetContent to update the container with the new panel
    // This is the correct way to update content in a container widget
    if (FiltersPanelContainer->GetContent() != FiltersPanel.ToSharedRef())
    {
        SBorder* Border = static_cast<SBorder*>(FiltersPanelContainer.Get());
        if (Border)
        {
            Border->SetContent(FiltersPanel.ToSharedRef());
        }
    }
}

TSharedRef<SWidget> FCEItemDefinitionEditorToolkit::MakeFilterMenu()
{
    FMenuBuilder MenuBuilder(true, nullptr);

    // Add Category filter submenu
    MenuBuilder.AddSubMenu(
        LOCTEXT("CategoryFilter", "Category"),
        LOCTEXT("CategoryFilterTooltip", "Filter items by Category"),
        FNewMenuDelegate::CreateLambda([this](FMenuBuilder& SubMenuBuilder) {
            MakeCategoryFilterSubmenu(SubMenuBuilder);
        })
    );

    // Add Usage filter submenu
    MenuBuilder.AddSubMenu(
        LOCTEXT("UsageFilter", "Usage"),
        LOCTEXT("UsageFilterTooltip", "Filter items by Usage"),
        FNewMenuDelegate::CreateLambda([this](FMenuBuilder& SubMenuBuilder) {
            MakeUsageFilterSubmenu(SubMenuBuilder);
        })
    );

    // Add Value filter submenu
    MenuBuilder.AddSubMenu(
        LOCTEXT("ValueFilter", "Value"),
        LOCTEXT("ValueFilterTooltip", "Filter items by Value"),
        FNewMenuDelegate::CreateLambda([this](FMenuBuilder& SubMenuBuilder) {
            MakeValueFilterSubmenu(SubMenuBuilder);
        })
    );

    // Add separator
    MenuBuilder.AddMenuSeparator();

    // Add "Clear All Filters" option
    MenuBuilder.AddMenuEntry(
        LOCTEXT("ClearAllFilters", "Clear All Filters"),
        LOCTEXT("ClearAllFiltersTooltip", "Remove all active filters"),
        FSlateIcon(),
        FUIAction(FExecuteAction::CreateSP(this, &FCEItemDefinitionEditorToolkit::ClearAllFilters)),
        NAME_None,
        EUserInterfaceActionType::Button
    );

    return MenuBuilder.MakeWidget();
}

void FCEItemDefinitionEditorToolkit::MakeCategoryFilterSubmenu(FMenuBuilder& SubMenuBuilder)
{
    // Get all Category assets
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    TArray<FAssetData> AssetData;
    FARFilter Filter;
    
    // Use ClassPaths instead of ClassNames for UE5.4+
    Filter.ClassPaths.Add(UCECategory::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    // Sort the assets by name
    AssetData.Sort([](const FAssetData& A, const FAssetData& B) {
        return A.AssetName.ToString() < B.AssetName.ToString();
    });

    for (const FAssetData& Asset : AssetData)
    {
        FString AssetName = Asset.AssetName.ToString();
        SubMenuBuilder.AddMenuEntry(
            FText::FromString(AssetName),
            FText::Format(LOCTEXT("FilterByCategoryTooltip", "Filter items by the '{0}' category"), FText::FromString(AssetName)),
            FSlateIcon(),
            FUIAction(FExecuteAction::CreateSP(this, &FCEItemDefinitionEditorToolkit::AddCategoryFilter, 
                TSoftObjectPtr<UCECategory>(Asset.ToSoftObjectPath()))),
            NAME_None,
            EUserInterfaceActionType::Button
        );
    }
}

void FCEItemDefinitionEditorToolkit::MakeUsageFilterSubmenu(FMenuBuilder& SubMenuBuilder)
{
    // Get all Usage assets
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    TArray<FAssetData> AssetData;
    FARFilter Filter;
    
    // Use ClassPaths instead of ClassNames for UE5.4+
    Filter.ClassPaths.Add(UCEUsage::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    // Sort the assets by name
    AssetData.Sort([](const FAssetData& A, const FAssetData& B) {
        return A.AssetName.ToString() < B.AssetName.ToString();
    });

    for (const FAssetData& Asset : AssetData)
    {
        FString AssetName = Asset.AssetName.ToString();
        SubMenuBuilder.AddMenuEntry(
            FText::FromString(AssetName),
            FText::Format(LOCTEXT("FilterByUsageTooltip", "Filter items by the '{0}' usage"), FText::FromString(AssetName)),
            FSlateIcon(),
            FUIAction(FExecuteAction::CreateSP(this, &FCEItemDefinitionEditorToolkit::AddUsageFilter, 
                TSoftObjectPtr<UCEUsage>(Asset.ToSoftObjectPath()))),
            NAME_None,
            EUserInterfaceActionType::Button
        );
    }
}

void FCEItemDefinitionEditorToolkit::MakeValueFilterSubmenu(FMenuBuilder& SubMenuBuilder)
{
    // Get all Value assets
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    TArray<FAssetData> AssetData;
    FARFilter Filter;
    
    // Use ClassPaths instead of ClassNames for UE5.4+
    Filter.ClassPaths.Add(UCEValue::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    // Sort the assets by name
    AssetData.Sort([](const FAssetData& A, const FAssetData& B) {
        return A.AssetName.ToString() < B.AssetName.ToString();
    });

    for (const FAssetData& Asset : AssetData)
    {
        FString AssetName = Asset.AssetName.ToString();
        SubMenuBuilder.AddMenuEntry(
            FText::FromString(AssetName),
            FText::Format(LOCTEXT("FilterByValueTooltip", "Filter items by the '{0}' value"), FText::FromString(AssetName)),
            FSlateIcon(),
            FUIAction(FExecuteAction::CreateSP(this, &FCEItemDefinitionEditorToolkit::AddValueFilter, 
                TSoftObjectPtr<UCEValue>(Asset.ToSoftObjectPath()))),
            NAME_None,
            EUserInterfaceActionType::Button
        );
    }
}

#undef LOCTEXT_NAMESPACE
