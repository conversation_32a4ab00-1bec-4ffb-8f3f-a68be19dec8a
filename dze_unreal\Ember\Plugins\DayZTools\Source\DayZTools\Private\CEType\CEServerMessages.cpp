#include "CEType/CEServerMessages.h"

UCEServerMessages::UCEServerMessages(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Initialize with a default message
    FServerMessage DefaultMessage;
    DefaultMessage.Text = "Welcome to our DayZ server!";
    DefaultMessage.bOnConnect = true;
    DefaultMessage.Delay = 0;
    Messages.Add(DefaultMessage);
}

FString UCEServerMessages::ExportToXML() const
{
    FString XMLOutput;

    // Start messages tag
    XMLOutput += TEXT("<messages>\n");

    // Add message entries
    for (const FServerMessage& Message : Messages)
    {
        XMLOutput += TEXT("\t<message>\n");
        
        // Add parameters only if they are non-zero
        if (Message.Delay != 0)
        {
            XMLOutput += FString::Printf(TEXT("\t\t<delay>%d</delay>\n"), Message.Delay);
        }
        
        // For repeat, only include if it's enabled and has a non-zero value
        if (Message.bRepeat && Message.RepeatTime != 0)
        {
            XMLOutput += FString::Printf(TEXT("\t\t<repeat>%d</repeat>\n"), Message.RepeatTime);
        }
        else if (!Message.bRepeat)
        {
            // Include with value 0 to explicitly disable repeat (per DayZ documentation)
            XMLOutput += TEXT("\t\t<repeat>0</repeat>\n");
        }
        
        // For deadline, only include if countdown is enabled and has a non-zero value
        if (Message.bCountdown && Message.Deadline != 0)
        {
            XMLOutput += FString::Printf(TEXT("\t\t<deadline>%d</deadline>\n"), Message.Deadline);
        }
        
        // For onConnect flag, only include if enabled (value = 1)
        if (Message.bOnConnect)
        {
            XMLOutput += TEXT("\t\t<onConnect>1</onConnect>\n");
        }
        
        // For shutdown flag, only include if countdown and shutdown are enabled
        if (Message.bCountdown && Message.bShutdown)
        {
            XMLOutput += TEXT("\t\t<shutdown>1</shutdown>\n");
        }
        
        // Add message text, properly escaped if needed
        FString EscapedText = Message.Text;
        EscapedText.ReplaceInline(TEXT("&"), TEXT("&amp;"), ESearchCase::CaseSensitive);
        EscapedText.ReplaceInline(TEXT("<"), TEXT("&lt;"), ESearchCase::CaseSensitive);
        EscapedText.ReplaceInline(TEXT(">"), TEXT("&gt;"), ESearchCase::CaseSensitive);
        EscapedText.ReplaceInline(TEXT("\""), TEXT("&quot;"), ESearchCase::CaseSensitive);
        EscapedText.ReplaceInline(TEXT("'"), TEXT("&apos;"), ESearchCase::CaseSensitive);
        
        XMLOutput += FString::Printf(TEXT("\t\t<text>%s</text>\n"), *EscapedText);
        
        XMLOutput += TEXT("\t</message>\n");
    }

    // Close messages tag
    XMLOutput += TEXT("</messages>");

    return XMLOutput;
}