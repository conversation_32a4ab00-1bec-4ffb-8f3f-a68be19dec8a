#include "CEType/CEServerMessagesActions.h"
#include "CEType/CEServerMessages.h"
#include "CEType/CEServerMessagesEditorToolkit.h"
#include "DayZTools.h"
#include "Toolkits/SimpleAssetEditor.h"

FText FCEServerMessagesActions::GetName() const
{
    return FText::FromString("Server Messages");
}

FColor FCEServerMessagesActions::GetTypeColor() const
{
    return FColor(128, 0, 200); // Purple color
}

UClass* FCEServerMessagesActions::GetSupportedClass() const
{
    return UCEServerMessages::StaticClass();
}

uint32 FCEServerMessagesActions::GetCategories()
{
    return EAssetTypeCategories::Misc;
}

void FCEServerMessagesActions::OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor)
{
    EToolkitMode::Type Mode = EditWithinLevelEditor.IsValid() ? EToolkitMode::WorldCentric : EToolkitMode::Standalone;

    for (auto ObjIt = InObjects.CreateConstIterator(); ObjIt; ++ObjIt)
    {
        auto ServerMessages = Cast<UCEServerMessages>(*ObjIt);
        if (ServerMessages != nullptr)
        {
            // Create and initialize a new editor using our custom toolkit
            TSharedRef<FCEServerMessagesEditorToolkit> NewEditor = MakeShareable(new FCEServerMessagesEditorToolkit());
            NewEditor->Initialize(Mode, EditWithinLevelEditor, ServerMessages);
        }
    }
}