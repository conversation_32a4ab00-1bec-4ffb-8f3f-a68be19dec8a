#include "CEType/CEServerMessagesCustomization.h"
#include "CEType/CEServerMessages.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Text/STextBlock.h"
#include "DetailWidgetRow.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"
#include "Widgets/Layout/SBox.h"
#include "EditorStyleSet.h"
#include "Misc/MessageDialog.h"
#include "PropertyCustomizationHelpers.h"

TSharedRef<IDetailCustomization> FCEServerMessagesCustomization::MakeInstance()
{
    return MakeShareable(new FCEServerMessagesCustomization);
}

void FCEServerMessagesCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // Get the currently selected objects
    TArray<TWeakObjectPtr<UObject>> SelectedObjects;
    DetailBuilder.GetObjectsBeingCustomized(SelectedObjects);

    if (SelectedObjects.Num() != 1)
    {
        return;
    }

    ServerMessagesAsset = Cast<UCEServerMessages>(SelectedObjects[0].Get());
    if (!ServerMessagesAsset.IsValid())
    {
        return;
    }

    // Add a custom category for XML export
    IDetailCategoryBuilder& ExportCategory = DetailBuilder.EditCategory("XML Export");

    // Add a button to export the server messages to XML
    ExportCategory.AddCustomRow(FText::FromString("Export to XML"))
        .ValueContent()
        [
            SNew(SButton)
            .Text(FText::FromString("Show XML"))
            .ToolTipText(FText::FromString("Exports the server messages configuration as XML and displays it"))
            .OnClicked(this, &FCEServerMessagesCustomization::OnExportToXMLClicked)
        ];
}

FReply FCEServerMessagesCustomization::OnExportToXMLClicked()
{
    if (ServerMessagesAsset.IsValid())
    {
        FString XMLContent = ServerMessagesAsset->ExportToXML();

        // Show the XML in a dialog
        FText DialogTitle = FText::FromString("XML Export");
        FText DialogContent = FText::FromString(XMLContent);
        FMessageDialog::Open(EAppMsgType::Ok, DialogContent, DialogTitle);
    }

    return FReply::Handled();
}

FReply FCEServerMessagesCustomization::OnAddMessageClicked()
{
    if (ServerMessagesAsset.IsValid())
    {
        FServerMessage NewMessage;
        NewMessage.Text = "New server message";
        NewMessage.bOnConnect = true;
        NewMessage.Delay = 0;
        
        ServerMessagesAsset->Messages.Add(NewMessage);
        ServerMessagesAsset->Modify();
    }
    
    return FReply::Handled();
}

FReply FCEServerMessagesCustomization::OnRemoveMessageClicked(int32 MessageIndex)
{
    if (ServerMessagesAsset.IsValid() && 
        ServerMessagesAsset->Messages.IsValidIndex(MessageIndex))
    {
        ServerMessagesAsset->Messages.RemoveAt(MessageIndex);
        ServerMessagesAsset->Modify();
    }
    
    return FReply::Handled();
}