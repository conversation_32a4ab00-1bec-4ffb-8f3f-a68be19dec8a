#include "CEType/CEServerMessagesEditorToolkit.h"
#include "CEType/CEServerMessages.h"
#include "DayZToolsStyle.h"
#include "Framework/Docking/TabManager.h"
#include "EditorStyleSet.h"
#include "Widgets/Docking/SDockTab.h"
#include "PropertyEditorModule.h"
#include "IDetailsView.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SSplitter.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Framework/Notifications/NotificationManager.h"
#include "DayZToolsSettings.h"
#include "HAL/PlatformFileManager.h"
#include "Misc/FileHelper.h"
#include "Misc/MessageDialog.h"

#define LOCTEXT_NAMESPACE "CEServerMessagesEditor"

const FName FCEServerMessagesEditorToolkit::PropertiesTabId(TEXT("CEServerMessagesEditor_Properties"));
const FName FCEServerMessagesEditorToolkit::XMLPreviewTabId(TEXT("CEServerMessagesEditor_XMLPreview"));

FCEServerMessagesEditorToolkit::FCEServerMessagesEditorToolkit()
    : ServerMessagesAsset(nullptr)
{
    // Create notification list for this toolkit
    NotificationList = SNew(SNotificationList);
}

FCEServerMessagesEditorToolkit::~FCEServerMessagesEditorToolkit()
{
}

void FCEServerMessagesEditorToolkit::RegisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    WorkspaceMenuCategory = InTabManager->AddLocalWorkspaceMenuCategory(LOCTEXT("ServerMessagesEditorMenuCategory", "Server Messages Editor"));
    auto WorkspaceMenuCategoryRef = WorkspaceMenuCategory.ToSharedRef();

    FAssetEditorToolkit::RegisterTabSpawners(InTabManager);

    // Properties tab
    InTabManager->RegisterTabSpawner(PropertiesTabId, FOnSpawnTab::CreateLambda([this](const FSpawnTabArgs& Args) -> TSharedRef<SDockTab>
    {
        // Load the PropertyEditorModule correctly
        FPropertyEditorModule& PropertyEditorModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");
        
        FDetailsViewArgs DetailsViewArgs;
        DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
        DetailsViewArgs.bHideSelectionTip = true;
        
        TSharedRef<IDetailsView> DetailsView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);
        DetailsView->SetObject(ServerMessagesAsset);
        
        return SNew(SDockTab)
            .Label(LOCTEXT("PropertiesTabLabel", "Details"))
            .TabColorScale(GetTabColorScale())
            [
                DetailsView
            ];
    }))
    .SetDisplayName(LOCTEXT("PropertiesTab", "Details"))
    .SetGroup(WorkspaceMenuCategoryRef)
    .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Details"));

    // XML Preview tab
    InTabManager->RegisterTabSpawner(XMLPreviewTabId, FOnSpawnTab::CreateSP(this, &FCEServerMessagesEditorToolkit::SpawnTab_XMLPreview))
        .SetDisplayName(LOCTEXT("XMLPreviewTab", "XML Preview"))
        .SetGroup(WorkspaceMenuCategoryRef)
        .SetIcon(FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Tabs.Viewports"));
}

void FCEServerMessagesEditorToolkit::UnregisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::UnregisterTabSpawners(InTabManager);

    InTabManager->UnregisterTabSpawner(PropertiesTabId);
    InTabManager->UnregisterTabSpawner(XMLPreviewTabId);
}

FName FCEServerMessagesEditorToolkit::GetToolkitFName() const
{
    return FName("CEServerMessagesEditor");
}

FText FCEServerMessagesEditorToolkit::GetBaseToolkitName() const
{
    return LOCTEXT("AppLabel", "Server Messages Editor");
}

FText FCEServerMessagesEditorToolkit::GetToolkitName() const
{
    if (ServerMessagesAsset)
    {
        const bool bDirtyState = ServerMessagesAsset->GetOutermost()->IsDirty();
        
        FFormatNamedArguments Args;
        Args.Add(TEXT("AssetName"), FText::FromString(ServerMessagesAsset->GetName()));
        Args.Add(TEXT("DirtyState"), bDirtyState ? FText::FromString(TEXT("*")) : FText::GetEmpty());
        return FText::Format(LOCTEXT("ServerMessagesEditorAppLabel", "{AssetName}{DirtyState}"), Args);
    }
    return GetBaseToolkitName();
}

FText FCEServerMessagesEditorToolkit::GetToolkitToolTipText() const
{
    if (ServerMessagesAsset)
    {
        return FAssetEditorToolkit::GetToolTipTextForObject(ServerMessagesAsset);
    }
    return FText();
}

FString FCEServerMessagesEditorToolkit::GetWorldCentricTabPrefix() const
{
    return TEXT("ServerMessagesEditor");
}

FLinearColor FCEServerMessagesEditorToolkit::GetWorldCentricTabColorScale() const
{
    return FLinearColor(0.7f, 0.0f, 0.7f, 0.5f);  // Purple
}

bool FCEServerMessagesEditorToolkit::OnRequestClose()
{
    return FAssetEditorToolkit::OnRequestClose();
}

void FCEServerMessagesEditorToolkit::Initialize(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UCEServerMessages* InServerMessages)
{
    ServerMessagesAsset = InServerMessages;

    const TSharedRef<FTabManager::FLayout> StandaloneDefaultLayout = FTabManager::NewLayout("Standalone_CEServerMessagesEditor_Layout_v1")
        ->AddArea
        (
            FTabManager::NewPrimaryArea()->SetOrientation(Orient_Vertical)
            ->Split
            (
                FTabManager::NewSplitter()->SetOrientation(Orient_Horizontal)
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.4f)
                    ->AddTab(PropertiesTabId, ETabState::OpenedTab)
                )
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.6f)
                    ->AddTab(XMLPreviewTabId, ETabState::OpenedTab)
                )
            )
        );

    // Initialize asset editor
    InitAssetEditor(Mode, InitToolkitHost, FName("CEServerMessagesEditorApp"), StandaloneDefaultLayout, true, true, InServerMessages);

    // Extend the toolbar
    TSharedPtr<FExtender> ToolbarExtender = MakeShareable(new FExtender);
    
    ToolbarExtender->AddToolBarExtension(
        "Asset",
        EExtensionHook::After,
        GetToolkitCommands(),
        FToolBarExtensionDelegate::CreateLambda([this](FToolBarBuilder& ToolbarBuilder)
        {
            ToolbarBuilder.BeginSection("CEServerMessages");
            
            ToolbarBuilder.AddToolBarButton(
                FUIAction(FExecuteAction::CreateSP(this, &FCEServerMessagesEditorToolkit::ShowXMLExport)),
                NAME_None,
                LOCTEXT("ShowXML", "Show XML"),
                LOCTEXT("ShowXMLTooltip", "Display the XML representation of the server messages"),
                FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Help")
            );
            
            ToolbarBuilder.AddToolBarButton(
                FUIAction(FExecuteAction::CreateSP(this, &FCEServerMessagesEditorToolkit::ExportServerMessages)),
                NAME_None,
                LOCTEXT("ExportMessages", "Export"),
                LOCTEXT("ExportMessagesTooltip", "Export server messages to XML file"),
                FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Save")
            );
            
            ToolbarBuilder.EndSection();
        })
    );
    
    AddToolbarExtender(ToolbarExtender);
    
    RegenerateMenusAndToolbars();
    
    // Initial update of XML preview
    UpdateXMLPreview();
}

TSharedRef<SDockTab> FCEServerMessagesEditorToolkit::SpawnTab_XMLPreview(const FSpawnTabArgs& Args)
{
    XMLPreviewTextBox = SNew(SMultiLineEditableTextBox)
        .IsReadOnly(true)
        .AllowContextMenu(true)
        .AutoWrapText(false)
        .Text(FText::FromString(ServerMessagesAsset ? ServerMessagesAsset->ExportToXML() : TEXT("")))
        .Font(FCoreStyle::GetDefaultFontStyle("Mono", 10));

    return SNew(SDockTab)
        .Label(LOCTEXT("XMLPreviewTabLabel", "XML Preview"))
        [
            SNew(SScrollBox)
            + SScrollBox::Slot()
            [
                SNew(SBox)
                .Padding(FMargin(5, 5))
                [
                    SNew(SVerticalBox)
                    + SVerticalBox::Slot()
                    .AutoHeight()
                    .Padding(0, 0, 0, 5)
                    [
                        SNew(STextBlock)
                        .Text(LOCTEXT("XMLPreviewHeader", "XML Output Preview:"))
                        .Font(FCoreStyle::GetDefaultFontStyle("Bold", 12))
                    ]
                    + SVerticalBox::Slot()
                    [
                        XMLPreviewTextBox.ToSharedRef()
                    ]
                ]
            ]
        ];
}

void FCEServerMessagesEditorToolkit::UpdateXMLPreview()
{
    if (XMLPreviewTextBox.IsValid() && ServerMessagesAsset)
    {
        FString XMLContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n") + ServerMessagesAsset->ExportToXML();
        XMLPreviewTextBox->SetText(FText::FromString(XMLContent));
    }
}

void FCEServerMessagesEditorToolkit::ExportServerMessages()
{
    if (!ServerMessagesAsset)
    {
        FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("NoAssetError", "No server messages asset loaded."));
        return;
    }

    // Get the mission path from settings
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    if (!Settings)
    {
        FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("SettingsError", "Could not access DayZ Tools settings."));
        return;
    }

    FString MissionPath = Settings->MissionPath;
    if (MissionPath.IsEmpty())
    {
        FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("MissionPathError", "Mission Path is not set in DayZ Tools settings. Please set it in Project Settings > Plugins > DayZ Tools."));
        return;
    }

    // Generate XML content
    FString XmlContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n");
    XmlContent += ServerMessagesAsset->ExportToXML();

    // Set the full path for messages.xml
    FString MessagesXmlPath = FPaths::Combine(MissionPath, TEXT("messages.xml"));
    
    // Write the XML file
    if (FFileHelper::SaveStringToFile(XmlContent, *MessagesXmlPath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
    {
        // Show a notification
        FNotificationInfo Info(FText::Format(
            LOCTEXT("ExportSuccess", "Successfully exported Server Messages to {0}"),
            FText::FromString(MessagesXmlPath)
        ));
        Info.ExpireDuration = 5.0f;
        Info.bUseLargeFont = false;
        FSlateNotificationManager::Get().AddNotification(Info);

        UE_LOG(LogTemp, Log, TEXT("Successfully exported Server Messages to %s"), *MessagesXmlPath);
    }
    else
    {
        // Show error in a dialog
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            LOCTEXT("ExportError", "Failed to write XML file to:\n{0}"),
            FText::FromString(MessagesXmlPath)
        ));
        
        UE_LOG(LogTemp, Error, TEXT("Failed to write Server Messages XML file to %s"), *MessagesXmlPath);
    }
}

void FCEServerMessagesEditorToolkit::ShowXMLExport()
{
    if (!ServerMessagesAsset)
    {
        FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("NoAssetError", "No server messages asset loaded."));
        return;
    }

    // Generate XML content with XML declaration
    FString XMLContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n");
    XMLContent += ServerMessagesAsset->ExportToXML();

    // Show the XML in a dialog
    FText DialogTitle = FText::FromString("XML Export");
    FText DialogContent = FText::FromString(XMLContent);
    FMessageDialog::Open(EAppMsgType::Ok, DialogContent, &DialogTitle);
    
    // Update the preview
    UpdateXMLPreview();
}

#undef LOCTEXT_NAMESPACE