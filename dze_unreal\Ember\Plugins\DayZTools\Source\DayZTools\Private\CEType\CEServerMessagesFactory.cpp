#include "CEType/CEServerMessagesFactory.h"
#include "CEType/CEServerMessages.h"

UCEServerMessagesFactory::UCEServerMessagesFactory()
{
    SupportedClass = UCEServerMessages::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UCEServerMessagesFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new Server Messages asset
    UCEServerMessages* NewAsset = NewObject<UCEServerMessages>(InParent, Class, Name, Flags);

    return NewAsset;
}

bool UCEServerMessagesFactory::ShouldShowInNewMenu() const
{
    return true;
}