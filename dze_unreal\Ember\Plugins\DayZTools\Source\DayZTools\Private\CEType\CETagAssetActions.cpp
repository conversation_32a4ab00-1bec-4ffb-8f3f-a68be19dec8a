// CETagAssetActions.cpp
#include "CEType/CETagAssetActions.h"

FCETagAssetActions::FCETagAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FCETagAssetActions::GetName() const
{
    return FText::FromString(TEXT("CE Tag"));
}

FColor FCETagAssetActions::GetTypeColor() const
{
    // Use a distinctive color for CE Tag assets
    return FColor(150, 50, 200); // Purple
}

UClass* FCETagAssetActions::GetSupportedClass() const
{
    return UCETag::StaticClass();
}

uint32 FCETagAssetActions::GetCategories()
{
    return AssetCategory;
}
