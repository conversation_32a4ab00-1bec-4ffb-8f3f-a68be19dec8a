// CETagFactory.cpp
#include "CEType/CETagFactory.h"
#include "CEType/CETag.h"

UCETagFactory::UCETagFactory()
{
    SupportedClass = UCETag::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UCETagFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new CE Tag asset
    UCETag* NewAsset = NewObject<UCETag>(InParent, Class, Name, Flags);
    return NewAsset;
}

bool UCETagFactory::ShouldShowInNewMenu() const
{
    return true;
}
