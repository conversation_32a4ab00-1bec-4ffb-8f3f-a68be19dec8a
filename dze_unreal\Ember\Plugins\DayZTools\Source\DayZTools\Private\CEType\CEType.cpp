// CEType.cpp
#include "CEType/CEType.h"

UCEType::UCEType(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Type will be null by default
    Type = nullptr;
    
    // Initialize with default values
    Nominal = 0;
    Lifetime = 14400;
    Restock = 0;
    Min = 0;
    QuantMin = -1;
    QuantMax = -1;
    Cost = 100;

    // Initialize flags
    Flags.bCountInCargo = false;
    Flags.bCountInHoarder = false;
    Flags.bCountInMap = true;
    Flags.bCountInPlayer = false;
    Flags.bCrafted = false;
    Flags.bDeloot = false;
}

FString UCEType::ExportToXML() const
{
    FString XMLOutput;

    // Start type tag with name (use asset name)
    XMLOutput += FString::Printf(TEXT("<type name=\"%s\">\n"), *GetName());

    // Add basic properties
    XMLOutput += FString::Printf(TEXT("        <nominal>%d</nominal>\n"), Nominal);
    XMLOutput += FString::Printf(TEXT("        <lifetime>%d</lifetime>\n"), Lifetime);
    XMLOutput += FString::Printf(TEXT("        <restock>%d</restock>\n"), Restock);
    XMLOutput += FString::Printf(TEXT("        <min>%d</min>\n"), Min);
    XMLOutput += FString::Printf(TEXT("        <quantmin>%d</quantmin>\n"), QuantMin);
    XMLOutput += FString::Printf(TEXT("        <quantmax>%d</quantmax>\n"), QuantMax);
    XMLOutput += FString::Printf(TEXT("        <cost>%d</cost>\n"), Cost);

    // Add flags
    XMLOutput += FString::Printf(TEXT("        <flags count_in_cargo=\"%d\" count_in_hoarder=\"%d\" count_in_map=\"%d\" count_in_player=\"%d\" crafted=\"%d\" deloot=\"%d\"/>\n"),
        Flags.bCountInCargo ? 1 : 0,
        Flags.bCountInHoarder ? 1 : 0,
        Flags.bCountInMap ? 1 : 0,
        Flags.bCountInPlayer ? 1 : 0,
        Flags.bCrafted ? 1 : 0,
        Flags.bDeloot ? 1 : 0);

    // Add category if specified
    if (!Category.IsNull())
    {
        // Get the category name
        FString CategoryName = FPaths::GetBaseFilename(Category.ToString());

        // Try to load the category asset if possible
        UCECategory* CategoryAsset = Category.LoadSynchronous();
        if (CategoryAsset)
        {
            CategoryName = CategoryAsset->GetName();
        }

        XMLOutput += FString::Printf(TEXT("        <category name=\"%s\"/>\n"), *CategoryName);
    }

    // Add usage entries if any
    for (const TSoftObjectPtr<UCEUsage>& UsageEntry : Usage)
    {
        if (!UsageEntry.IsNull())
        {
            // Get the usage name
            FString UsageName = FPaths::GetBaseFilename(UsageEntry.ToString());

            // Try to load the usage asset if possible
            UCEUsage* UsageAsset = UsageEntry.LoadSynchronous();
            if (UsageAsset)
            {
                UsageName = UsageAsset->GetName();
            }

            XMLOutput += FString::Printf(TEXT("        <usage name=\"%s\"/>\n"), *UsageName);
        }
    }

    // Add tag entries if any
    for (const TSoftObjectPtr<UCETag>& TagEntry : Tag)
    {
        if (!TagEntry.IsNull())
        {
            // Get the tag name
            FString TagName = FPaths::GetBaseFilename(TagEntry.ToString());

            // Try to load the tag asset if possible
            UCETag* TagAsset = TagEntry.LoadSynchronous();
            if (TagAsset)
            {
                TagName = TagAsset->GetName();
            }

            XMLOutput += FString::Printf(TEXT("        <tag name=\"%s\"/>\n"), *TagName);
        }
    }

    // Add value entries if any
    for (const TSoftObjectPtr<UCEValue>& ValueEntry : Value)
    {
        if (!ValueEntry.IsNull())
        {
            // Get the value name
            FString ValueName = FPaths::GetBaseFilename(ValueEntry.ToString());

            // Try to load the value asset if possible
            UCEValue* ValueAsset = ValueEntry.LoadSynchronous();
            if (ValueAsset)
            {
                ValueName = ValueAsset->GetName();
            }

            XMLOutput += FString::Printf(TEXT("        <value name=\"%s\"/>\n"), *ValueName);
        }
    }

    // Close type tag
    XMLOutput += TEXT("    </type>");

    return XMLOutput;
}
