// CETypeAssetActions.cpp
#include "CEType/CETypeAssetActions.h"

FCETypeAssetActions::FCETypeAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FCETypeAssetActions::GetName() const
{
    return FText::FromString(TEXT("CE Type"));
}

FColor FCETypeAssetActions::GetTypeColor() const
{
    // Use a distinctive color for CE Type assets
    return FColor(0, 140, 255); // Blue
}

UClass* FCETypeAssetActions::GetSupportedClass() const
{
    return UCEType::StaticClass();
}

uint32 FCETypeAssetActions::GetCategories()
{
    return AssetCategory;
}
