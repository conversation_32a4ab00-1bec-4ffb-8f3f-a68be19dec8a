// CETypeCustomization.cpp
#include "CEType/CETypeCustomization.h"
#include "CEType/CEType.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "DetailWidgetRow.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Text/STextBlock.h"
#include "Framework/Notifications/NotificationManager.h"
#include "Widgets/Notifications/SNotificationList.h"
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "DesktopPlatformModule.h"
#include "IDesktopPlatform.h"
#include "Framework/Application/SlateApplication.h"
#include "ConfigClass/ConfigClass.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "UObject/Package.h"
#include "EditorAssetLibrary.h"
#include "ObjectTools.h"
#include "PropertyHandle.h"
#include "Misc/MessageDialog.h"
// Additional required includes
#include "AssetToolsModule.h"
#include "IAssetTools.h"

TSharedRef<IDetailCustomization> FCETypeCustomization::MakeInstance()
{
    return MakeShareable(new FCETypeCustomization);
}

void FCETypeCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // Get the selected objects
    TArray<TWeakObjectPtr<UObject>> SelectedObjects;
    DetailBuilder.GetObjectsBeingCustomized(SelectedObjects);

    if (SelectedObjects.Num() != 1)
    {
        return;
    }

    CETypeAsset = Cast<UCEType>(SelectedObjects[0].Get());
    if (!CETypeAsset.IsValid())
    {
        return;
    }

    // Get the Type property handle
    TSharedPtr<IPropertyHandle> TypePropertyHandle = DetailBuilder.GetProperty("Type");
    if (TypePropertyHandle.IsValid())
    {
        // Register a callback for when the property changes
        FSimpleDelegate PropertyChangedDelegate = FSimpleDelegate::CreateSP(this, &FCETypeCustomization::OnTypePropertyChanged);
        TypePropertyHandle->SetOnPropertyValueChanged(PropertyChangedDelegate);
    }

    // Note: "Export to XML" button has been removed as requested
}

void FCETypeCustomization::OnTypePropertyChanged()
{
    if (CETypeAsset.IsValid())
    {
        AttemptRenameToConfigClass();
    }
}

bool FCETypeCustomization::AttemptRenameToConfigClass()
{
    if (!CETypeAsset.IsValid())
    {
        return false;
    }

    // Get the selected Config Class asset
    UConfigClass* ConfigClass = CETypeAsset->Type.LoadSynchronous();
    
    // If no Config Class selected (or it's null/invalid), don't do anything
    if (!ConfigClass || !IsValid(ConfigClass))
    {
        return false;
    }

    // Get the name of the selected Config Class asset
    FString NewName = ConfigClass->GetName();
    
    // If the asset already has this name, no need to rename
    if (CETypeAsset->GetName() == NewName)
    {
        return true;
    }
    
    // Check if an asset with this name already exists in the same folder
    FString CurrentPath = CETypeAsset->GetPathName();
    FString CurrentPackagePath = FPackageName::GetLongPackagePath(CurrentPath);
    FString NewAssetPath = CurrentPackagePath + TEXT("/") + NewName;
    
    // Check if asset with this path already exists
    if (UEditorAssetLibrary::DoesAssetExist(NewAssetPath))
    {
        // Asset with the same name already exists, show error message and reset the Type property
        FText ErrorMessage = FText::Format(
            FText::FromString(TEXT("{0} type definition already exists")),
            FText::FromString(NewName)
        );
        FMessageDialog::Open(EAppMsgType::Ok, ErrorMessage);
        
        // Reset the Type property to None
        CETypeAsset->Type = nullptr;
        CETypeAsset->Modify();
        
        return false;
    }
    
    // Get the Asset Tools module
    IAssetTools& AssetTools = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools").Get();
    
    // Get the asset's package
    UPackage* Package = CETypeAsset->GetOutermost();
    FString PackagePath = Package->GetName();
    
    // Get just the folder path without the asset name
    FString FolderPath = FPaths::GetPath(PackagePath);
    
    // Prepare the rename data
    TArray<FAssetRenameData> AssetsAndNames;
    FAssetRenameData RenameData(CETypeAsset.Get(), FolderPath, NewName);
    AssetsAndNames.Add(RenameData);
    
    // Perform the rename operation
    bool bSuccess = AssetTools.RenameAssets(AssetsAndNames);
    
    return bSuccess;
}

FReply FCETypeCustomization::OnExportToXMLClicked()
{
    // Keep this method for now, but it's no longer connected to any button
    if (!CETypeAsset.IsValid())
    {
        return FReply::Handled();
    }

    // Generate XML content
    FString XMLContent = CETypeAsset->ExportToXML();

    // Show file save dialog
    IDesktopPlatform* DesktopPlatform = FDesktopPlatformModule::Get();
    if (DesktopPlatform)
    {
        TArray<FString> SaveFilenames;
        FString DefaultFileName = CETypeAsset->GetName() + TEXT(".xml");
        
        bool bSaveFile = DesktopPlatform->SaveFileDialog(
            FSlateApplication::Get().FindBestParentWindowHandleForDialogs(nullptr),
            TEXT("Save CE Type XML"),
            FPaths::GetPath(DefaultFileName),
            DefaultFileName,
            TEXT("XML Files (*.xml)|*.xml"),
            EFileDialogFlags::None,
            SaveFilenames
        );

        if (bSaveFile && SaveFilenames.Num() > 0)
        {
            FString SaveFileName = SaveFilenames[0];
            if (FFileHelper::SaveStringToFile(XMLContent, *SaveFileName))
            {
                // Show success notification
                FNotificationInfo Info(FText::Format(
                    FText::FromString("Successfully exported CE Type to {0}"),
                    FText::FromString(FPaths::GetCleanFilename(SaveFileName))
                ));
                Info.ExpireDuration = 5.0f;
                FSlateNotificationManager::Get().AddNotification(Info);
            }
            else
            {
                // Show error notification
                FNotificationInfo Info(FText::Format(
                    FText::FromString("Failed to export CE Type to {0}"),
                    FText::FromString(FPaths::GetCleanFilename(SaveFileName))
                ));
                Info.ExpireDuration = 5.0f;
                FSlateNotificationManager::Get().AddNotification(Info);
            }
        }
    }

    return FReply::Handled();
}
