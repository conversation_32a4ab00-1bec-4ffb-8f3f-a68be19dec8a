// CETypeFactory.cpp
#include "CEType/CETypeFactory.h"
#include "CEType/CEType.h"

UCETypeFactory::UCETypeFactory()
{
    SupportedClass = UCEType::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UCETypeFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new CE Type asset
    UCEType* NewAsset = NewObject<UCEType>(InParent, Class, Name, Flags);
    return NewAsset;
}

bool UCETypeFactory::ShouldShowInNewMenu() const
{
    return true;
}
