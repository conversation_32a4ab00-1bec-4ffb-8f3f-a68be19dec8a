// CEUsageAssetActions.cpp
#include "CEType/CEUsageAssetActions.h"

FCEUsageAssetActions::FCEUsageAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FCEUsageAssetActions::GetName() const
{
    return FText::FromString(TEXT("CE Usage"));
}

FColor FCEUsageAssetActions::GetTypeColor() const
{
    // Use a distinctive color for CE Usage assets
    return FColor(200, 100, 0); // Orange
}

UClass* FCEUsageAssetActions::GetSupportedClass() const
{
    return UCEUsage::StaticClass();
}

uint32 FCEUsageAssetActions::GetCategories()
{
    return AssetCategory;
}
