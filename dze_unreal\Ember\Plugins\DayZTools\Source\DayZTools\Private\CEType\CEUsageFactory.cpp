// CEUsageFactory.cpp
#include "CEType/CEUsageFactory.h"
#include "CEType/CEUsage.h"

UCEUsageFactory::UCEUsageFactory()
{
    SupportedClass = UCEUsage::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UCEUsageFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new CE Usage asset
    UCEUsage* NewAsset = NewObject<UCEUsage>(InParent, Class, Name, Flags);
    return NewAsset;
}

bool UCEUsageFactory::ShouldShowInNewMenu() const
{
    return true;
}
