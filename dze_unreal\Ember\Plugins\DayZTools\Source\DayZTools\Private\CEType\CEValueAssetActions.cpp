// CEValueAssetActions.cpp
#include "CEType/CEValueAssetActions.h"

FCEValueAssetActions::FCEValueAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FCEValueAssetActions::GetName() const
{
    return FText::FromString(TEXT("CE Value"));
}

FColor FCEValueAssetActions::GetTypeColor() const
{
    // Use a distinctive color for CE Value assets
    return FColor(50, 150, 200); // Light Blue
}

UClass* FCEValueAssetActions::GetSupportedClass() const
{
    return UCEValue::StaticClass();
}

uint32 FCEValueAssetActions::GetCategories()
{
    return AssetCategory;
}
