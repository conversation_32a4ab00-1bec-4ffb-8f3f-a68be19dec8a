// CEValueFactory.cpp
#include "CEType/CEValueFactory.h"
#include "CEType/CEValue.h"

UCEValueFactory::UCEValueFactory()
{
    SupportedClass = UCEValue::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UCEValueFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new CE Value asset
    UCEValue* NewAsset = NewObject<UCEValue>(InParent, Class, Name, Flags);
    return NewAsset;
}

bool UCEValueFactory::ShouldShowInNewMenu() const
{
    return true;
}
