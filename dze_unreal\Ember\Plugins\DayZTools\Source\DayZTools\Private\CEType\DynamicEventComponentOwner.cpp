// DynamicEventComponentOwner.cpp
#include "CEType/DynamicEventComponentOwner.h"

// Sets default values
ADynamicEventComponentOwner::ADynamicEventComponentOwner()
{
    // Set this actor to call Tick() every frame
    PrimaryActorTick.bCanEverTick = false;

    // Create a root component
    RootComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootComponent"));
}

// Called when the game starts or when spawned
void ADynamicEventComponentOwner::BeginPlay()
{
    Super::BeginPlay();
}

// Called every frame
void ADynamicEventComponentOwner::Tick(float DeltaTime)
{
    Super::Tick(DeltaTime);
}
