#include "ChannelSwizzling.h"
#include <algorithm>

TexSwizzle ARGBSwizzle::operator[](int ch) const
{
    switch (ch)
    {
    case 0: return SwizA;
    case 1: return SwizR;
    case 2: return SwizG;
    case 3: return SwizB;
    default: throw std::out_of_range("Invalid channel index");
    }
}

void ARGBSwizzle::SetSwizzle(int ch, TexSwizzle value)
{
    switch (ch)
    {
    case 0: SwizA = value; break;
    case 1: SwizR = value; break;
    case 2: SwizG = value; break;
    case 3: SwizB = value; break;
    default: throw std::out_of_range("Invalid channel index");
    }
}

ARGBSwizzle ARGBSwizzle::Default = { TexSwizzle::TSBlue, TexSwizzle::TSGreen, TexSwizzle::TSRed, TexSwizzle::TSAlpha };

void ChannelSwizzling::Apply(std::vector<uint8_t>& argbPixels, const ARGBSwizzle& swizzle)
{
    ARGBSwizzle invSwizzle = ARGBSwizzle::Default;
    InvertSwizzle(invSwizzle, swizzle, 0);
    InvertSwizzle(invSwizzle, swizzle, 1);
    InvertSwizzle(invSwizzle, swizzle, 2);
    InvertSwizzle(invSwizzle, swizzle, 3);
    ChannelSwizzle(invSwizzle, argbPixels);
}

void ChannelSwizzling::InvertSwizzle(ARGBSwizzle& invSwizzle, const ARGBSwizzle& swizzle, uint8_t ch)
{
    TexSwizzle swiz = static_cast<TexSwizzle>(static_cast<int>(TexSwizzle::TSAlpha) + ch);
    if (swizzle[ch] >= TexSwizzle::TSInvAlpha && swizzle[ch] <= TexSwizzle::TSInvBlue)
    {
        invSwizzle.SetSwizzle(static_cast<int>(swizzle[ch]) - static_cast<int>(TexSwizzle::TSInvAlpha),
            static_cast<TexSwizzle>(static_cast<int>(TexSwizzle::TSInvAlpha) - static_cast<int>(TexSwizzle::TSAlpha) + static_cast<int>(swiz)));
    }
    else if (swizzle[ch] <= TexSwizzle::TSBlue)
    {
        invSwizzle.SetSwizzle(static_cast<int>(swizzle[ch]), swiz);
    }
}

std::tuple<int, int, int> ChannelSwizzling::CheckInvSwizzle(TexSwizzle swiz)
{
    if (swiz == TexSwizzle::TSOne)
    {
        return { 0, 0, 255 };
    }
    int mul = 1;
    int add = 0;
    switch (swiz)
    {
    case TexSwizzle::TSInvAlpha: swiz = TexSwizzle::TSAlpha; mul = -1; add = 255; break;
    case TexSwizzle::TSInvRed: swiz = TexSwizzle::TSRed; mul = -1; add = 255; break;
    case TexSwizzle::TSInvGreen: swiz = TexSwizzle::TSGreen; mul = -1; add = 255; break;
    case TexSwizzle::TSInvBlue: swiz = TexSwizzle::TSBlue; mul = -1; add = 255; break;
    }
    int offset = swiz < TexSwizzle::TSOne ? 24 - static_cast<int>(swiz) * 8 : 0;

    return { offset, mul, add };
}

void ChannelSwizzling::ChannelSwizzle(const ARGBSwizzle& channelSwizzle, std::vector<uint8_t>& argbPixels)
{
    if (channelSwizzle[0] == TexSwizzle::TSAlpha && channelSwizzle[1] == TexSwizzle::TSRed &&
        channelSwizzle[2] == TexSwizzle::TSGreen && channelSwizzle[3] == TexSwizzle::TSBlue)
    {
        return;
    }

    auto [aOffset, mulA, addA] = CheckInvSwizzle(channelSwizzle[0]);
    auto [rOffset, mulR, addR] = CheckInvSwizzle(channelSwizzle[1]);
    auto [gOffset, mulG, addG] = CheckInvSwizzle(channelSwizzle[2]);
    auto [bOffset, mulB, addB] = CheckInvSwizzle(channelSwizzle[3]);

    int nPixel = argbPixels.size() / 4;
    while (--nPixel >= 0)
    {
        int pixOffset = nPixel * 4;
        uint32_t p = *reinterpret_cast<uint32_t*>(&argbPixels[pixOffset]);
        int a = (p >> aOffset) & 0xff;
        int r = (p >> rOffset) & 0xff;
        int g = (p >> gOffset) & 0xff;
        int b = (p >> bOffset) & 0xff;

        argbPixels[pixOffset] = static_cast<uint8_t>(b * mulB + addB);
        argbPixels[pixOffset + 1] = static_cast<uint8_t>(g * mulG + addG);
        argbPixels[pixOffset + 2] = static_cast<uint8_t>(r * mulR + addR);
        argbPixels[pixOffset + 3] = static_cast<uint8_t>(a * mulA + addA);
    }
}