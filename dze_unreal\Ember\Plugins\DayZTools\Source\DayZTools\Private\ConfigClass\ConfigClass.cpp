// ConfigClass.cpp
#include "ConfigClass/ConfigClass.h"
#include "P3DBlueprint.h"

UConfigClass::UConfigClass(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
}

FString UConfigClass::ExportToConfig() const
{
    FString ConfigText;
    
    // Generate config file format if the model is set
    if (Model.IsValid())
    {
        ConfigText = FString::Printf(TEXT("class %s\n{\n\tmodel = \"%s\";\n};\n"),
            *GetName(),
            *Model.GetAssetName());
    }
    
    return ConfigText;
}