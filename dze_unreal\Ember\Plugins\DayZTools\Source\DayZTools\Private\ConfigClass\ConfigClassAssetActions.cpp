// ConfigClassAssetActions.cpp
#include "ConfigClass/ConfigClassAssetActions.h"
#include "ConfigClass/ConfigClass.h"

#define LOCTEXT_NAMESPACE "AssetTypeActions"

FConfigClassAssetActions::FConfigClassAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FConfigClassAssetActions::GetName() const
{
    return LOCTEXT("ConfigClassAssetName", "Config Class");
}

FColor FConfigClassAssetActions::GetTypeColor() const
{
    // Green color
    return FColor(80, 200, 120);
}

UClass* FConfigClassAssetActions::GetSupportedClass() const
{
    return UConfigClass::StaticClass();
}

uint32 FConfigClassAssetActions::GetCategories()
{
    return AssetCategory;
}

#undef LOCTEXT_NAMESPACE