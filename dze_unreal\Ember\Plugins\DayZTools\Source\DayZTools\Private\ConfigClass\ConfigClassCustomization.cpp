// ConfigClassCustomization.cpp
#include "ConfigClass/ConfigClassCustomization.h"
#include "ConfigClass/ConfigClass.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "DetailWidgetRow.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Text/STextBlock.h"
#include "Misc/MessageDialog.h"

#define LOCTEXT_NAMESPACE "ConfigClassEditor"

TSharedRef<IDetailCustomization> FConfigClassCustomization::MakeInstance()
{
    return MakeShareable(new FConfigClassCustomization);
}

void FConfigClassCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // Get the selected ConfigClass asset
    TArray<TWeakObjectPtr<UObject>> SelectedObjects;
    DetailBuilder.GetObjectsBeingCustomized(SelectedObjects);

    if (SelectedObjects.Num() == 1)
    {
        ConfigClassAsset = Cast<UConfigClass>(SelectedObjects[0].Get());
    }

    // Add a custom row with an export button
    IDetailCategoryBuilder& ConfigSettingsCategory = DetailBuilder.EditCategory("Config Settings", FText::GetEmpty(), ECategoryPriority::Important);

    ConfigSettingsCategory.AddCustomRow(LOCTEXT("ExportConfigRow", "Export Config"))
    .ValueContent()
    [
        SNew(SButton)
        .Text(LOCTEXT("ExportConfig", "Show Config"))
        .OnClicked(this, &FConfigClassCustomization::OnExportToConfigClicked)
        .ToolTipText(LOCTEXT("ExportConfigTooltip", "Show the config class in DayZ config format"))
    ];
}

FReply FConfigClassCustomization::OnExportToConfigClicked()
{
    if (ConfigClassAsset.IsValid())
    {
        FString ConfigText = ConfigClassAsset->ExportToConfig();
        
        if (!ConfigText.IsEmpty())
        {
            FMessageDialog::Open(EAppMsgType::Ok, FText::FromString(ConfigText));
        }
        else
        {
            FMessageDialog::Open(EAppMsgType::Ok, LOCTEXT("ExportFailed", "Failed to generate config. Make sure model is set."));
        }
    }
    
    return FReply::Handled();
}

#undef LOCTEXT_NAMESPACE