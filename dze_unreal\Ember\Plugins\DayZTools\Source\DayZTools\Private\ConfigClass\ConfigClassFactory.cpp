// ConfigClassFactory.cpp
#include "ConfigClass/ConfigClassFactory.h"
#include "ConfigClass/ConfigClass.h"

UConfigClassFactory::UConfigClassFactory()
{
    SupportedClass = UConfigClass::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
    bEditorImport = false;
    
    // Set the default file extension to .uasset
    Formats.Add(TEXT("configclass;Config Class"));
}

UObject* UConfigClassFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    UConfigClass* NewConfigClass = NewObject<UConfigClass>(InParent, Class, Name, Flags);
    return NewConfigClass;
}

bool UConfigClassFactory::ShouldShowInNewMenu() const
{
    return true;
}