#include "DayZGameTab.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Styling/AppStyle.h"
#include "Styling/ToolBarStyle.h"
#include "Windows/WindowsHWrapper.h" // For HWND manipulation
#include "Windows/AllowWindowsPlatformTypes.h"
#include <Windows.h>
#include "Windows/HideWindowsPlatformTypes.h"

#define LOCTEXT_NAMESPACE "DayZGameTab"

void DayZGameTab::Construct(const FArguments& InArgs)
{
    // Initialize the command list
    CommandList = MakeShareable(new FUICommandList);

    // Initialize play mode
    CurrentPlayMode = EDayZPlayMode::Offline;

    ChildSlot
        [
            SNew(SVerticalBox)
                // Add the toolbar
                + SVerticalBox::Slot()
                .AutoHeight()
                [
                    MakeToolbar()
                ]
                // Add the rest of your content here
                + SVerticalBox::Slot()
                .Padding(10)
                .FillHeight(1.0f) // Fill the remaining space
                [
                    SNew(SBox)
                        .HAlign(HAlign_Fill)
                        .VAlign(VAlign_Fill)
                        [
                            // Placeholder for main content
                            SNew(STextBlock)
                                .Text(LOCTEXT("WelcomeText", "Welcome to the DayZ Game window!"))
                        ]
                ]
        ];
}

TSharedRef<SWidget> DayZGameTab::MakeToolbar()
{
    FToolBarBuilder ToolbarBuilder(CommandList, FMultiBoxCustomization::None);

    // Set the toolbar style to the default "SlimToolBar" style
    ToolbarBuilder.SetStyle(&FAppStyle::Get(), "SlimToolBar");

    // Group 1: Launch Programs
    ToolbarBuilder.BeginSection("LaunchPrograms");
    {
        // Workbench button (icon-only)
        ToolbarBuilder.AddToolBarButton(
            FUIAction(FExecuteAction::CreateSP(this, &DayZGameTab::OnLaunchWorkbench)),
            NAME_None,
            FText::GetEmpty(), // No label
            LOCTEXT("WorkbenchTooltip", "Launch Workbench"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Settings") // Use a valid default icon
        );

        // Object Builder button
        ToolbarBuilder.AddToolBarButton(
            FUIAction(FExecuteAction::CreateSP(this, &DayZGameTab::OnLaunchObjectBuilder)),
            NAME_None,
            FText::GetEmpty(),
            LOCTEXT("ObjectBuilderTooltip", "Launch Object Builder"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.StaticMesh") // Use a valid default icon
        );

        // Terrain Builder button
        ToolbarBuilder.AddToolBarButton(
            FUIAction(FExecuteAction::CreateSP(this, &DayZGameTab::OnLaunchTerrainBuilder)),
            NAME_None,
            FText::GetEmpty(),
            LOCTEXT("TerrainBuilderTooltip", "Launch Terrain Builder"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Landscape") // Use a valid default icon
        );
    }
    ToolbarBuilder.EndSection();

    // Group 2: Other Actions
    ToolbarBuilder.BeginSection("OtherActions");
    {
        // Build PBO button
        ToolbarBuilder.AddToolBarButton(
            FUIAction(FExecuteAction::CreateSP(this, &DayZGameTab::OnBuildPBO)),
            NAME_None,
            FText::GetEmpty(),
            LOCTEXT("BuildPBOTooltip", "Build PBO"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "LevelEditor.Build") // Use a valid default icon
        );

        ToolbarBuilder.BeginBlockGroup(); // Begin button group

        // Play button (green viewport play button)
        ToolbarBuilder.AddToolBarButton(
            FUIAction(FExecuteAction::CreateSP(this, &DayZGameTab::OnPlayButtonClicked)),
            NAME_None,
            FText::GetEmpty(),
            LOCTEXT("PlayButtonTooltip", "Play DayZ"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "PlayWorld.PlayInViewport") // Green play icon
        );

        // Play options combo button (three vertical dots)
        ToolbarBuilder.AddComboButton(
            FUIAction(),
            FOnGetContent::CreateSP(this, &DayZGameTab::GenerateLaunchDayZMenu),
            FText::GetEmpty(),
            LOCTEXT("LaunchDayZTooltip", "Play Options"),
            FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.EditorViewportOptions"), // Three vertical dots icon
            false // Ensure it behaves like a toolbar button
        );

        ToolbarBuilder.EndBlockGroup(); // End button group
    }
    ToolbarBuilder.EndSection();

    // Vertically center the toolbar
    return SNew(SBox)
        .VAlign(VAlign_Center)
        [
            ToolbarBuilder.MakeWidget()
        ];
}

TSharedRef<SWidget> DayZGameTab::GenerateLaunchDayZMenu()
{
    FMenuBuilder MenuBuilder(true, CommandList);

    // DayZ Offline option
    MenuBuilder.AddMenuEntry(
        LOCTEXT("PlayDayZOffline", "DayZ Offline"),
        LOCTEXT("PlayDayZOffline_Tooltip", "Play DayZ in offline mode"),
        FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Stop"), // Use a valid default icon
        FUIAction(
            FExecuteAction::CreateSP(this, &DayZGameTab::OnSelectPlayMode, EDayZPlayMode::Offline),
            FCanExecuteAction(),
            FIsActionChecked::CreateSP(this, &DayZGameTab::IsPlayModeChecked, EDayZPlayMode::Offline)
        ),
        NAME_None,
        EUserInterfaceActionType::RadioButton
    );

    // DayZ Multiplayer option
    MenuBuilder.AddMenuEntry(
        LOCTEXT("PlayDayZMultiplayer", "DayZ Multiplayer"),
        LOCTEXT("PlayDayZMultiplayer_Tooltip", "Play DayZ in multiplayer mode"),
        FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.World"), // Use a valid default icon
        FUIAction(
            FExecuteAction::CreateSP(this, &DayZGameTab::OnSelectPlayMode, EDayZPlayMode::Multiplayer),
            FCanExecuteAction(),
            FIsActionChecked::CreateSP(this, &DayZGameTab::IsPlayModeChecked, EDayZPlayMode::Multiplayer)
        ),
        NAME_None,
        EUserInterfaceActionType::RadioButton
    );

    return MenuBuilder.MakeWidget();
}

// Toolbar button actions

void DayZGameTab::OnLaunchWorkbench()
{
    // Code to launch Workbench
    UE_LOG(LogTemp, Log, TEXT("Launching Workbench..."));
    // Example: Launch external application (replace with actual path)
    FString ExecutablePath = TEXT("C:/Path/To/Workbench.exe");
    FString Params = TEXT(""); // Any command-line parameters
    FPlatformProcess::CreateProc(*ExecutablePath, *Params, true, false, false, nullptr, 0, nullptr, nullptr);
}

void DayZGameTab::OnLaunchObjectBuilder()
{
    // Code to launch Object Builder
    UE_LOG(LogTemp, Log, TEXT("Launching Object Builder..."));
    // Example: Launch external application (replace with actual path)
    FString ExecutablePath = TEXT("C:/Path/To/ObjectBuilder.exe");
    FString Params = TEXT(""); // Any command-line parameters
    FPlatformProcess::CreateProc(*ExecutablePath, *Params, true, false, false, nullptr, 0, nullptr, nullptr);
}

void DayZGameTab::OnLaunchTerrainBuilder()
{
    // Code to launch Terrain Builder
    UE_LOG(LogTemp, Log, TEXT("Launching Terrain Builder..."));
    // Example: Launch external application (replace with actual path)
    FString ExecutablePath = TEXT("C:/Path/To/TerrainBuilder.exe");
    FString Params = TEXT(""); // Any command-line parameters
    FPlatformProcess::CreateProc(*ExecutablePath, *Params, true, false, false, nullptr, 0, nullptr, nullptr);
}

void DayZGameTab::OnBuildPBO()
{
    // Code to build PBO
    UE_LOG(LogTemp, Log, TEXT("Building PBO..."));
    // Execute build process
}

void DayZGameTab::OnPlayButtonClicked()
{
    // Launch DayZ based on the selected play mode
    if (CurrentPlayMode == EDayZPlayMode::Offline)
    {
        OnLaunchDayZOffline();
    }
    else if (CurrentPlayMode == EDayZPlayMode::Multiplayer)
    {
        OnLaunchDayZMultiplayer();
    }
}

void DayZGameTab::OnSelectPlayMode(EDayZPlayMode SelectedMode)
{
    CurrentPlayMode = SelectedMode;
}

bool DayZGameTab::IsPlayModeChecked(EDayZPlayMode Mode) const
{
    return CurrentPlayMode == Mode;
}

void DayZGameTab::OnLaunchDayZOffline()
{
    // Code to launch DayZ Offline embedded in the main content area
    UE_LOG(LogTemp, Log, TEXT("Launching DayZ Offline..."));
}

void DayZGameTab::OnLaunchDayZMultiplayer()
{
    // Code to launch DayZ Multiplayer embedded in the main content area
    UE_LOG(LogTemp, Log, TEXT("Launching DayZ Multiplayer..."));

}

#undef LOCTEXT_NAMESPACE