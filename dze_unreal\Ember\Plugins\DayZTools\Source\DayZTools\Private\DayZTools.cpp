#include "DayZTools.h"
#include "HAL/FileManager.h"
#include "Misc/ConfigCacheIni.h"
#include "RvmatAsset.h"
#include "RvmatAssetDetails.h"
#include "AssetToolsModule.h"
#include "RvmatAssetActions.h"
#include "RvmatAssetCustomization.h"
#include "DayZToolsSettingsCustomization.h"
#include "PropertyEditorModule.h"
#include "ISettingsModule.h"
#include "DayZGameTab.h"
#include "Interfaces/IPluginManager.h"

#include "AssetTypeActions_P3D.h"
#include "DynamicEvent/DynamicEvent.h"
#include "DynamicEvent/DynamicEventAssetActions.h"
#include "DynamicEvent/DynamicEventCustomization.h"
#include "DynamicEvent/DynamicEventSpawner.h"
#include "CEType/CEType.h"
#include "CEType/CETypeAssetActions.h"
#include "CEType/CETypeCustomization.h"
#include "CEType/CECategory.h"
#include "CEType/CECategoryAssetActions.h"
#include "CEType/CEUsage.h"
#include "CEType/CEUsageAssetActions.h"
#include "CEType/CETag.h"
#include "CEType/CETagAssetActions.h"
#include "CEType/CEValue.h"
#include "CEType/CEValueAssetActions.h"
#include "CEType/CEItemDefinitionEditor.h"
#include "CEType/CEItemDefinitionEditorAssetActions.h"
#include "CEType/CEItemDefinitionEditorFactory.h"
#include "CEType/CEDynamicEventGroup.h"
#include "CEType/CEDynamicEventGroupAssetActions.h"
#include "CEType/CEDynamicEventGroupFactory.h"
#include "CEType/CEDynamicEventGroupChildCustomization.h"
#include "CEType/CEDynamicEventGroupChildProxy.h"
#include "ConfigClass/ConfigClass.h"
#include "ConfigClass/ConfigClassAssetActions.h"
#include "FBiomeDataAssetTypeActions.h"
#include "ConfigClass/ConfigClassCustomization.h"
#include "CELootPoints/CELootPoints.h"
#include "CELootPoints/CELootPointsAssetActions.h"
#include "CELootPoints/CELootPointsItemProxy.h"
#include "CELootPoints/CELootPointsItemProxyCustomization.h"

#include "DayZToolsStyle.h"
#include "P3DActorEditorToolbar.h"
#include "LevelEditorToolbar.h"
#include "Assets/TerrainLayers/TerrainLayers.h"
#include "Assets/TerrainLayers/TerrainLayersCustomization.h"
#include "Assets/TerrainLayers/TerrainLayerCustomization.h"
#include "CEType/CEDynamicEventGroupEditorViewportCommands.h"
#include "CEType/CEServerMessagesActions.h"
#include "CEType/CEServerMessagesCustomization.h"

static const FName DayZToolsTabName("DayZGame");

#define LOCTEXT_NAMESPACE "FDayZToolsModule"


void FDayZToolsModule::StartupModule()
{
    PluginCommands = MakeShareable(new FUICommandList);

    /////////// P3D PREFAB ACTOR //////////////

    IAssetTools& AssetTools = FModuleManager::LoadModuleChecked<FAssetToolsModule>("AssetTools").Get();

    TerrainAssetActions = MakeShareable(new FTerrainLayersAssetTypeActions());
    AssetTools.RegisterAssetTypeActions(TerrainAssetActions.ToSharedRef());

    // Use a custom category for P3D assets
    EAssetTypeCategories::Type P3DCategory = AssetTools.RegisterAdvancedAssetCategory(FName(TEXT("P3D")), FText::FromString("P3D"));

    P3DAssetAction = MakeShareable(new FAssetTypeActions_P3D());
    AssetTools.RegisterAssetTypeActions(P3DAssetAction.ToSharedRef());

    // Register asset category and actions
    DayZAssetCategoryBit = AssetTools.RegisterAdvancedAssetCategory(FName(TEXT("DayZ")), LOCTEXT("DayZAssetCategory", "DayZ"));
    RvmatAssetAction = MakeShareable(new FRvmatAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(RvmatAssetAction.ToSharedRef());

    // Register Dynamic Event asset type
    DynamicEventAssetAction = MakeShareable(new FDynamicEventAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(DynamicEventAssetAction.ToSharedRef());

    // Register CE Type asset type
    CETypeAssetAction = MakeShareable(new FCETypeAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(CETypeAssetAction.ToSharedRef());

    // Register CE Category asset type
    CECategoryAssetAction = MakeShareable(new FCECategoryAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(CECategoryAssetAction.ToSharedRef());

    // Register CE Usage asset type
    CEUsageAssetAction = MakeShareable(new FCEUsageAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(CEUsageAssetAction.ToSharedRef());

    // Register CE Tag asset type
    CETagAssetAction = MakeShareable(new FCETagAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(CETagAssetAction.ToSharedRef());

    // Register CE Value asset type
    CEValueAssetAction = MakeShareable(new FCEValueAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(CEValueAssetAction.ToSharedRef());

    // Register CE Item Definition Editor asset type
    CEItemDefinitionEditorAssetAction = MakeShareable(new FCEItemDefinitionEditorAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(CEItemDefinitionEditorAssetAction.ToSharedRef());

    // Register CE Dynamic Event Group asset type
    CEDynamicEventGroupAssetAction = MakeShareable(new FCEDynamicEventGroupAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(CEDynamicEventGroupAssetAction.ToSharedRef());

    // Register Config Class asset type
    ConfigClassAssetAction = MakeShareable(new FConfigClassAssetActions(DayZAssetCategoryBit));
    AssetTools.RegisterAssetTypeActions(ConfigClassAssetAction.ToSharedRef());

    // Register CE Loot Points asset type
    CELootPointsAssetAction = MakeShareable(new FCELootPointsAssetActions());
    AssetTools.RegisterAssetTypeActions(CELootPointsAssetAction.ToSharedRef());

    // Register Server Messages asset type
    TSharedPtr<FAssetTypeActions_Base> ServerMessagesAction = MakeShareable(new FCEServerMessagesActions);
    AssetTools.RegisterAssetTypeActions(ServerMessagesAction.ToSharedRef());

    BiomeDataAssetTypeActions = MakeShareable(new FBiomeDataAssetTypeActions());
    AssetTools.RegisterAssetTypeActions(BiomeDataAssetTypeActions.ToSharedRef());

    // Register the commands ONCE for the module
    FCEDynamicEventGroupEditorViewportCommands::Register();

    FPropertyEditorModule& PropertyModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");
    PropertyModule.RegisterCustomClassLayout(
        UTerrainLayers::StaticClass()->GetFName(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FTerrainLayersCustomization::MakeInstance)
    );

    // Register custom class layouts
    PropertyModule.RegisterCustomClassLayout(
        URvmatAsset::StaticClass()->GetFName(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FRvmatAssetCustomization::MakeInstance)
    );
    PropertyModule.RegisterCustomClassLayout(
        UDayZToolsSettings::StaticClass()->GetFName(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FDayZToolsSettingsCustomization::MakeInstance)
    );

    // Register custom layout for Dynamic Event assets
    PropertyModule.RegisterCustomClassLayout(
        UDynamicEvent::StaticClass()->GetFName(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FDynamicEventCustomization::MakeInstance)
    );

    // Register custom layout for CE Type assets
    PropertyModule.RegisterCustomClassLayout(
        UCEType::StaticClass()->GetFName(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FCETypeCustomization::MakeInstance)
    );

    // Register custom layout for Dynamic Event Group Child Proxy
    PropertyModule.RegisterCustomClassLayout(
        UCEDynamicEventGroupChildProxy::StaticClass()->GetFName(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FCEDynamicEventGroupChildCustomization::MakeInstance)
    );

    // Register custom layout for Config Class assets
    PropertyModule.RegisterCustomClassLayout(
        UConfigClass::StaticClass()->GetFName(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FConfigClassCustomization::MakeInstance)
    );

    // Register custom layout for CE Loot Points Item Proxy
    PropertyModule.RegisterCustomClassLayout(
        UCELootPointsItemProxy::StaticClass()->GetFName(),
        FOnGetDetailCustomizationInstance::CreateStatic(&FCELootPointsItemProxyCustomization::MakeInstance)
    );

    // Register Server Messages customization
    PropertyModule.RegisterCustomClassLayout(
        "CEServerMessages",
        FOnGetDetailCustomizationInstance::CreateStatic(&FCEServerMessagesCustomization::MakeInstance)
    );

    PropertyModule.RegisterCustomPropertyTypeLayout("TerrainLayer",
        FOnGetPropertyTypeCustomizationInstance::CreateStatic(&FTerrainLayerCustomization::MakeInstance));

    // Register settings
    if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
    {
        SettingsModule->RegisterSettings("Project", "Plugins", "DayZ Tools",
            LOCTEXT("DayZToolsSettingsName", "DayZ Tools"),
            LOCTEXT("DayZToolsSettingsDescription", "Configure DayZ Tools plugin settings"),
            GetMutableDefault<UDayZToolsSettings>()
        );
    }

    FDayZToolsStyle::Initialize();
    FLevelEditorToolbar::Initialize();
    FP3DActorEditorToolbar::Initialize();

    UE_LOG(LogTemp, Warning, TEXT("DayZ Tools module started"));
}

void FDayZToolsModule::ShutdownModule()
{
    FDayZToolsStyle::Shutdown();
    FLevelEditorToolbar::Shutdown();

    // Unregister asset type actions
    if (FModuleManager::Get().IsModuleLoaded("AssetTools"))
    {
        IAssetTools& AssetTools = FModuleManager::GetModuleChecked<FAssetToolsModule>("AssetTools").Get();

        // Unregister P3DAssetAction using the stored shared pointer
        if (P3DAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(P3DAssetAction.ToSharedRef());
            P3DAssetAction.Reset();
        }

        // Unregister RvmatAssetAction using the stored shared pointer
        if (RvmatAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(RvmatAssetAction.ToSharedRef());
            RvmatAssetAction.Reset();
        }

        // Unregister DynamicEventAssetAction
        if (DynamicEventAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(DynamicEventAssetAction.ToSharedRef());
            DynamicEventAssetAction.Reset();
        }

        // Unregister CETypeAssetAction
        if (CETypeAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(CETypeAssetAction.ToSharedRef());
            CETypeAssetAction.Reset();
        }

        // Unregister CECategoryAssetAction
        if (CECategoryAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(CECategoryAssetAction.ToSharedRef());
            CECategoryAssetAction.Reset();
        }

        // Unregister CEUsageAssetAction
        if (CEUsageAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(CEUsageAssetAction.ToSharedRef());
            CEUsageAssetAction.Reset();
        }

        // Unregister CETagAssetAction
        if (CETagAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(CETagAssetAction.ToSharedRef());
            CETagAssetAction.Reset();
        }

        // Unregister CEValueAssetAction
        if (CEValueAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(CEValueAssetAction.ToSharedRef());
            CEValueAssetAction.Reset();
        }

        // Unregister CEItemDefinitionEditorAssetAction
        if (CEItemDefinitionEditorAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(CEItemDefinitionEditorAssetAction.ToSharedRef());
            CEItemDefinitionEditorAssetAction.Reset();
        }

        // Unregister CEDynamicEventGroupAssetAction
        if (CEDynamicEventGroupAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(CEDynamicEventGroupAssetAction.ToSharedRef());
            CEDynamicEventGroupAssetAction.Reset();
        }

        // Unregister ConfigClassAssetAction
        if (ConfigClassAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(ConfigClassAssetAction.ToSharedRef());
            ConfigClassAssetAction.Reset();
        }

        // Unregister CELootPointsAssetAction
        if (CELootPointsAssetAction.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(CELootPointsAssetAction.ToSharedRef());
            CELootPointsAssetAction.Reset();
        }

        if (TerrainAssetActions.IsValid())
        {
            AssetTools.UnregisterAssetTypeActions(TerrainAssetActions.ToSharedRef());
        }

    }

    // Unregister custom class layouts
    if (FModuleManager::Get().IsModuleLoaded("PropertyEditor"))
    {
        FPropertyEditorModule& PropertyModule = FModuleManager::GetModuleChecked<FPropertyEditorModule>("PropertyEditor");
        PropertyModule.UnregisterCustomClassLayout(URvmatAsset::StaticClass()->GetFName());
        PropertyModule.UnregisterCustomClassLayout(UDayZToolsSettings::StaticClass()->GetFName());
        PropertyModule.UnregisterCustomClassLayout(UDynamicEvent::StaticClass()->GetFName());
        PropertyModule.UnregisterCustomClassLayout(UCEType::StaticClass()->GetFName());
        PropertyModule.UnregisterCustomClassLayout(UCEDynamicEventGroupChildProxy::StaticClass()->GetFName());
        PropertyModule.UnregisterCustomClassLayout(UCELootPointsItemProxy::StaticClass()->GetFName());
        PropertyModule.UnregisterCustomClassLayout("CEServerMessages");
    }

    // Unregister settings
    if (ISettingsModule* SettingsModule = FModuleManager::GetModulePtr<ISettingsModule>("Settings"))
    {
        SettingsModule->UnregisterSettings("Project", "Plugins", "DayZ Tools");
    }

    if (FSlateApplication::IsInitialized()) // Check if Slate is still up
    {
        FCEDynamicEventGroupEditorViewportCommands::Unregister();
    }

    UE_LOG(LogTemp, Warning, TEXT("DayZ Tools module shut down"));
}


TSharedRef<SDockTab> FDayZToolsModule::OnSpawnPluginTab(const FSpawnTabArgs& SpawnTabArgs)
{
    return SNew(SDockTab)
        .TabRole(ETabRole::NomadTab)
        [
            // Use your custom widget here
            SNew(DayZGameTab)
        ];
}


#undef LOCTEXT_NAMESPACE

IMPLEMENT_MODULE(FDayZToolsModule, DayZTools)
