#include "DayZToolsSettings.h"

UDayZToolsSettings::UDayZToolsSettings(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    ProjectTrunkPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectDir());
    ProjectToolsPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectDir(), TEXT("Tools")));
    MissionPath = FPaths::ConvertRelativePathToFull(FPaths::Combine(FPaths::ProjectDir(), TEXT("Missions")));
}