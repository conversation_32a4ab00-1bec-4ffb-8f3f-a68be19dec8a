#include "DayZToolsSettingsCustomization.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "DetailWidgetRow.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Text/STextBlock.h"
#include "DesktopPlatformModule.h"

TSharedRef<IDetailCustomization> FDayZToolsSettingsCustomization::MakeInstance()
{
    return MakeShareable(new FDayZToolsSettingsCustomization);
}

void FDayZToolsSettingsCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    IDetailCategoryBuilder& PathsCategory = DetailBuilder.EditCategory("Paths");

    TSharedRef<IPropertyHandle> ProjectTrunkPathHandle = DetailBuilder.GetProperty("ProjectTrunkPath");
    TSharedRef<IPropertyHandle> ProjectToolsPathHandle = DetailBuilder.GetProperty("ProjectToolsPath");
    TSharedRef<IPropertyHandle> MissionPathHandle = DetailBuilder.GetProperty("MissionPath");

    PathsCategory.AddProperty(ProjectTrunkPathHandle)
        .CustomWidget()
        .NameContent()
        [
            ProjectTrunkPathHandle->CreatePropertyNameWidget()
        ]
        .ValueContent()
        .MinDesiredWidth(250.f)
        .MaxDesiredWidth(0.f)
        [
            SNew(SHorizontalBox)
                + SHorizontalBox::Slot()
                .FillWidth(1.0f)
                .VAlign(VAlign_Center)
                [
                    ProjectTrunkPathHandle->CreatePropertyValueWidget()
                ]
                + SHorizontalBox::Slot()
                .AutoWidth()
                .Padding(4.0f, 0.0f, 0.0f, 0.0f)
                .VAlign(VAlign_Center)
                [
                    SNew(SButton)
                        .OnClicked(FOnClicked::CreateLambda([this, ProjectTrunkPathHandle]() -> FReply
                            {
                                return this->OnBrowseButtonClicked(ProjectTrunkPathHandle, FString("Select Project Trunk Path"));
                            }))
                        .Text(FText::FromString("Browse"))
                ]
        ];

    PathsCategory.AddProperty(ProjectToolsPathHandle)
        .CustomWidget()
        .NameContent()
        [
            ProjectToolsPathHandle->CreatePropertyNameWidget()
        ]
        .ValueContent()
        .MinDesiredWidth(250.f)
        .MaxDesiredWidth(0.f)
        [
            SNew(SHorizontalBox)
                + SHorizontalBox::Slot()
                .FillWidth(1.0f)
                .VAlign(VAlign_Center)
                [
                    ProjectToolsPathHandle->CreatePropertyValueWidget()
                ]
                + SHorizontalBox::Slot()
                .AutoWidth()
                .Padding(4.0f, 0.0f, 0.0f, 0.0f)
                .VAlign(VAlign_Center)
                [
                    SNew(SButton)
                        .OnClicked(FOnClicked::CreateLambda([this, ProjectToolsPathHandle]() -> FReply
                            {
                                return this->OnBrowseButtonClicked(ProjectToolsPathHandle, FString("Select Project Tools Path"));
                            }))
                        .Text(FText::FromString("Browse"))
                ]
        ];

    PathsCategory.AddProperty(MissionPathHandle)
        .CustomWidget()
        .NameContent()
        [
            MissionPathHandle->CreatePropertyNameWidget()
        ]
        .ValueContent()
        .MinDesiredWidth(250.f)
        .MaxDesiredWidth(0.f)
        [
            SNew(SHorizontalBox)
                + SHorizontalBox::Slot()
                .FillWidth(1.0f)
                .VAlign(VAlign_Center)
                [
                    MissionPathHandle->CreatePropertyValueWidget()
                ]
                + SHorizontalBox::Slot()
                .AutoWidth()
                .Padding(4.0f, 0.0f, 0.0f, 0.0f)
                .VAlign(VAlign_Center)
                [
                    SNew(SButton)
                        .OnClicked(FOnClicked::CreateLambda([this, MissionPathHandle]() -> FReply
                            {
                                return this->OnBrowseButtonClicked(MissionPathHandle, FString("Select Mission Path"));
                            }))
                        .Text(FText::FromString("Browse"))
                ]
        ];

}

FString FDayZToolsSettingsCustomization::OpenDirectoryDialog(const FString& DialogTitle, const FString& DefaultPath)
{
    IDesktopPlatform* DesktopPlatform = FDesktopPlatformModule::Get();
    if (DesktopPlatform)
    {
        const void* ParentWindowWindowHandle = FSlateApplication::Get().FindBestParentWindowHandleForDialogs(nullptr);

        FString FolderName;
        const FString Title = DialogTitle;
        const bool bFolderSelected = DesktopPlatform->OpenDirectoryDialog(
            ParentWindowWindowHandle,
            Title,
            DefaultPath,
            FolderName
        );

        if (bFolderSelected)
        {
            return FolderName;
        }
    }

    return FString();
}

FReply FDayZToolsSettingsCustomization::OnBrowseButtonClicked(TSharedRef<IPropertyHandle> PropertyHandle, const FString& DialogTitle)
{
    FString CurrentPath;
    PropertyHandle->GetValue(CurrentPath);

    FString NewPath = OpenDirectoryDialog(DialogTitle, CurrentPath);
    if (!NewPath.IsEmpty())
    {
        PropertyHandle->SetValue(NewPath);
    }

    return FReply::Handled();
}
