#include "DayZToolsStyle.h"
#include "Interfaces/IPluginManager.h"
#include "Styling/SlateStyle.h"
#include "Styling/SlateTypes.h"
#include "Styling/SlateStyleRegistry.h"
#include "Brushes/SlateImageBrush.h"

//----------------------------------------------------------------------------------
// Static member definition
//----------------------------------------------------------------------------------
TSharedPtr<FSlateStyleSet> FDayZToolsStyle::StyleSet = nullptr;

//----------------------------------------------------------------------------------
// FDayZToolsStyle Implementation
//----------------------------------------------------------------------------------
void FDayZToolsStyle::Initialize()
{
    if (!StyleSet.IsValid())
    {
        // 1) Create a new StyleSet with a unique name
        StyleSet = MakeShareable(new FSlateStyleSet(GetStyleSetName()));

        // 2) Locate plugin content directory
        FString PluginBaseDir = IPluginManager::Get().FindPlugin(TEXT("DayZTools"))->GetBaseDir();
        FString PluginContentDir = FPaths::Combine(PluginBaseDir, TEXT("Content"));
        StyleSet->SetContentRoot(PluginContentDir);

        // 3) Register any images/icons you need
        const FVector2D Icon40x40(40.f, 40.f);

        StyleSet->Set("DayZTools.Workbench",
            new FSlateImageBrush(
                StyleSet->RootToContentDir(TEXT("Resources/Workbench"), TEXT(".png")),
                Icon40x40));

        StyleSet->Set("DayZTools.ObjectBuilder",
            new FSlateImageBrush(
                StyleSet->RootToContentDir(TEXT("Resources/ObjectBuilder"), TEXT(".png")),
                Icon40x40));

        StyleSet->Set("DayZTools.TerrainBuilder",
            new FSlateImageBrush(
                StyleSet->RootToContentDir(TEXT("Resources/TerrainBuilder"), TEXT(".png")),
                Icon40x40));

        StyleSet->Set("DayZTools.BuildPBO",
            new FSlateImageBrush(
                StyleSet->RootToContentDir(TEXT("Resources/BuildPBO"), TEXT(".png")),
                Icon40x40));

        // 4) Register the style
        FSlateStyleRegistry::RegisterSlateStyle(*StyleSet.Get());
    }
}

void FDayZToolsStyle::Shutdown()
{
    if (StyleSet.IsValid())
    {
        FSlateStyleRegistry::UnRegisterSlateStyle(*StyleSet.Get());
        StyleSet.Reset();
    }
}

TSharedPtr<ISlateStyle> FDayZToolsStyle::Get()
{
    return StyleSet;
}

FName FDayZToolsStyle::GetStyleSetName()
{
    static FName StyleSetName(TEXT("DayZToolsStyle"));
    return StyleSetName;
}
