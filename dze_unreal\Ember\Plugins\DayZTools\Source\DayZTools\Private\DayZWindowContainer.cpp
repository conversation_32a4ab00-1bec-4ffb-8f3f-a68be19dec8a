#include "DayZWindowContainer.h"
#include "Framework/Application/SlateApplication.h"
#include "Async/Async.h"
#include "Windows/AllowWindowsPlatformTypes.h"
#include <Windows.h>
#include "Windows/HideWindowsPlatformTypes.h"
#include "GenericPlatform/GenericWindow.h"

void SDayZWindowContainer::Construct(const FArguments& InArgs)
{
    ContentWindowHandle = nullptr;
}

void SDayZWindowContainer::SetContent(HWND InWindowHandle, HWND InParentWindowHandle)
{
    ContentWindowHandle = InWindowHandle;
    ParentWindowHandle = InParentWindowHandle;

    UE_LOG(LogTemp, Log, TEXT("SetContent called. ContentHandle: %p, ParentHandle: %p"), ContentWindowHandle, ParentWindowHandle);

    if (ContentWindowHandle && ParentWindowHandle)
    {
        // Adjust the DayZ window style
        LONG Style = GetWindowLong(ContentWindowHandle, GWL_STYLE);
        Style &= ~(WS_CAPTION | WS_THICKFRAME | WS_SYSMENU | WS_MINIMIZE | WS_MAXIMIZE);
        Style |= WS_CHILD;
        SetWindowLong(ContentWindowHandle, GWL_STYLE, Style);

        // Set the parent window
        ::SetParent(ContentWindowHandle, ParentWindowHandle);

        // Force a repaint
        ::InvalidateRect(ParentWindowHandle, NULL, 1);
        ::UpdateWindow(ParentWindowHandle);

        UE_LOG(LogTemp, Log, TEXT("DayZ window style adjusted and parent set."));
    }
}

int32 SDayZWindowContainer::OnPaint(const FPaintArgs& Args, const FGeometry& AllottedGeometry, const FSlateRect& MyCullingRect, FSlateWindowElementList& OutDrawElements, int32 LayerId, const FWidgetStyle& InWidgetStyle, bool bParentEnabled) const
{
    UE_LOG(LogTemp, Verbose, TEXT("OnPaint called. ContentHandle: %p, ParentHandle: %p"), ContentWindowHandle, ParentWindowHandle);

    if (ContentWindowHandle && ParentWindowHandle && IsWindow(ContentWindowHandle) && IsWindow(ParentWindowHandle))
    {
        FVector2D Position = AllottedGeometry.GetAbsolutePosition();
        FVector2D Size = AllottedGeometry.GetAbsoluteSize();

        POINT TopLeft = { static_cast<LONG>(Position.X), static_cast<LONG>(Position.Y) };
        ::ClientToScreen(ParentWindowHandle, &TopLeft);
        ::ScreenToClient(ParentWindowHandle, &TopLeft);

        ::SetWindowPos(ContentWindowHandle, HWND_TOP, TopLeft.x, TopLeft.y, Size.X, Size.Y, SWP_SHOWWINDOW);

        UE_LOG(LogTemp, Verbose, TEXT("DayZ window position updated. Pos: (%d, %d), Size: (%f, %f)"),
            TopLeft.x, TopLeft.y, Size.X, Size.Y);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Invalid handles in OnPaint. ContentHandle: %p, ParentHandle: %p"),
            ContentWindowHandle, ParentWindowHandle);
    }

    return SCompoundWidget::OnPaint(Args, AllottedGeometry, MyCullingRect, OutDrawElements, LayerId, InWidgetStyle, bParentEnabled);
}

void SDayZWindowContainer::ClearContent()
{
    UE_LOG(LogTemp, Log, TEXT("ClearContent called"));
    if (ContentWindowHandle && IsWindow(ContentWindowHandle))
    {
        ::SetParent(ContentWindowHandle, NULL);
    }
    ContentWindowHandle = NULL;
    ParentWindowHandle = NULL;
    LogWindowHandleStatus(TEXT("After ClearContent"));
}

void SDayZWindowContainer::LogWindowHandleStatus(const FString& Context) const
{
    UE_LOG(LogTemp, Log, TEXT("%s - ContentWindowHandle: %p, IsWindow: %d"), *Context, ContentWindowHandle, IsWindow(ContentWindowHandle));
    UE_LOG(LogTemp, Log, TEXT("%s - ParentWindowHandle: %p, IsWindow: %d"), *Context, ParentWindowHandle, IsWindow(ParentWindowHandle));
}
