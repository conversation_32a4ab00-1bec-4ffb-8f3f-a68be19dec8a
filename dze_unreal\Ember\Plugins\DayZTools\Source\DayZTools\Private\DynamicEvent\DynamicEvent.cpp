// DynamicEvent.cpp
#include "DynamicEvent/DynamicEvent.h"

UDynamicEvent::UDynamicEvent(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    // Initialize with default values
    bActive = false;
    Position = EEventPosition::Fixed;
    Limit = EEventLimit::Child;

    // Add a default child
    FEventChild DefaultChild;
    // TypeAsset will be null by default, user will need to select a CE Type asset
    Children.Add(DefaultChild);
}

FString UDynamicEvent::ExportToXML() const
{
    FString XMLOutput;

    // Start event tag with name (use asset name)
    XMLOutput += FString::Printf(TEXT("<event name=\"%s\">\n"), *GetName());

    // Add basic properties
    XMLOutput += FString::Printf(TEXT("        <nominal>%d</nominal>\n"), Nominal);
    XMLOutput += FString::Printf(TEXT("        <min>%d</min>\n"), Min);
    XMLOutput += FString::Printf(TEXT("        <max>%d</max>\n"), Max);
    XMLOutput += FString::Printf(TEXT("        <lifetime>%d</lifetime>\n"), Lifetime);
    XMLOutput += FString::Printf(TEXT("        <restock>%d</restock>\n"), Restock);
    XMLOutput += FString::Printf(TEXT("        <saferadius>%d</saferadius>\n"), SafeRadius);
    XMLOutput += FString::Printf(TEXT("        <distanceradius>%d</distanceradius>\n"), DistanceRadius);
    XMLOutput += FString::Printf(TEXT("        <cleanupradius>%d</cleanupradius>\n"), CleanupRadius);

    // Add secondary if it's valid
    if (Secondary.IsValid())
    {
        XMLOutput += FString::Printf(TEXT("        <secondary>%s</secondary>\n"), *Secondary.Get()->GetName());
    }

    // Add flags
    XMLOutput += FString::Printf(TEXT("        <flags deletable=\"%d\" init_random=\"%d\" remove_damaged=\"%d\"/>\n"),
        bDeletable ? 1 : 0,
        bInitRandom ? 1 : 0,
        bRemoveDamaged ? 1 : 0);

    // Add position and limit
    FString PositionStr;
    switch (Position)
    {
        case EEventPosition::Fixed: PositionStr = "fixed"; break;
        case EEventPosition::Player: PositionStr = "player"; break;
        case EEventPosition::Uniform: PositionStr = "uniform"; break;
        default: PositionStr = "fixed";
    }

    FString LimitStr;
    switch (Limit)
    {
        case EEventLimit::Child: LimitStr = "child"; break;
        case EEventLimit::Parent: LimitStr = "parent"; break;
        case EEventLimit::Mixed: LimitStr = "mixed"; break;
        case EEventLimit::Custom: LimitStr = "custom"; break;
        default: LimitStr = "child";
    }

    XMLOutput += FString::Printf(TEXT("        <position>%s</position>\n"), *PositionStr);
    XMLOutput += FString::Printf(TEXT("        <limit>%s</limit>\n"), *LimitStr);
    XMLOutput += FString::Printf(TEXT("        <active>%d</active>\n"), bActive ? 1 : 0);

    // Add children if any, or include an empty children tag if none
    if (Children.Num() > 0)
    {
        XMLOutput += TEXT("        <children>\n");

        for (const FEventChild& Child : Children)
        {
            // Get the type name from the CE Type asset
            FString TypeName = "Unknown";

            if (!Child.TypeAsset.IsNull())
            {
                // Get the type name from the path
                FString TypePath = Child.TypeAsset.ToString();
                TypeName = FPaths::GetBaseFilename(TypePath);

                // Try to load the type asset if possible
                UCEType* TypeAsset = Child.TypeAsset.LoadSynchronous();
                if (TypeAsset)
                {
                    TypeName = TypeAsset->GetName();
                }
            }

            XMLOutput += FString::Printf(TEXT("            <child lootmax=\"%d\" lootmin=\"%d\" max=\"%d\" min=\"%d\" type=\"%s\"/>\n"),
                Child.LootMax,
                Child.LootMin,
                Child.Max,
                Child.Min,
                *TypeName);
        }

        XMLOutput += TEXT("        </children>\n");
    }
    else
    {
        // Include an empty children tag when no children are defined
        XMLOutput += TEXT("        <children/>\n");
    }

    // Close event tag
    XMLOutput += TEXT("    </event>");

    return XMLOutput;
}
