// DynamicEventAssetActions.cpp
#include "DynamicEvent/DynamicEventAssetActions.h"

FDynamicEventAssetActions::FDynamicEventAssetActions(EAssetTypeCategories::Type InAssetCategory)
    : AssetCategory(InAssetCategory)
{
}

FText FDynamicEventAssetActions::GetName() const
{
    return FText::FromString(TEXT("Dynamic Event"));
}

FColor FDynamicEventAssetActions::GetTypeColor() const
{
    // Use a distinctive color for Dynamic Event assets
    return FColor(255, 140, 0); // Orange
}

UClass* FDynamicEventAssetActions::GetSupportedClass() const
{
    return UDynamicEvent::StaticClass();
}

uint32 FDynamicEventAssetActions::GetCategories()
{
    return AssetCategory;
}
