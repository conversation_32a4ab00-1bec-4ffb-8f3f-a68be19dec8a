// DynamicEventCustomization.cpp
#include "DynamicEvent/DynamicEventCustomization.h"
#include "DynamicEvent/DynamicEvent.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Text/STextBlock.h"
#include "DetailWidgetRow.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"
#include "Widgets/Layout/SBox.h"
#include "EditorStyleSet.h"
#include "Misc/MessageDialog.h"

TSharedRef<IDetailCustomization> FDynamicEventCustomization::MakeInstance()
{
    return MakeShareable(new FDynamicEventCustomization);
}

void FDynamicEventCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // Get the currently selected objects
    TArray<TWeakObjectPtr<UObject>> SelectedObjects;
    DetailBuilder.GetObjectsBeingCustomized(SelectedObjects);

    if (SelectedObjects.Num() != 1)
    {
        return;
    }

    DynamicEventAsset = Cast<UDynamicEvent>(SelectedObjects[0].Get());
    if (!DynamicEventAsset.IsValid())
    {
        return;
    }

    // Add a custom category for XML export
    IDetailCategoryBuilder& ExportCategory = DetailBuilder.EditCategory("XML Export");

    // Add a button to export the event to XML
    ExportCategory.AddCustomRow(FText::FromString("Export to XML"))
        .ValueContent()
        [
            SNew(SButton)
            .Text(FText::FromString("Show XML"))
            .ToolTipText(FText::FromString("Exports the event configuration as XML and displays it"))
            .OnClicked(this, &FDynamicEventCustomization::OnExportToXMLClicked)
        ];
}

FReply FDynamicEventCustomization::OnExportToXMLClicked()
{
    if (DynamicEventAsset.IsValid())
    {
        FString XMLContent = DynamicEventAsset->ExportToXML();

        // Show the XML in a dialog
        FText DialogTitle = FText::FromString("XML Export");
        FText DialogContent = FText::FromString(XMLContent);
        FMessageDialog::Open(EAppMsgType::Ok, DialogContent, DialogTitle);
    }

    return FReply::Handled();
}
