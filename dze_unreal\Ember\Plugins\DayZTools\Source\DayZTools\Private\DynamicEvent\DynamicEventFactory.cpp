// DynamicEventFactory.cpp
#include "DynamicEvent/DynamicEventFactory.h"
#include "DynamicEvent/DynamicEvent.h"

UDynamicEventFactory::UDynamicEventFactory()
{
    SupportedClass = UDynamicEvent::StaticClass();
    bCreateNew = true;
    bEditAfterNew = true;
}

UObject* UDynamicEventFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // Create a new Dynamic Event asset
    UDynamicEvent* NewAsset = NewObject<UDynamicEvent>(InParent, Class, Name, Flags);

    // No need to set the event name as we use the asset name directly

    return NewAsset;
}

bool UDynamicEventFactory::ShouldShowInNewMenu() const
{
    return true;
}
