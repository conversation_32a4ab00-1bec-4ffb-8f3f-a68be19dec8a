// DynamicEventSpawner.cpp
#include "DynamicEvent/DynamicEventSpawner.h"
#include "Components/BillboardComponent.h"
#include "Components/BoxComponent.h"
#include "Components/ChildActorComponent.h"
#include "Components/SphereComponent.h"
#include "UObject/ConstructorHelpers.h"
#include "Engine/Texture2D.h"
#include "Engine/StreamableManager.h"
#include "P3DActor.h"
#include "CEType/CEDynamicEventGroup.h" // Ensure header is included

ADynamicEventSpawner::ADynamicEventSpawner()
{
    // Set this actor to not tick
    PrimaryActorTick.bCanEverTick = false;

    // Create root scene component
    RootSceneComponent = CreateDefaultSubobject<USceneComponent>(TEXT("RootSceneComponent"));
    SetRootComponent(RootSceneComponent);

    // Create preview actor component
    PreviewActorComponent = CreateDefaultSubobject<UChildActorComponent>(TEXT("PreviewActorComponent"));
    PreviewActorComponent->SetupAttachment(RootSceneComponent);
    PreviewActorComponent->SetVisibility(false);

    // Create debug sphere component
    DebugSphereComponent = CreateDefaultSubobject<USphereComponent>(TEXT("DebugSphereComponent"));
    DebugSphereComponent->SetupAttachment(RootSceneComponent);
    DebugSphereComponent->SetCollisionEnabled(ECollisionEnabled::NoCollision);
    DebugSphereComponent->SetHiddenInGame(true);
    DebugSphereComponent->SetVisibility(false);
    DebugSphereComponent->ShapeColor = FColor(255, 255, 0); // Yellow
    DebugSphereComponent->bDrawOnlyIfSelected = false;
    DebugSphereComponent->SetSphereRadius(100.0f); // Default radius

    // Create billboard component for easy selection in the editor
    BillboardComponent = CreateDefaultSubobject<UBillboardComponent>(TEXT("BillboardComponent"));
    BillboardComponent->SetupAttachment(RootSceneComponent);

    // Try to load a custom texture for the billboard
    static ConstructorHelpers::FObjectFinder<UTexture2D> BillboardTexture(TEXT("/DayZTools/DynamicEventSpawnerIcon"));
    if (BillboardTexture.Succeeded())
    {
        BillboardComponent->SetSprite(BillboardTexture.Object);
    }
    else
    {
        // Fall back to default actor sprite if custom texture is not found
        static ConstructorHelpers::FObjectFinder<UTexture2D> DefaultTexture(TEXT("/Engine/EditorResources/S_Actor"));
        if (DefaultTexture.Succeeded())
        {
            BillboardComponent->SetSprite(DefaultTexture.Object);
        }
    }

    // Create spawn volume component
    SpawnVolumeComponent = CreateDefaultSubobject<UBoxComponent>(TEXT("SpawnVolumeComponent"));
    SpawnVolumeComponent->SetupAttachment(RootSceneComponent);
    SpawnVolumeComponent->SetBoxExtent(FVector(100.0f, 100.0f, 100.0f));
    SpawnVolumeComponent->SetCollisionEnabled(ECollisionEnabled::QueryOnly);
    SpawnVolumeComponent->SetHiddenInGame(true);
    SpawnVolumeComponent->SetLineThickness(5.0f);
    SpawnVolumeComponent->ShapeColor = FColor(255, 140, 0); // Orange to match Dynamic Event color

    // Initialize properties
    EventGroupAsset = nullptr; // Initialize the new property
    EventAsset = nullptr;
    bShowPreviewAsset = true;
    PreviewActorClass = nullptr;
    DebugRadiusType = EDebugRadiusType::None;
    bSpawnOnTerrain = false;
}

void ADynamicEventSpawner::OnConstruction(const FTransform& Transform)
{
    Super::OnConstruction(Transform);

    // Update the actor label based on the event asset
    UpdateActorLabel();

    // Update the preview actor when the actor is constructed
    UpdatePreviewActor();

    // Update debug visualization
    UpdateDebugVisualization();
}

void ADynamicEventSpawner::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

    // Get the name of the property that was changed
    FName PropertyName = (PropertyChangedEvent.Property != nullptr) ? PropertyChangedEvent.Property->GetFName() : NAME_None;

    // If the preview actor class or show preview flag changed, update the preview mesh
    if (PropertyName == GET_MEMBER_NAME_CHECKED(ADynamicEventSpawner, PreviewActorClass) ||
        PropertyName == GET_MEMBER_NAME_CHECKED(ADynamicEventSpawner, bShowPreviewAsset))
    {
        UpdatePreviewActor();
    }

    // If the event asset changed, update the actor label and debug visualization
    if (PropertyName == GET_MEMBER_NAME_CHECKED(ADynamicEventSpawner, EventAsset))
    {
        UE_LOG(LogTemp, Warning, TEXT("Event Asset changed: %s"), *EventAsset.ToSoftObjectPath().ToString());

        // Update the actor label based on the new event asset
        UpdateActorLabel();

        // If we have a valid asset path and a non-None debug radius type, try to preload the asset
        if (!EventAsset.ToSoftObjectPath().ToString().IsEmpty() && DebugRadiusType != EDebugRadiusType::None)
        {
            UE_LOG(LogTemp, Warning, TEXT("Preloading asset: %s"), *EventAsset.ToSoftObjectPath().ToString());
            FSoftObjectPath AssetPath = EventAsset.ToSoftObjectPath();
            AssetPath.TryLoad();
        }

        // Add a small delay to ensure the asset is properly loaded
        FTimerHandle TimerHandle;
        GetWorld()->GetTimerManager().SetTimer(TimerHandle, this, &ADynamicEventSpawner::UpdateDebugVisualization, 0.5f, false);
    }
    // If only the debug radius type changed, just update the visualization
    else if (PropertyName == GET_MEMBER_NAME_CHECKED(ADynamicEventSpawner, DebugRadiusType))
    {
        UE_LOG(LogTemp, Warning, TEXT("Debug Radius Type changed: %d"), (int32)DebugRadiusType);

        // Add a small delay to ensure the asset is properly loaded
        FTimerHandle TimerHandle;
        GetWorld()->GetTimerManager().SetTimer(TimerHandle, this, &ADynamicEventSpawner::UpdateDebugVisualization, 0.5f, false);
    }
}

void ADynamicEventSpawner::UpdatePreviewActor()
{
#if WITH_EDITOR
    UE_LOG(LogTemp, Warning, TEXT("UpdatePreviewActor called. bShowPreviewAsset: %d, PreviewActorClass valid: %d"),
        bShowPreviewAsset, PreviewActorClass != nullptr);

    // If we should show the preview and have a valid preview actor class
    if (bShowPreviewAsset && PreviewActorClass)
    {
        UE_LOG(LogTemp, Warning, TEXT("Setting child actor class to: %s"), *PreviewActorClass->GetName());

        // Set the child actor class and make it visible
        PreviewActorComponent->SetChildActorClass(PreviewActorClass);
        PreviewActorComponent->SetVisibility(true);

        // Hide the box component when showing the preview actor
        SpawnVolumeComponent->SetVisibility(false);
        BillboardComponent->SetVisibility(true);
    }
    else
    {
        // If we shouldn't show the preview or don't have a valid preview actor class
        if (!bShowPreviewAsset)
        {
            UE_LOG(LogTemp, Warning, TEXT("Preview disabled by user"));
        }
        else if (!PreviewActorClass)
        {
            UE_LOG(LogTemp, Warning, TEXT("No valid preview actor class selected"));
        }

        // Clear the child actor class and hide it
        PreviewActorComponent->SetChildActorClass(nullptr);
        PreviewActorComponent->SetVisibility(false);

        // Show the box component when not showing the preview actor
        SpawnVolumeComponent->SetVisibility(true);
        BillboardComponent->SetVisibility(true);
    }
#endif
}

void ADynamicEventSpawner::UpdateActorLabel()
{
#if WITH_EDITOR
    // If we don't have a valid event asset path, use the default name
    if (EventAsset.ToSoftObjectPath().ToString().IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("No Event Asset selected, using default actor label"));
        return;
    }

    // Extract the asset name from the path
    FSoftObjectPath AssetPath = EventAsset.ToSoftObjectPath();
    FString AssetPathStr = AssetPath.ToString();

    // Find the last part of the path (the asset name)
    FString AssetName;
    if (AssetPathStr.Split(TEXT("/"), nullptr, &AssetName, ESearchCase::IgnoreCase, ESearchDir::FromEnd))
    {
        // Remove the extension if present
        AssetName.Split(TEXT("."), &AssetName, nullptr, ESearchCase::IgnoreCase, ESearchDir::FromEnd);

        // Create the new actor label
        FString NewLabel = FString::Printf(TEXT("DynamicEventSpawner_%s"), *AssetName);

        // Set the actor label
        UE_LOG(LogTemp, Warning, TEXT("Renaming actor to: %s"), *NewLabel);
        SetActorLabel(NewLabel);
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to extract asset name from path: %s"), *AssetPathStr);
    }
#endif
}

void ADynamicEventSpawner::UpdateDebugVisualization()
{
#if WITH_EDITOR
    // Hide the debug sphere by default
    DebugSphereComponent->SetVisibility(false);

    // If debug radius type is None, don't show anything
    if (DebugRadiusType == EDebugRadiusType::None)
    {
        UE_LOG(LogTemp, Warning, TEXT("Debug radius type is None, not showing any visualization"));
        return;
    }

    // Log the current state
    UE_LOG(LogTemp, Warning, TEXT("UpdateDebugVisualization called. Debug type: %d, EventAsset path: %s"),
        (int32)DebugRadiusType, *EventAsset.ToSoftObjectPath().ToString());

    // Check if we have a valid event asset path
    if (EventAsset.ToSoftObjectPath().ToString().IsEmpty())
    {
        UE_LOG(LogTemp, Warning, TEXT("Cannot show debug radius: No Event Asset path specified"));
        return;
    }

    // Try to load the asset directly from the path
    FSoftObjectPath AssetPath = EventAsset.ToSoftObjectPath();
    UDynamicEvent* DynamicEvent = Cast<UDynamicEvent>(AssetPath.TryLoad());

    if (!DynamicEvent)
    {
        // Try an alternative loading method
        UE_LOG(LogTemp, Warning, TEXT("First load attempt failed, trying StreamableManager..."));

        static FStreamableManager StreamableManager;
        DynamicEvent = Cast<UDynamicEvent>(StreamableManager.LoadSynchronous(AssetPath));

        if (!DynamicEvent)
        {
            UE_LOG(LogTemp, Warning, TEXT("Cannot show debug radius: Failed to load Event Asset from path: %s"),
                *AssetPath.ToString());
            return;
        }
    }

    UE_LOG(LogTemp, Warning, TEXT("Successfully loaded Event Asset: %s"), *DynamicEvent->GetName());

    // Log the available radius values for debugging
    UE_LOG(LogTemp, Warning, TEXT("Event Asset radius values - Safe: %d, Distance: %d, Cleanup: %d"),
        DynamicEvent->SafeRadius, DynamicEvent->DistanceRadius, DynamicEvent->CleanupRadius);

    // Get the appropriate radius based on the selected debug type
    float Radius = 0.0f;
    FColor SphereColor;
    FString RadiusTypeName;

    switch (DebugRadiusType)
    {
        case EDebugRadiusType::SafeRadius:
            Radius = DynamicEvent->SafeRadius;
            SphereColor = FColor(0, 255, 0); // Green
            RadiusTypeName = TEXT("Safe Radius");
            break;

        case EDebugRadiusType::DistanceRadius:
            Radius = DynamicEvent->DistanceRadius;
            SphereColor = FColor(255, 255, 0); // Yellow
            RadiusTypeName = TEXT("Distance Radius");
            break;

        case EDebugRadiusType::CleanupRadius:
            Radius = DynamicEvent->CleanupRadius;
            SphereColor = FColor(255, 0, 0); // Red
            RadiusTypeName = TEXT("Cleanup Radius");
            break;

        default:
            UE_LOG(LogTemp, Warning, TEXT("Unknown debug radius type: %d"), (int32)DebugRadiusType);
            return;
    }

    UE_LOG(LogTemp, Warning, TEXT("Selected radius type: %s, Value: %d"), *RadiusTypeName, (int32)Radius);

    // Set the sphere radius and color
    if (Radius > 0.0f)
    {
        // Convert radius from meters to Unreal units (1 meter = 100 Unreal units)
        float UnrealRadius = Radius * 100.0f;

        UE_LOG(LogTemp, Warning, TEXT("Setting sphere radius to %d Unreal units (%d meters)"), (int32)UnrealRadius, (int32)Radius);

        // Check if the sphere component is valid
        if (!DebugSphereComponent)
        {
            UE_LOG(LogTemp, Error, TEXT("Debug sphere component is null!"));
            return;
        }

        // Set up the sphere component
        DebugSphereComponent->SetSphereRadius(UnrealRadius);
        DebugSphereComponent->ShapeColor = SphereColor;
        DebugSphereComponent->SetVisibility(true);

        UE_LOG(LogTemp, Warning, TEXT("Showing debug radius: %s = %d meters"), *RadiusTypeName, (int32)Radius);

        // Force a redraw of the component
        DebugSphereComponent->MarkRenderStateDirty();
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Cannot show debug radius: %s is zero or negative (%d)"),
            *RadiusTypeName, (int32)Radius);
    }
#endif
}
