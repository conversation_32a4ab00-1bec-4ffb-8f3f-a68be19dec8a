// DynamicEventSpawnerCustomization.cpp
#include "DynamicEvent/DynamicEventSpawnerCustomization.h"
#include "DynamicEvent/DynamicEventSpawner.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"

TSharedRef<IDetailCustomization> FDynamicEventSpawnerCustomization::MakeInstance()
{
    return MakeShareable(new FDynamicEventSpawnerCustomization);
}

void FDynamicEventSpawnerCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    // Get the object being customized
    TArray<TWeakObjectPtr<UObject>> Objects;
    DetailBuilder.GetObjectsBeingCustomized(Objects);
    
    if (Objects.Num() != 1)
    {
        return;
    }
    
    // Hide all categories except Transform and Dynamic Event
    DetailBuilder.HideCategory("Actor");
    DetailBuilder.HideCategory("Rendering");
    DetailBuilder.HideCategory("Input");
    DetailBuilder.HideCategory("Replication");
    DetailBuilder.HideCategory("Collision");
    DetailBuilder.HideCategory("Tags");
    DetailBuilder.HideCategory("ComponentReplication");
    DetailBuilder.HideCategory("Variable");
    DetailBuilder.HideCategory("Tick");
    DetailBuilder.HideCategory("ComponentTick");
    DetailBuilder.HideCategory("AssetUserData");
    DetailBuilder.HideCategory("Activation");
    DetailBuilder.HideCategory("Cooking");
    DetailBuilder.HideCategory("Hidden");
    DetailBuilder.HideCategory("Components");
    DetailBuilder.HideCategory("Sockets");
    DetailBuilder.HideCategory("Events");
    DetailBuilder.HideCategory("Lighting");
    DetailBuilder.HideCategory("LOD");
    DetailBuilder.HideCategory("Mobile");
    DetailBuilder.HideCategory("Navigation");
    DetailBuilder.HideCategory("Physics");
    DetailBuilder.HideCategory("VirtualTexture");
    DetailBuilder.HideCategory("TextureStreaming");
    DetailBuilder.HideCategory("Materials");
    DetailBuilder.HideCategory("Sprite");
    DetailBuilder.HideCategory("Box");
    DetailBuilder.HideCategory("ChildActorComponent");
    DetailBuilder.HideCategory("Scene");
    DetailBuilder.HideCategory("SceneComponent");
    DetailBuilder.HideCategory("HLOD");
    DetailBuilder.HideCategory("WorldPartition");
    DetailBuilder.HideCategory("DataLayers");
    DetailBuilder.HideCategory("Networking");
    DetailBuilder.HideCategory("Mobility");
    DetailBuilder.HideCategory("RayTracing");
    DetailBuilder.HideCategory("Pooling");
    DetailBuilder.HideCategory("UserInterface");
    DetailBuilder.HideCategory("Transformation");
    DetailBuilder.HideCategory("DeveloperSettings");
    DetailBuilder.HideCategory("EditorOnly");
    
    // Make sure the Transform and Dynamic Event categories are visible
    DetailBuilder.EditCategory("Transform");
    DetailBuilder.EditCategory("Dynamic Event");
}
