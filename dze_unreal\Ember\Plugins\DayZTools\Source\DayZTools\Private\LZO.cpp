#include "LZO.h"
#include <cassert>
#include <cstring>
#include <stdexcept>

static const uint32_t M2_MAX_OFFSET = 0x0800;

uint32_t FLZO::Decompress(const uint8_t* Input, uint8_t* Output, uint32_t ExpectedSize)
{
    const uint8_t* Op_End = Output + ExpectedSize;
    uint8_t* Op = Output;
    const uint8_t* Ip = Input;

    uint32_t T;
    const uint8_t* M_Pos;

    if (*Ip > 17)
    {
        T = *Ip++ - 17;
        if (T < 4) goto match_next;
        assert(T > 0);
        if ((Op_End - Op) < static_cast<ptrdiff_t>(T)) { throw std::runtime_error("Output Overrun"); }
        do *Op++ = *Ip++; while (--T > 0);
        goto first_literal_run;
    }

B_3:
    T = *Ip++;
    if (T >= 16) goto match;
    if (T == 0)
    {
        while (*Ip == 0)
        {
            T += 255;
            Ip++;
        }
        T += 15 + *Ip++;
    }
    assert(T > 0);
    if ((Op_End - Op) < static_cast<ptrdiff_t>(T + 3)) { throw std::runtime_error("Output Overrun"); }

    *Op++ = *Ip++;
    *Op++ = *Ip++;
    *Op++ = *Ip++;
    *Op++ = *Ip++;
    if (--T > 0)
    {
        if (T >= 4)
        {
            do
            {
                *Op++ = *Ip++;
                *Op++ = *Ip++;
                *Op++ = *Ip++;
                *Op++ = *Ip++;
                T -= 4;
            } while (T >= 4);
            if (T > 0) do *Op++ = *Ip++; while (--T > 0);
        }
        else
            do *Op++ = *Ip++; while (--T > 0);
    }

first_literal_run:
    T = *Ip++;
    if (T >= 16) goto match;

    M_Pos = Op - (1 + M2_MAX_OFFSET);
    M_Pos -= T >> 2;
    M_Pos -= *Ip++ << 2;

    if (M_Pos < Output || M_Pos >= Op) { throw std::runtime_error("Lookbehind Overrun"); }
    if ((Op_End - Op) < 3) { throw std::runtime_error("Output Overrun"); }
    *Op++ = *M_Pos++;
    *Op++ = *M_Pos++;
    *Op++ = *M_Pos;

    goto match_done;

match:
    if (T >= 64)
    {
        M_Pos = Op - 1;
        M_Pos -= (T >> 2) & 7;
        M_Pos -= *Ip++ << 3;
        T = (T >> 5) - 1;
        if (M_Pos < Output || M_Pos >= Op) { throw std::runtime_error("Lookbehind Overrun"); }
        assert(T > 0);
        if ((Op_End - Op) < static_cast<ptrdiff_t>(T + 2)) { throw std::runtime_error("Output Overrun"); }
        goto copy_match;
    }
    else if (T >= 32)
    {
        T &= 31;
        if (T == 0)
        {
            while (*Ip == 0)
            {
                T += 255;
                Ip++;
            }
            T += 31 + *Ip++;
        }

        M_Pos = Op - 1;
        M_Pos -= (Ip[0] >> 2) + (Ip[1] << 6);

        Ip += 2;
    }
    else if (T >= 16)
    {
        M_Pos = Op;
        M_Pos -= (T & 8) << 11;

        T &= 7;
        if (T == 0)
        {
            while (*Ip == 0)
            {
                T += 255;
                Ip++;
            }
            T += 7 + *Ip++;
        }

        M_Pos -= (Ip[0] >> 2) + (Ip[1] << 6);

        Ip += 2;

        if (M_Pos == Op)
        {
            uint32_t Val = static_cast<uint32_t>(Op - Output);
            assert(T == 1);
            if (M_Pos != Op_End) { throw std::runtime_error("Output Underrun"); }
            return static_cast<uint32_t>(Ip - Input);
        }
        M_Pos -= 0x4000;
    }
    else
    {
        M_Pos = Op - 1;
        M_Pos -= T >> 2;
        M_Pos -= *Ip++ << 2;

        if (M_Pos < Output || M_Pos >= Op) { throw std::runtime_error("Lookbehind Overrun"); }
        if ((Op_End - Op) < 2) { throw std::runtime_error("Output Overrun"); }
        *Op++ = *M_Pos++;
        *Op++ = *M_Pos;
        goto match_done;
    }

    if (M_Pos < Output || M_Pos >= Op) { throw std::runtime_error("Lookbehind Overrun"); }
    assert(T > 0);
    if ((Op_End - Op) < static_cast<ptrdiff_t>(T + 2)) { throw std::runtime_error("Output Overrun"); }

    if (T >= 2 * 4 - (3 - 1) && (Op - M_Pos) >= 4)
    {
        std::memcpy(Op, M_Pos, 4);
        Op += 4;
        M_Pos += 4;
        T -= 4 - (3 - 1);
        do
        {
            std::memcpy(Op, M_Pos, 4);
            Op += 4;
            M_Pos += 4;
            T -= 4;
        } while (T >= 4);
        if (T > 0) do *Op++ = *M_Pos++; while (--T > 0);
        goto match_done;
    }

copy_match:
    *Op++ = *M_Pos++;
    *Op++ = *M_Pos++;
    do *Op++ = *M_Pos++; while (--T > 0);

match_done:
    T = Ip[-2] & 3;
    if (T == 0) goto B_3;

match_next:
    assert(T > 0 && T < 4);
    if ((Op_End - Op) < static_cast<ptrdiff_t>(T)) { throw std::runtime_error("Output Overrun"); }

    *Op++ = *Ip++;
    if (T > 1) { *Op++ = *Ip++; if (T > 2) { *Op++ = *Ip++; } }

    T = *Ip++;
    goto match;
}

uint32_t FLZO::Decompress(const std::vector<uint8_t>& Input, std::vector<uint8_t>& Dst, uint32_t ExpectedSize)
{
    Dst.resize(ExpectedSize);
    return Decompress(Input.data(), Dst.data(), ExpectedSize);
}

std::vector<uint8_t> FLZO::ReadLZO(const std::vector<uint8_t>& Input, uint32_t ExpectedSize)
{
    std::vector<uint8_t> Dst(ExpectedSize);
    Decompress(Input.data(), Dst.data(), ExpectedSize);
    return Dst;
}

void FLZO::CopyMatch(uint8_t*& Op, const uint8_t*& M_Pos, uint32_t& T)
{
    *Op++ = *M_Pos++;
    *Op++ = *M_Pos++;
    do *Op++ = *M_Pos++; while (--T > 0);
}
