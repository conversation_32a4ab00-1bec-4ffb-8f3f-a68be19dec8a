#include "LZSS.h"
#include <vector>
#include <string>
#include <algorithm>
#include <stdexcept>
#include <cstring>
#include <iostream>

uint32_t FLZSS::ReadLZSS(const std::vector<uint8_t>& Input, std::vector<uint8_t>& Dst, uint32_t ExpectedSize, bool bUseSignedChecksum)
{
    const int32_t N = 4096;
    const int32_t F = 18;
    const int32_t THRESHOLD = 2;
    std::vector<char> TextBuf(N + F - 1, ' '); // Initialize with ' ' as in the C# code
    Dst.resize(ExpectedSize);

    if (ExpectedSize <= 0)
    {
        return 0;
    }

    size_t Position = 0;
    uint32_t BytesLeft = ExpectedSize;
    int32_t IDst = 0;
    int32_t I, J, R, C;
    int32_t CSum = 0;
    int32_t Flags = 0;

    R = N - F;

    auto ReadByte = [&Input, &Position]() -> uint8_t {
        if (Position >= Input.size()) {
            throw std::runtime_error("Unexpected end of input");
        }
        return Input[Position++];
        };

    while (BytesLeft > 0)
    {
        if (((Flags >>= 1) & 256) == 0)
        {
            C = ReadByte();
            Flags = C | 0xFF00;
        }

        if ((Flags & 1) != 0)
        {
            C = ReadByte();
            if (bUseSignedChecksum)
            {
                CSum += static_cast<int8_t>(C);
            }
            else
            {
                CSum += static_cast<uint8_t>(C);
            }
            Dst[IDst++] = static_cast<uint8_t>(C);
            BytesLeft--;
            TextBuf[R] = static_cast<char>(C);
            R = (R + 1) & (N - 1);
        }
        else
        {
            I = ReadByte();
            J = ReadByte();
            I |= (J & 0xF0) << 4;
            J = (J & 0x0F) + THRESHOLD;

            int32_t II = R - I;
            int32_t JJ = II + J;

            if (static_cast<uint32_t>(J + 1) > BytesLeft)
            {
                throw std::runtime_error("LZSS overflow");
            }

            for (; II <= JJ; ++II)
            {
                C = static_cast<uint8_t>(TextBuf[II & (N - 1)]);
                if (bUseSignedChecksum)
                {
                    CSum += static_cast<int8_t>(C);
                }
                else
                {
                    CSum += static_cast<uint8_t>(C);
                }
                Dst[IDst++] = static_cast<uint8_t>(C);
                BytesLeft--;
                TextBuf[R] = static_cast<char>(C);
                R = (R + 1) & (N - 1);
            }
        }
    }

    std::vector<uint8_t> CSData(4);
    if (Position + 4 > Input.size()) {
        throw std::runtime_error("Not enough data for checksum");
    }
    std::memcpy(CSData.data(), &Input[Position], 4);
    int32_t CSR = *reinterpret_cast<int32_t*>(CSData.data());
    if (CSR != CSum)
    {
        throw std::runtime_error("Checksum mismatch");
    }

    return static_cast<uint32_t>(Position + 4);
}
