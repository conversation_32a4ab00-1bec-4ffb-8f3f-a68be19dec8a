#include "LevelEditorToolbar.h"
#include "Modules/ModuleManager.h"
#include "LevelEditor.h"
#include "DayZToolsStyle.h"       // For icons (DayZTools.Workbench, etc.)
#include "Interfaces/IPluginManager.h"

// Add Asset Registry module include
#include "AssetRegistry/AssetRegistryModule.h"
#include "CEType/CEType.h"
#include "CEType/CEServerMessages.h"
#include "CELootPoints/CELootPoints.h"
#include "CEType/CEDynamicEventGroup.h"

#include "Widgets/SBoxPanel.h"
#include "Widgets/Text/STextBlock.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "ToolMenus.h"
#include "Windows/AllowWindowsPlatformTypes.h"
#include <tlhelp32.h>
#include "Windows/HideWindowsPlatformTypes.h"

// For reading/writing files
#include "Misc/FileHelper.h"
#include "Misc/Paths.h"
#include "GenericPlatform/GenericPlatformProcess.h"
#include "Editor/UnrealEd/Public/EditorViewportClient.h"
#include "LevelEditorViewport.h"
#include "SLevelViewport.h"
#include "PropertyEditorModule.h"
#include "ISettingsModule.h"
#include "DayZToolsSettings.h"
#include "Misc/MessageDialog.h"

#include "HAL/PlatformFileManager.h"
#include "AssetToolsModule.h"
#include "Framework/Commands/UICommandList.h"

#include "Engine/World.h"
#include "Editor.h"  // For GEditor, GEditor->GetEditorWorldContext()
#include "Kismet/GameplayStatics.h"
#include "DynamicEvent/DynamicEventSpawner.h"
#include "DynamicEvent/DynamicEvent.h"
#include "Misc/MessageDialog.h"

#include "Landscape.h"
#include "LandscapeInfo.h"
#include "LandscapeComponent.h"

// If you need your custom WRP includes:
#include "WRPUtils.h"
#include "BinaryReader.h"

#define LOCTEXT_NAMESPACE "LevelEditorToolbar"

//----------------------------------------------
// Static class-member definitions
//----------------------------------------------
TSharedPtr<FUICommandList>  FLevelEditorToolbar::PluginCommands = nullptr;
TSharedPtr<FExtender>       FLevelEditorToolbar::LevelEditorExtender = nullptr;
FLevelEditorToolbar::EDayZPlayMode FLevelEditorToolbar::CurrentPlayMode = FLevelEditorToolbar::EDayZPlayMode::Offline;
TArray<FLevelEditorToolbar::FDirectoryItem> FLevelEditorToolbar::DirectoryList = {};

//----------------------------------------------
//  Initialize / Shutdown
//----------------------------------------------
void FLevelEditorToolbar::Initialize()
{
	// We can create our command list, or get it from somewhere else
	// but typically you already have a "PluginCommands" if you have custom commands.
	PluginCommands = MakeShareable(new FUICommandList);

	// If you want to load a previously saved directory list:
	LoadDirectoryList();

	// Actually register with the Level Editor
	RegisterToolbarExtension();
}

void FLevelEditorToolbar::Shutdown()
{
	UnregisterToolbarExtension();
	PluginCommands.Reset();
	LevelEditorExtender.Reset();
	DirectoryList.Empty();
}

//----------------------------------------------
//  Register / Unregister the Extender
//----------------------------------------------
void FLevelEditorToolbar::RegisterToolbarExtension()
{
	FLevelEditorModule& LevelEditorModule =
		FModuleManager::LoadModuleChecked<FLevelEditorModule>("LevelEditor");

	TSharedPtr<FExtensibilityManager> ToolbarExtMgr = LevelEditorModule.GetToolBarExtensibilityManager();
	if (!ToolbarExtMgr.IsValid())
	{
		return;
	}

	// Create an Extender
	LevelEditorExtender = GetToolbarExtender();
	ToolbarExtMgr->AddExtender(LevelEditorExtender);
}

void FLevelEditorToolbar::UnregisterToolbarExtension()
{
	if (LevelEditorExtender.IsValid())
	{
		FLevelEditorModule& LevelEditorModule =
			FModuleManager::LoadModuleChecked<FLevelEditorModule>("LevelEditor");

		TSharedPtr<FExtensibilityManager> ToolbarExtMgr = LevelEditorModule.GetToolBarExtensibilityManager();
		if (ToolbarExtMgr.IsValid())
		{
			ToolbarExtMgr->RemoveExtender(LevelEditorExtender);
		}
	}
}

TSharedRef<FExtender> FLevelEditorToolbar::GetToolbarExtender()
{
	TSharedRef<FExtender> Extender = MakeShareable(new FExtender);

	// Insert after the "Play" section
	Extender->AddToolBarExtension(
		"Play",
		EExtensionHook::After,
		PluginCommands,
		FToolBarExtensionDelegate::CreateStatic(&FLevelEditorToolbar::AddToolbarExtension)
	);

	return Extender;
}

//----------------------------------------------
//  Building the actual toolbar row
//----------------------------------------------
void FLevelEditorToolbar::AddToolbarExtension(FToolBarBuilder& ToolbarBuilder)
{
	// A separator for clarity
	ToolbarBuilder.AddSeparator();

	ToolbarBuilder.SetLabelVisibility(EVisibility::Visible);

	// Example "Workbench" button
	ToolbarBuilder.AddToolBarButton(
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnLaunchWorkbench)),
		NAME_None,
		FText::GetEmpty(),
		NSLOCTEXT("DayZTools", "WorkbenchTooltip", "Launch Workbench"),
		FSlateIcon(FDayZToolsStyle::GetStyleSetName(), "DayZTools.Workbench")
	);

	ToolbarBuilder.AddToolBarButton(
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnLaunchObjectBuilder)),
		NAME_None,
		FText::GetEmpty(),
		NSLOCTEXT("DayZTools", "ObjectBuilderTooltip", "Launch Object Builder"),
		FSlateIcon(FDayZToolsStyle::GetStyleSetName(), "DayZTools.ObjectBuilder")
	);

	ToolbarBuilder.AddToolBarButton(
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnLaunchTerrainBuilder)),
		NAME_None,
		FText::GetEmpty(),
		NSLOCTEXT("DayZTools", "TerrainBuilderTooltip", "Launch Terrain Builder"),
		FSlateIcon(FDayZToolsStyle::GetStyleSetName(), "DayZTools.TerrainBuilder")
	);

	ToolbarBuilder.AddSeparator();

	// Build PBO button group
	ToolbarBuilder.BeginStyleOverride("Toolbar.BackplateLeftPlay");
	ToolbarBuilder.AddToolBarButton(
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnBuildPBO)),
		NAME_None,
		FText::GetEmpty(),
		LOCTEXT("BuildPBOTooltip", "Build PBO"),
		FSlateIcon(FDayZToolsStyle::GetStyleSetName(), "DayZTools.BuildPBO")
	);
	ToolbarBuilder.EndStyleOverride();

	ToolbarBuilder.BeginStyleOverride("Toolbar.BackplateRightCombo");
	ToolbarBuilder.AddComboButton(
		FUIAction(),
		FOnGetContent::CreateStatic(&FLevelEditorToolbar::GenerateBuildPBOMenu),
		FText::GetEmpty(),
		LOCTEXT("BuildPBOSettingsTooltip", "Build PBO Options"),
		FSlateIcon(FAppStyle::GetAppStyleSetName(), "ToolBar.ExpandArrow")
	);
	ToolbarBuilder.EndStyleOverride();

	ToolbarBuilder.AddSeparator();

	// Play DayZ button group
	ToolbarBuilder.BeginStyleOverride("Toolbar.BackplateLeftPlay");
	ToolbarBuilder.AddToolBarButton(
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnPlayButtonClicked)),
		NAME_None,
		FText::GetEmpty(),
		LOCTEXT("PlayButtonTooltip", "Play DayZ"),
		FSlateIcon(FAppStyle::GetAppStyleSetName(), "PlayWorld.PlayInViewport")
	);
	ToolbarBuilder.EndStyleOverride();

	ToolbarBuilder.BeginStyleOverride("Toolbar.BackplateRightCombo");
	ToolbarBuilder.AddComboButton(
		FUIAction(),
		FOnGetContent::CreateStatic(&FLevelEditorToolbar::GenerateLaunchDayZMenu),
		FText::GetEmpty(),
		LOCTEXT("LaunchDayZTooltip", "Play Options"),
		FSlateIcon(FAppStyle::GetAppStyleSetName(), "ToolBar.ExpandArrow")
	);
	ToolbarBuilder.EndStyleOverride();

	ToolbarBuilder.AddSeparator();

	// ExportTools button - standard UE5 dropdown button style with visible label and icon
	ToolbarBuilder.AddComboButton(
		FUIAction(),
		FOnGetContent::CreateStatic(&FLevelEditorToolbar::GenerateExportToolsMenu),
		LOCTEXT("ExportToolsLabel", "Export"),
		LOCTEXT("ExportToolsTooltip", "Export Tools for Central Economy and Dynamic Events"),
		FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Save"), false);

	ToolbarBuilder.AddSeparator();

	// Example WRP test
	ToolbarBuilder.AddToolBarButton(
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::TestLoadWRPFile)),
		NAME_None,
		FText::FromString(TEXT("WRP")),
		NSLOCTEXT("DayZTools", "TerrainBuilderTooltip", "Test reading WRP file"),
		FSlateIcon()
	);
}

//----------------------------------------------
//  Submenu Builders (Build PBO, Launch DayZ, etc.)
//----------------------------------------------

TSharedRef<SWidget> FLevelEditorToolbar::GenerateBuildPBOMenu()
{
	FMenuBuilder MenuBuilder(/*bInShouldCloseWindowAfterMenuSelection=*/true, PluginCommands);

	// "Refresh Directories" button
	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "RefreshDirectories", "Refresh Directories"),
		NSLOCTEXT("DayZTools", "RefreshDirectories_Tooltip", "Refresh the list of directories to build"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnRefreshDirectoriesClicked))
	);

	MenuBuilder.AddSeparator();

	// Add checkboxes for directories
	for (int32 Index = 0; Index < DirectoryList.Num(); ++Index)
	{
		const FDirectoryItem& Item = DirectoryList[Index];

		MenuBuilder.AddWidget(
			SNew(SHorizontalBox)
			+ SHorizontalBox::Slot()
			.AutoWidth()
			[
				SNew(SCheckBox)
					.OnCheckStateChanged_Lambda([Index](ECheckBoxState NewState) {
					OnDirectoryCheckboxChanged(NewState, Index);
						})
					.IsChecked_Lambda([Index]() {
					return GetDirectoryCheckboxState(Index);
						})
			]
			+ SHorizontalBox::Slot()
			.FillWidth(1.0f)
			.VAlign(VAlign_Center)
			[
				SNew(STextBlock).Text(FText::FromString(Item.Directory))
			],
			FText::GetEmpty(),
			true /*bNoIndent*/
		);
	}

	return MenuBuilder.MakeWidget();
}

TSharedRef<SWidget> FLevelEditorToolbar::GenerateLaunchDayZMenu()
{
	FMenuBuilder MenuBuilder(true, PluginCommands);

	// "Close All Instances"
	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "CloseAllInstances", "Close All Instances"),
		NSLOCTEXT("DayZTools", "CloseAllInstances_Tooltip", "Closes all instances of DayZ/DayZServer"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateLambda([]() {
			// Call the bool-returning function and discard the result
			(void)FLevelEditorToolbar::ExitDayZInstances();
			}))
	);

	MenuBuilder.AddSeparator();

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "PlayDayZOffline", "DayZ Offline"),
		NSLOCTEXT("DayZTools", "PlayDayZOffline_Tooltip", "Play DayZ in offline mode"),
		FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.Stop"),
		FUIAction(
			FExecuteAction::CreateLambda([]()
				{
					CurrentPlayMode = EDayZPlayMode::Offline;
				}),
			FCanExecuteAction(),
			FIsActionChecked::CreateLambda([]()
				{
					return (CurrentPlayMode == EDayZPlayMode::Offline);
				})
		),
		NAME_None,
		EUserInterfaceActionType::RadioButton
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "PlayDayZMultiplayer", "DayZ Multiplayer"),
		NSLOCTEXT("DayZTools", "PlayDayZMultiplayer_Tooltip", "Play DayZ in multiplayer mode"),
		FSlateIcon(FAppStyle::GetAppStyleSetName(), "Icons.World"),
		FUIAction(
			FExecuteAction::CreateLambda([]()
				{
					CurrentPlayMode = EDayZPlayMode::Multiplayer;
				}),
			FCanExecuteAction(),
			FIsActionChecked::CreateLambda([]()
				{
					return (CurrentPlayMode == EDayZPlayMode::Multiplayer);
				})
		),
		NAME_None,
		EUserInterfaceActionType::RadioButton
	);

	return MenuBuilder.MakeWidget();
}

//----------------------------------------------
//  Button handlers
//----------------------------------------------
void FLevelEditorToolbar::OnLaunchWorkbench()
{
	UE_LOG(LogTemp, Log, TEXT("Launching Workbench..."));
	// ...
}

void FLevelEditorToolbar::OnLaunchObjectBuilder()
{
	UE_LOG(LogTemp, Log, TEXT("Launching Object Builder..."));
	// ...
}

void FLevelEditorToolbar::OnLaunchTerrainBuilder()
{
	UE_LOG(LogTemp, Log, TEXT("Launching Terrain Builder..."));
	// ...
}

void FLevelEditorToolbar::OnBuildPBO()
{
	UE_LOG(LogTemp, Log, TEXT("Building PBO..."));
	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	FString ProjectTrunkPath = Settings->ProjectTrunkPath.Replace(TEXT("/"), TEXT("\\"));
	ProjectTrunkPath.RemoveFromEnd(TEXT("\\"));
	FString ProjectCfgPath = FPaths::Combine(ProjectTrunkPath, TEXT("project.cfg"));
	FString UserCfgPath = FPaths::Combine(ProjectTrunkPath, TEXT("user.cfg"));

	if (!FPaths::FileExists(ProjectCfgPath) || !FPaths::FileExists(UserCfgPath))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find project.cfg or user.cfg file, exiting."));
		return;
	}

	FString WorkDrive, ModName, ModBuildDirectory, KeyDirectory, KeyName, PrefixLinkRoot;

	// Extract data from config files
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("WorkDrive"), WorkDrive);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ModName"), ModName);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ModBuildDirectory"), ModBuildDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("KeyDirectory"), KeyDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("KeyName"), KeyName);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("prefixLinkRoot"), PrefixLinkRoot);

	// Normalize paths
	WorkDrive = WorkDrive.Replace(TEXT("/"), TEXT("\\"));
	WorkDrive.RemoveFromEnd(TEXT("\\"));
	ModBuildDirectory = ModBuildDirectory.Replace(TEXT("/"), TEXT("\\"));
	ModBuildDirectory.RemoveFromEnd(TEXT("\\"));
	KeyDirectory = KeyDirectory.Replace(TEXT("/"), TEXT("\\"));
	KeyDirectory.RemoveFromEnd(TEXT("\\"));
	PrefixLinkRoot = PrefixLinkRoot.Replace(TEXT("/"), TEXT("\\"));
	PrefixLinkRoot.RemoveFromEnd(TEXT("\\"));

	if (KeyDirectory.IsEmpty() || KeyName.IsEmpty() || ModName.IsEmpty() || WorkDrive.IsEmpty() || ModBuildDirectory.IsEmpty() || PrefixLinkRoot.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("One or more required parameters were not set in the config files."));
		return;
	}

	// Log extracted and normalized configuration
	UE_LOG(LogTemp, Log, TEXT("WorkDrive: %s"), *WorkDrive);
	UE_LOG(LogTemp, Log, TEXT("ModName: %s"), *ModName);
	UE_LOG(LogTemp, Log, TEXT("ModBuildDirectory: %s"), *ModBuildDirectory);
	UE_LOG(LogTemp, Log, TEXT("KeyDirectory: %s"), *KeyDirectory);
	UE_LOG(LogTemp, Log, TEXT("KeyName: %s"), *KeyName);
	UE_LOG(LogTemp, Log, TEXT("PrefixLinkRoot: %s"), *PrefixLinkRoot);

	// Create necessary directories
	FString ModDirectory = FPaths::Combine(ModBuildDirectory, ModName);
	FString ModAddonsDirectory = FPaths::Combine(ModDirectory, TEXT("Addons"));
	FString ModKeysDirectory = FPaths::Combine(ModDirectory, TEXT("Keys"));

	UE_LOG(LogTemp, Log, TEXT("Full Mod Directory: %s"), *ModDirectory);
	UE_LOG(LogTemp, Log, TEXT("Full Addons Directory: %s"), *ModAddonsDirectory);
	UE_LOG(LogTemp, Log, TEXT("Full Keys Directory: %s"), *ModKeysDirectory);

	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	PlatformFile.CreateDirectoryTree(*ModDirectory);
	PlatformFile.CreateDirectoryTree(*ModAddonsDirectory);
	PlatformFile.CreateDirectoryTree(*ModKeysDirectory);

	// Copy files
	FString SourceModCpp = FPaths::Combine(WorkDrive, PrefixLinkRoot, TEXT("mod.cpp"));
	FString DestModCpp = FPaths::Combine(ModDirectory, TEXT("mod.cpp"));
	PlatformFile.CopyFile(*DestModCpp, *SourceModCpp);

	FString SourceBikey = FPaths::Combine(KeyDirectory, KeyName + TEXT(".bikey"));
	FString DestBikey = FPaths::Combine(ModKeysDirectory, KeyName + TEXT(".bikey"));
	PlatformFile.CopyFile(*DestBikey, *SourceBikey);

	FString SourceBiprivatekey = FPaths::Combine(KeyDirectory, KeyName + TEXT(".biprivatekey"));
	FString DestBiprivatekey = FPaths::Combine(ModKeysDirectory, KeyName + TEXT(".biprivatekey"));
	PlatformFile.CopyFile(*DestBiprivatekey, *SourceBiprivatekey);

	// Get DayZ Tools path
	FString DayZToolsPath = GetDayZToolsPath();
	if (DayZToolsPath.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find DayZ Tools path."));
		return;
	}

	DayZToolsPath = DayZToolsPath.Replace(TEXT("/"), TEXT("\\"));
	DayZToolsPath.RemoveFromEnd(TEXT("\\"));
	UE_LOG(LogTemp, Log, TEXT("DayZ Tools Path: %s"), *DayZToolsPath);

	// Loop over DirectoryList and process checked directories
	if (DirectoryList.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("No directories selected for building. Please refresh directories and select at least one."));
		return;
	}

	for (const FDirectoryItem& Item : DirectoryList)
	{
		if (Item.bChecked)
		{
			FString Directory = Item.Directory;
			UE_LOG(LogTemp, Log, TEXT("Processing directory: %s"), *Directory);
			BinarizePBO(Directory, ModBuildDirectory, ModName, WorkDrive, DayZToolsPath, KeyDirectory, KeyName, ProjectTrunkPath);
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("Skipping unchecked directory: %s"), *Item.Directory);
		}
	}

	UE_LOG(LogTemp, Log, TEXT("PBO building process completed."));
}

void FLevelEditorToolbar::OnPlayButtonClicked()
{
	if (CurrentPlayMode == EDayZPlayMode::Offline)
	{
		OnLaunchDayZOffline();
	}
	else if (CurrentPlayMode == EDayZPlayMode::Multiplayer)
	{
		OnLaunchDayZMultiplayer();
	}
}

void FLevelEditorToolbar::OnSelectPlayMode(EDayZPlayMode SelectedMode)
{
	CurrentPlayMode = SelectedMode;
}

bool FLevelEditorToolbar::IsPlayModeChecked(EDayZPlayMode Mode) const
{
	return CurrentPlayMode == Mode;
}


//----------------------------------------------
//  DayZ Launch logic
//----------------------------------------------
void FLevelEditorToolbar::OnLaunchDayZOffline()
{
	UE_LOG(LogTemp, Log, TEXT("Launching DayZ Offline..."));

	// First, exit any running DayZ instances
	if (!ExitDayZInstances())
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to close all DayZ instances. Proceeding with launch anyway."));
	}

	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	FString ProjectTrunkPath = Settings->ProjectTrunkPath;
	FString ProjectCfgPath = FPaths::Combine(ProjectTrunkPath, TEXT("project.cfg"));
	FString UserCfgPath = FPaths::Combine(ProjectTrunkPath, TEXT("user.cfg"));

	if (!FPaths::FileExists(ProjectCfgPath) || !FPaths::FileExists(UserCfgPath))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find project.cfg or user.cfg file, exiting."));
		return;
	}

	FString GameDirectory, ModName, Mods, Mission, MissionDirectory, ClientEXE, ClientLaunchParams, ModBuildDirectory, PlayerName;

	// Extract data from config files
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ModName"), ModName);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("AdditionalSPMods"), Mods);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("GameDirectory"), GameDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("SPMissionDirectory"), MissionDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ClientEXE"), ClientEXE);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ClientLaunchParams"), ClientLaunchParams);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("PlayerName"), PlayerName);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ModBuildDirectory"), ModBuildDirectory);

	// Normalize paths
	GameDirectory = GameDirectory.Replace(TEXT("/"), TEXT("\\"));
	GameDirectory.RemoveFromEnd(TEXT("\\"));
	ModBuildDirectory = ModBuildDirectory.Replace(TEXT("/"), TEXT("\\"));
	ModBuildDirectory.RemoveFromEnd(TEXT("\\"));
	ClientEXE = ClientEXE.Replace(TEXT("/"), TEXT("\\"));
	ClientEXE.RemoveFromEnd(TEXT("\\"));

	FString ModNameCleaned = ModName.TrimStartAndEnd();

	if (ModNameCleaned.StartsWith(TEXT("@")))
	{
		ModNameCleaned.RemoveAt(0);
	}

	Mission = FPaths::Combine(MissionDirectory, FString::Printf(TEXT("%s.%s"), *ModNameCleaned, *GetCurrentLevelName()));
	Mission = Mission.Replace(TEXT("/"), TEXT("\\"));
	Mission.RemoveFromEnd(TEXT("\\"));

	// Validate extracted data
	if (ClientEXE.IsEmpty() || ModName.IsEmpty() || GameDirectory.IsEmpty() || ModBuildDirectory.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("One or more required parameters were not set in the config files."));
		return;
	}

	// Handle AdditionalSPMods
	if (Mods.IsEmpty())
	{
		UE_LOG(LogTemp, Warning, TEXT("AdditionalSPMods parameter was not set in the project.cfg, ignoring."));
		Mods = ModName;
	}
	else
	{
		Mods = Mods + TEXT(";") + ModName;
	}

	// Handle PlayerName
	if (!PlayerName.IsEmpty())
	{
		PlayerName = FString::Printf(TEXT("-name=%s"), *PlayerName);
	}

	// Construct mod list
	TArray<FString> ModArray;
	Mods.ParseIntoArray(ModArray, TEXT(";"));
	FString ModList;
	for (const FString& Mod : ModArray)
	{
		if (ModList.IsEmpty())
		{
			ModList = FPaths::Combine(ModBuildDirectory, Mod);
		}
		else
		{
			ModList += TEXT(";") + FPaths::Combine(ModBuildDirectory, Mod);
		}
	}

	// Construct full path to ClientEXE
	FString FullClientEXEPath = FPaths::Combine(GameDirectory, ClientEXE);

	// Construct launch command
	FString LaunchCommand = FString::Printf(TEXT("\"%s\" %s \"-mod=%s\" \"-mission=%s\" %s -dologs -nopause -adminlog -freezecheck \"-scriptDebug=true\""),
		*FullClientEXEPath, *ClientLaunchParams, *ModList, *Mission, *PlayerName);

	UE_LOG(LogTemp, Log, TEXT("Launching DayZ with command: %s"), *LaunchCommand);

	// Launch DayZ
	FProcHandle ProcHandle = FPlatformProcess::CreateProc(
		*FullClientEXEPath,
		*LaunchCommand,
		true,   // bLaunchDetached
		false,  // bLaunchHidden
		false,  // bLaunchReallyHidden
		nullptr,// OutProcessID
		0,      // PriorityModifier
		*GameDirectory, // OptionalWorkingDirectory
		nullptr,// PipeRead
		nullptr // PipeWrite
	);

	if (ProcHandle.IsValid())
	{
		UE_LOG(LogTemp, Log, TEXT("Successfully launched DayZ"));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to launch DayZ"));
	}
}

bool FLevelEditorToolbar::LaunchDayZServer()
{
	UE_LOG(LogTemp, Log, TEXT("Launching DayZ Server..."));

	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	FString ProjectCfgPath = FPaths::Combine(Settings->ProjectTrunkPath, TEXT("project.cfg"));
	FString UserCfgPath = FPaths::Combine(Settings->ProjectTrunkPath, TEXT("user.cfg"));

	FString Port, Password, GameDirectory, ServerDirectory, ServerProfileDirectory, ServerConfig, ModName, Mods, Mission, MissionDirectory, ServerEXE, ServerLaunchParams, ModBuildDirectory;

	// Extract data from config files
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("Port"), Port);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ServerPassword"), Password);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("GameDirectory"), GameDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ServerDirectory"), ServerDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ServerProfileDirectory"), ServerProfileDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ServerConfig"), ServerConfig);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ModName"), ModName);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("AdditionalMPMods"), Mods);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("MPMissionDirectory"), MissionDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ServerEXE"), ServerEXE);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ServerLaunchParams"), ServerLaunchParams);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ModBuildDirectory"), ModBuildDirectory);

	// Normalize paths
	GameDirectory = GameDirectory.Replace(TEXT("/"), TEXT("\\"));
	ServerDirectory = ServerDirectory.Replace(TEXT("/"), TEXT("\\"));
	ServerProfileDirectory = ServerProfileDirectory.Replace(TEXT("/"), TEXT("\\"));
	ServerConfig = ServerConfig.Replace(TEXT("/"), TEXT("\\"));
	ModBuildDirectory = ModBuildDirectory.Replace(TEXT("/"), TEXT("\\"));

	FString ModNameCleaned = ModName.TrimStartAndEnd();

	if (ModNameCleaned.StartsWith(TEXT("@")))
	{
		ModNameCleaned.RemoveAt(0);
	}

	Mission = FPaths::Combine(MissionDirectory, FString::Printf(TEXT("%s.%s"), *ModNameCleaned, *GetCurrentLevelName()));
	Mission = Mission.Replace(TEXT("/"), TEXT("\\"));
	Mission.RemoveFromEnd(TEXT("\\"));

	// Validate data
	if (ServerEXE.IsEmpty() || ServerDirectory.IsEmpty() || ServerProfileDirectory.IsEmpty() || ServerConfig.IsEmpty() || ModName.IsEmpty() || ModBuildDirectory.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("One or more required parameters were not set in the config files."));
		return false;
	}

	// Handle Port
	if (Port.IsEmpty())
	{
		Port = TEXT("2302");
	}

	// Handle Mods
	if (Mods.IsEmpty())
	{
		Mods = ModName;
	}
	else
	{
		Mods = Mods + TEXT(";") + ModName;
	}

	// Construct mod list
	TArray<FString> ModArray;
	Mods.ParseIntoArray(ModArray, TEXT(";"));
	FString ModList;
	for (const FString& Mod : ModArray)
	{
		if (ModList.IsEmpty())
		{
			ModList = FPaths::Combine(ModBuildDirectory, Mod);
		}
		else
		{
			ModList += TEXT(";") + FPaths::Combine(ModBuildDirectory, Mod);
		}
	}

	// Construct launch command
	FString ServerPath = FPaths::Combine(ServerDirectory, ServerEXE);
	FString LaunchCommand = FString::Printf(TEXT("%s %s \"-config=%s\" -port=%s \"-profiles=%s\" -dologs -adminlog -freezecheck \"-scriptDebug=true\" \"-cpuCount=4\" \"-mission=%s\" \"-mod=%s\""),
		*ServerPath, *ServerLaunchParams, *ServerConfig, *Port, *ServerProfileDirectory, *Mission, *ModList);

	if (!Password.IsEmpty())
	{
		LaunchCommand += FString::Printf(TEXT(" -password=%s"), *Password);
	}

	UE_LOG(LogTemp, Log, TEXT("Launching DayZ Server with command: %s"), *LaunchCommand);

	// Launch DayZ Server
	FProcHandle ServerProcHandle = FPlatformProcess::CreateProc(
		*ServerPath,
		*LaunchCommand,
		true,   // bLaunchDetached
		false,  // bLaunchHidden
		false,  // bLaunchReallyHidden
		nullptr,// OutProcessID
		0,      // PriorityModifier
		*ServerDirectory, // OptionalWorkingDirectory
		nullptr,// PipeRead
		nullptr // PipeWrite
	);

	if (ServerProcHandle.IsValid())
	{
		UE_LOG(LogTemp, Log, TEXT("Successfully launched DayZ Server"));
		return true;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to launch DayZ Server"));
		return false;
	}
}

bool FLevelEditorToolbar::LaunchDayZLocalMP()
{
	UE_LOG(LogTemp, Log, TEXT("Launching DayZ Local MP..."));

	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	FString ProjectCfgPath = FPaths::Combine(Settings->ProjectTrunkPath, TEXT("project.cfg"));
	FString UserCfgPath = FPaths::Combine(Settings->ProjectTrunkPath, TEXT("user.cfg"));

	FString GameDirectory, ModName, Mods, Mission, MissionDirectory, ClientEXE, ClientLaunchParams, ModBuildDirectory, PlayerName;

	// Extract data from config files
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("GameDirectory"), GameDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ModName"), ModName);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("AdditionalMPMods"), Mods);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("SPMissionDirectory"), MissionDirectory);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ClientEXE"), ClientEXE);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ClientLaunchParams"), ClientLaunchParams);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("PlayerName"), PlayerName);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ModBuildDirectory"), ModBuildDirectory);

	// Normalize paths
	GameDirectory = GameDirectory.Replace(TEXT("/"), TEXT("\\"));
	ModBuildDirectory = ModBuildDirectory.Replace(TEXT("/"), TEXT("\\"));

	FString ModNameCleaned = ModName.TrimStartAndEnd();

	if (ModNameCleaned.StartsWith(TEXT("@")))
	{
		ModNameCleaned.RemoveAt(0);
	}

	Mission = FPaths::Combine(MissionDirectory, FString::Printf(TEXT("%s.%s"), *ModNameCleaned, *GetCurrentLevelName()));
	Mission = Mission.Replace(TEXT("/"), TEXT("\\"));
	Mission.RemoveFromEnd(TEXT("\\"));

	// Validate data
	if (ClientEXE.IsEmpty() || GameDirectory.IsEmpty() || ModName.IsEmpty() || ModBuildDirectory.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("One or more required parameters were not set in the config files."));
		return false;
	}

	// Handle Mods
	if (Mods.IsEmpty())
	{
		Mods = ModName;
	}
	else
	{
		Mods = Mods + TEXT(";") + ModName;
	}

	// Construct mod list
	TArray<FString> ModArray;
	Mods.ParseIntoArray(ModArray, TEXT(";"));
	FString ModList;
	for (const FString& Mod : ModArray)
	{
		if (ModList.IsEmpty())
		{
			ModList = FPaths::Combine(ModBuildDirectory, Mod);
		}
		else
		{
			ModList += TEXT(";") + FPaths::Combine(ModBuildDirectory, Mod);
		}
	}

	// Handle PlayerName
	if (!PlayerName.IsEmpty())
	{
		PlayerName = FString::Printf(TEXT("-name=%s"), *PlayerName);
	}

	// Construct launch command
	FString ClientPath = FPaths::Combine(GameDirectory, ClientEXE);
	FString LaunchCommand = FString::Printf(TEXT("%s %s \"-mod=%s\" %s -dologs -adminlog -freezecheck \"-scriptDebug=true\" -connect=127.0.0.1:2302"),
		*ClientPath, *ClientLaunchParams, *ModList, *PlayerName);

	UE_LOG(LogTemp, Log, TEXT("Launching DayZ Local MP with command: %s"), *LaunchCommand);

	// Launch DayZ Local MP
	FProcHandle ClientProcHandle = FPlatformProcess::CreateProc(
		*ClientPath,
		*LaunchCommand,
		true,   // bLaunchDetached
		false,  // bLaunchHidden
		false,  // bLaunchReallyHidden
		nullptr,// OutProcessID
		0,      // PriorityModifier
		*GameDirectory, // OptionalWorkingDirectory
		nullptr,// PipeRead
		nullptr // PipeWrite
	);

	if (ClientProcHandle.IsValid())
	{
		UE_LOG(LogTemp, Log, TEXT("Successfully launched DayZ Local MP"));
		return true;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to launch DayZ Local MP"));
		return false;
	}
}

void FLevelEditorToolbar::OnLaunchDayZMultiplayer()
{
	UE_LOG(LogTemp, Log, TEXT("Launching DayZ Multiplayer..."));

	// First, exit any running DayZ instances
	if (!ExitDayZInstances())
	{
		UE_LOG(LogTemp, Warning, TEXT("Failed to close all DayZ instances. Proceeding with launch anyway."));
	}

	if (WriteCameraPositionToFile())
	{
		// Launch DayZ Server
		if (LaunchDayZServer())
		{
			UE_LOG(LogTemp, Log, TEXT("DayZ Server launched successfully. Waiting before launching client..."));

			// Wait for 3 seconds
			FPlatformProcess::Sleep(3.0f);

			// Launch DayZ Local MP (client)
			if (LaunchDayZLocalMP())
			{
				UE_LOG(LogTemp, Log, TEXT("DayZ Multiplayer launched successfully."));
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("Failed to launch DayZ Local MP client."));
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to launch DayZ Server. Aborting Multiplayer launch."));
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to save Unreal Data. Aborting..."));
	}
}

bool FLevelEditorToolbar::ExitDayZInstances()
{
	const TCHAR* ProcessNames[] = {
		TEXT("DayZ_x64.exe"),
		TEXT("DayZServer_x64.exe"),
		TEXT("DZSALModServer.exe"),
		TEXT("DayZDiag_x64.exe"),
		TEXT("DayZDiag_x64-Server.exe")
	};

	bool AllProcessesClosed = true;

	for (const TCHAR* ProcessName : ProcessNames)
	{
		HANDLE hSnapShot = CreateToolhelp32Snapshot(TH32CS_SNAPALL, NULL);
		PROCESSENTRY32 pEntry;
		pEntry.dwSize = sizeof(pEntry);
		BOOL hRes = Process32First(hSnapShot, &pEntry);
		while (hRes)
		{
			if (FCString::Strcmp(pEntry.szExeFile, ProcessName) == 0)
			{
				HANDLE hProcess = OpenProcess(PROCESS_TERMINATE, 0, (DWORD)pEntry.th32ProcessID);
				if (hProcess != NULL)
				{
					if (TerminateProcess(hProcess, 9))
					{
						UE_LOG(LogTemp, Log, TEXT("Successfully terminated process: %s"), ProcessName);
					}
					else
					{
						UE_LOG(LogTemp, Warning, TEXT("Failed to terminate process: %s"), ProcessName);
						AllProcessesClosed = false;
					}
					CloseHandle(hProcess);
				}
				else
				{
					UE_LOG(LogTemp, Warning, TEXT("Failed to open process: %s"), ProcessName);
					AllProcessesClosed = false;
				}
			}
			hRes = Process32Next(hSnapShot, &pEntry);
		}
		CloseHandle(hSnapShot);
	}

	return AllProcessesClosed;
}

//----------------------------------------------
//  Directory checking & PBO building
//----------------------------------------------

void FLevelEditorToolbar::LoadDirectoryList()
{
	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	DirectoryList.Empty();
	for (const FDirectoryState& DirState : Settings->SavedDirectories)
	{
		DirectoryList.Add(FDirectoryItem(DirState.Directory, DirState.bChecked));
		UE_LOG(LogTemp, Log, TEXT("Loaded directory: %s, Checked: %s"), *DirState.Directory, DirState.bChecked ? TEXT("True") : TEXT("False"));
	}
	UE_LOG(LogTemp, Log, TEXT("Loaded %d directories from config"), DirectoryList.Num());
}


void FLevelEditorToolbar::SaveDirectoryList()
{
	UDayZToolsSettings* Settings = GetMutableDefault<UDayZToolsSettings>();
	Settings->SavedDirectories.Empty();
	for (const FDirectoryItem& Item : DirectoryList)
	{
		Settings->SavedDirectories.Add(FDirectoryState(Item.Directory, Item.bChecked));
		UE_LOG(LogTemp, Log, TEXT("Saving directory: %s, Checked: %s"), *Item.Directory, Item.bChecked ? TEXT("True") : TEXT("False"));
	}
	Settings->SaveConfig();
	Settings->UpdateDefaultConfigFile();
	UE_LOG(LogTemp, Log, TEXT("Saved %d directories to config"), Settings->SavedDirectories.Num());

	// Verify save
	const UDayZToolsSettings* VerifySettings = GetDefault<UDayZToolsSettings>();
	UE_LOG(LogTemp, Log, TEXT("Verifying save: %d directories in config"), VerifySettings->SavedDirectories.Num());
	for (const FDirectoryState& DirState : VerifySettings->SavedDirectories)
	{
		UE_LOG(LogTemp, Log, TEXT("Verified: %s, Checked: %s"), *DirState.Directory, DirState.bChecked ? TEXT("True") : TEXT("False"));
	}
}

void FLevelEditorToolbar::OnRefreshDirectoriesClicked()
{
	UE_LOG(LogTemp, Log, TEXT("Refreshing directories..."));

	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	FString ProjectTrunkPath = Settings->ProjectTrunkPath.Replace(TEXT("/"), TEXT("\\"));
	ProjectTrunkPath.RemoveFromEnd(TEXT("\\"));
	FString ProjectCfgPath = FPaths::Combine(ProjectTrunkPath, TEXT("project.cfg"));
	FString UserCfgPath = FPaths::Combine(ProjectTrunkPath, TEXT("user.cfg"));

	if (!FPaths::FileExists(ProjectCfgPath) || !FPaths::FileExists(UserCfgPath))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find project.cfg or user.cfg file, exiting."));
		return;
	}

	FString WorkDrive, PrefixLinkRoot;

	// Extract data from config files
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("WorkDrive"), WorkDrive);
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("prefixLinkRoot"), PrefixLinkRoot);

	// Normalize paths
	WorkDrive = WorkDrive.Replace(TEXT("/"), TEXT("\\"));
	WorkDrive.RemoveFromEnd(TEXT("\\"));
	PrefixLinkRoot = PrefixLinkRoot.Replace(TEXT("/"), TEXT("\\"));
	PrefixLinkRoot.RemoveFromEnd(TEXT("\\"));

	if (WorkDrive.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("WorkDrive parameter was not set in the config files."));
		return;
	}

	// Construct root directory for searching
	FString RootDirectory = FPaths::Combine(WorkDrive, PrefixLinkRoot);

	UE_LOG(LogTemp, Log, TEXT("WorkDrive: %s"), *WorkDrive);
	UE_LOG(LogTemp, Log, TEXT("PrefixLinkRoot: %s"), *PrefixLinkRoot);
	UE_LOG(LogTemp, Log, TEXT("Searching for config.cpp files in: %s"), *RootDirectory);

	// Find and process config.cpp files
	TArray<FString> ConfigFiles;
	IFileManager::Get().FindFilesRecursive(ConfigFiles, *RootDirectory, TEXT("config.cpp"), true, false);

	UE_LOG(LogTemp, Log, TEXT("Found %d config.cpp files:"), ConfigFiles.Num());

	// Load the saved checked states from settings
	const UDayZToolsSettings* SettingsConst = GetDefault<UDayZToolsSettings>();
	TMap<FString, bool> SavedDirectoryMap;
	for (const FDirectoryState& DirState : SettingsConst->SavedDirectories)
	{
		SavedDirectoryMap.Add(DirState.Directory, DirState.bChecked);
	}

	DirectoryList.Empty();

	for (const FString& ConfigFile : ConfigFiles)
	{
		FString NormalizedConfigFile = ConfigFile.Replace(TEXT("/"), TEXT("\\"));
		UE_LOG(LogTemp, Log, TEXT("Config file found: %s"), *NormalizedConfigFile);

		FString Directory = FPaths::GetPath(NormalizedConfigFile);
		if (!HasParentConfig(Directory))
		{
			// Preserve the checked state if it was previously saved
			bool bChecked = true; // Default to checked
			if (SavedDirectoryMap.Contains(Directory))
			{
				bChecked = SavedDirectoryMap[Directory];
			}

			UE_LOG(LogTemp, Log, TEXT("Adding directory: %s (Checked: %s)"), *Directory, bChecked ? TEXT("True") : TEXT("False"));
			DirectoryList.Add(FDirectoryItem(Directory, bChecked));
		}
		else
		{
			UE_LOG(LogTemp, Log, TEXT("Skipping directory (has parent config.cpp): %s"), *Directory);
		}
	}

	if (ConfigFiles.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("No config.cpp files found in %s"), *RootDirectory);
	}

	// Save the updated DirectoryList to settings
	SaveDirectoryList();
}

void FLevelEditorToolbar::OnDirectoryCheckboxChanged(ECheckBoxState NewState, int32 DirectoryIndex)
{
	if (DirectoryList.IsValidIndex(DirectoryIndex))
	{
		DirectoryList[DirectoryIndex].bChecked = (NewState == ECheckBoxState::Checked);

		// Save the updated DirectoryList
		SaveDirectoryList();
	}
}

ECheckBoxState FLevelEditorToolbar::GetDirectoryCheckboxState(int32 DirectoryIndex)
{
	if (DirectoryIndex >= 0 && DirectoryIndex < DirectoryList.Num())
	{
		return (DirectoryList[DirectoryIndex].bChecked ? ECheckBoxState::Checked : ECheckBoxState::Unchecked);
	}
	return ECheckBoxState::Unchecked;
}

void FLevelEditorToolbar::BinarizePBO(const FString& Directory, const FString& ModBuildDirectory, const FString& ModName, const FString& WorkDrive, const FString& DayZToolsPath, const FString& KeyDirectory, const FString& KeyName, const FString& TrunkPath)
{
	// Normalize and remove trailing backslashes from paths
	FString NormalizedDirectory = Directory.Replace(TEXT("/"), TEXT("\\"));
	NormalizedDirectory.RemoveFromEnd(TEXT("\\"));
	FString NormalizedModBuildDirectory = ModBuildDirectory.Replace(TEXT("/"), TEXT("\\"));
	NormalizedModBuildDirectory.RemoveFromEnd(TEXT("\\"));
	FString NormalizedWorkDrive = WorkDrive.Replace(TEXT("/"), TEXT("\\"));
	NormalizedWorkDrive.RemoveFromEnd(TEXT("\\"));
	FString NormalizedDayZToolsPath = DayZToolsPath.Replace(TEXT("/"), TEXT("\\"));
	NormalizedDayZToolsPath.RemoveFromEnd(TEXT("\\"));
	FString NormalizedKeyDirectory = KeyDirectory.Replace(TEXT("/"), TEXT("\\"));
	NormalizedKeyDirectory.RemoveFromEnd(TEXT("\\"));
	FString NormalizedTrunkPath = TrunkPath.Replace(TEXT("/"), TEXT("\\"));
	NormalizedTrunkPath.RemoveFromEnd(TEXT("\\"));

	FString AddonBuilderPath = FPaths::Combine(NormalizedDayZToolsPath, TEXT("Bin"), TEXT("AddonBuilder"), TEXT("AddonBuilder.exe"));
	FString SignFilePath = FPaths::Combine(NormalizedDayZToolsPath, TEXT("Bin"), TEXT("DsUtils"), TEXT("DSSignFile.exe"));

	// Correct path construction
	FString AddonsPath = FPaths::Combine(NormalizedModBuildDirectory, ModName, TEXT("Addons")).Replace(TEXT("/"), TEXT("\\"));

	// Log the constructed AddonsPath
	UE_LOG(LogTemp, Log, TEXT("Constructed AddonsPath: %s"), *AddonsPath);

	FString TempPath = FPaths::ConvertRelativePathToFull(FPaths::ProjectIntermediateDir()).Replace(TEXT("/"), TEXT("\\"));
	TempPath.RemoveFromEnd(TEXT("\\"));

	// Get PBOIncludeFile from project.cfg
	FString PBOIncludeFile;
	FString ProjectCfgPath = FPaths::Combine(NormalizedTrunkPath, TEXT("project.cfg"));
	ExtractDataFromConfig(ProjectCfgPath, TEXT(""), TEXT("PBOIncludeFile"), PBOIncludeFile);

	FString IncludePath = FPaths::Combine(NormalizedTrunkPath, PBOIncludeFile).Replace(TEXT("/"), TEXT("\\"));

	FString AddonBuilderArgs = FString::Printf(TEXT("\"%s\" \"%s\" -clear -temp=\"%s\" -project=\"%s\" -packonly -include=\"%s\""),
		*NormalizedDirectory,
		*AddonsPath,
		*TempPath,
		*NormalizedWorkDrive,
		*IncludePath);

	UE_LOG(LogTemp, Log, TEXT("Executing AddonBuilder: %s %s"), *AddonBuilderPath, *AddonBuilderArgs);

	int32 ReturnCode;
	FString StdOut, StdErr;
	if (FPlatformProcess::ExecProcess(*AddonBuilderPath, *AddonBuilderArgs, &ReturnCode, &StdOut, &StdErr))
	{
		if (ReturnCode == 0)
		{
			UE_LOG(LogTemp, Log, TEXT("Successfully built PBO for %s"), *NormalizedDirectory);

			FString PBOName = FPaths::GetBaseFilename(NormalizedDirectory);
			FString ExpectedPBOPath = FPaths::Combine(AddonsPath, PBOName + TEXT(".pbo")).Replace(TEXT("/"), TEXT("\\"));
			UE_LOG(LogTemp, Log, TEXT("Expected PBO output path: %s"), *ExpectedPBOPath);

			if (FPaths::FileExists(ExpectedPBOPath))
			{
				UE_LOG(LogTemp, Log, TEXT("PBO file confirmed at: %s"), *ExpectedPBOPath);

				// Sign PBO
				FString PrivateKeyPath = FPaths::Combine(NormalizedKeyDirectory, KeyName + TEXT(".biprivatekey")).Replace(TEXT("/"), TEXT("\\"));
				FString SignFileArgs = FString::Printf(TEXT("\"%s\" \"%s\""), *PrivateKeyPath, *ExpectedPBOPath);

				UE_LOG(LogTemp, Log, TEXT("Executing DSSignFile: %s %s"), *SignFilePath, *SignFileArgs);

				if (FPlatformProcess::ExecProcess(*SignFilePath, *SignFileArgs, &ReturnCode, &StdOut, &StdErr))
				{
					if (ReturnCode == 0)
					{
						UE_LOG(LogTemp, Log, TEXT("Successfully signed PBO: %s"), *ExpectedPBOPath);
					}
					else
					{
						UE_LOG(LogTemp, Error, TEXT("Failed to sign PBO: %s. Error: %s"), *ExpectedPBOPath, *StdErr);
					}
				}
				else
				{
					UE_LOG(LogTemp, Error, TEXT("Failed to execute DSSignFile for %s"), *ExpectedPBOPath);
				}
			}
			else
			{
				UE_LOG(LogTemp, Error, TEXT("PBO file not found at expected location: %s"), *ExpectedPBOPath);
				UE_LOG(LogTemp, Log, TEXT("AddonBuilder Output: %s"), *StdOut);
				UE_LOG(LogTemp, Error, TEXT("AddonBuilder Errors: %s"), *StdErr);
			}
		}
		else
		{
			UE_LOG(LogTemp, Error, TEXT("Failed to build PBO for %s. Error: %s"), *NormalizedDirectory, *StdErr);
			UE_LOG(LogTemp, Log, TEXT("AddonBuilder Output: %s"), *StdOut);
		}
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to execute AddonBuilder for %s"), *NormalizedDirectory);
	}
}

bool FLevelEditorToolbar::HasParentConfig(const FString& Directory)
{
	FString ParentDir = FPaths::GetPath(Directory);
	for (int i = 0; i < 8; ++i)
	{
		if (FPaths::FileExists(FPaths::Combine(ParentDir, TEXT("config.cpp"))))
		{
			return true;
		}
		ParentDir = FPaths::GetPath(ParentDir);
	}
	return false;
}

//----------------------------------------------
//  WRP test
//----------------------------------------------
void FLevelEditorToolbar::TestLoadWRPFile()
{

}

//----------------------------------------------
//  Misc. config / file helpers
//----------------------------------------------
void FLevelEditorToolbar::ExtractDataFromConfig(const FString& ProjectCfgPath, const FString& UserCfgPath, const FString& Key, FString& OutValue)
{
	TArray<FString> ConfigFiles = { ProjectCfgPath, UserCfgPath };

	for (const FString& ConfigFile : ConfigFiles)
	{
		TArray<FString> FileLines;
		if (FFileHelper::LoadFileToStringArray(FileLines, *ConfigFile))
		{
			for (const FString& Line : FileLines)
			{
				if (Line.StartsWith(Key + TEXT("=")))
				{
					FString RightSide;
					if (Line.Split(TEXT("="), nullptr, &RightSide))
					{
						OutValue = RightSide.TrimStartAndEnd();
						return;  // Found the value, no need to continue
					}
				}
			}
		}
	}

	// If we get here, the key wasn't found in either file
	UE_LOG(LogTemp, Warning, TEXT("Key '%s' not found in config files"), *Key);
}

FString FLevelEditorToolbar::GetDayZToolsPath()
{
	FString DayZToolsPath;

	// Try to get from registry
	if (FWindowsPlatformMisc::QueryRegKey(HKEY_CURRENT_USER, TEXT("Software\\bohemia interactive\\Dayz Tools"), TEXT("Path"), DayZToolsPath))
	{
		return DayZToolsPath;
	}

	// If not in registry, try to get from config
	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	ExtractDataFromConfig(FPaths::Combine(Settings->ProjectTrunkPath, TEXT("project.cfg")),
		FPaths::Combine(Settings->ProjectTrunkPath, TEXT("user.cfg")),
		TEXT("ToolsDirectory"), DayZToolsPath);

	if (!DayZToolsPath.IsEmpty())
	{
		return DayZToolsPath;
	}

	UE_LOG(LogTemp, Error, TEXT("Failed to find DayZ Tools path in registry or config files."));
	return FString();
}

FVector FLevelEditorToolbar::ConvertUnrealToDayZCoords(const FVector& UnrealCoords)
{
	float ScaleFactor = 0.01f; // Convert centimeters to meters

	float DayZX = UnrealCoords.Y * ScaleFactor;
	float DayZY = UnrealCoords.X * ScaleFactor;
	float DayZZ = UnrealCoords.Z * ScaleFactor;

	return FVector(DayZX, DayZY, DayZZ);
}

FVector FLevelEditorToolbar::GetEditorCameraPosition()
{
	FVector CameraPosition = FVector::ZeroVector;

	FLevelEditorModule& LevelEditor = FModuleManager::GetModuleChecked<FLevelEditorModule>("LevelEditor");
	TSharedPtr<SLevelViewport> LevelViewport = LevelEditor.GetFirstActiveLevelViewport();

	if (LevelViewport.IsValid())
	{
		FLevelEditorViewportClient& LevelViewportClient = LevelViewport->GetLevelViewportClient();
		CameraPosition = LevelViewportClient.GetViewLocation();

		// If you're using the Rewind Debugger, you might want to consider its current state
		// This is just an example and might need to be adjusted based on your specific setup
		/*
		if (YourRewindDebugger)
		{
			if (const TraceServices::IAnalysisSession* Session = YourRewindDebugger->GetAnalysisSession())
			{
				TraceServices::FAnalysisSessionReadScope SessionReadScope(*Session);
				double CurrentTraceTime = YourRewindDebugger->CurrentTraceTime();

				// Add any specific logic for handling the debugger state
				// For example, you might want to adjust the camera position based on debugging data
			}
		}
		*/
	}

	return CameraPosition;
}

bool FLevelEditorToolbar::WriteCameraPositionToFile()
{
	// Get the camera position
	FVector CameraPosition = GetEditorCameraPosition();

	// Swap Y and Z to get X Z Y format

	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	FString ProjectTrunkPath = Settings->ProjectTrunkPath.Replace(TEXT("/"), TEXT("\\"));
	ProjectTrunkPath.RemoveFromEnd(TEXT("\\"));
	FString ProjectCfgPath = FPaths::Combine(ProjectTrunkPath, TEXT("project.cfg"));
	FString UserCfgPath = FPaths::Combine(ProjectTrunkPath, TEXT("user.cfg"));

	if (!FPaths::FileExists(ProjectCfgPath) || !FPaths::FileExists(UserCfgPath))
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to find project.cfg or user.cfg file, exiting."));
		return false;
	}

	FString ServerProfileDirectory;

	// Extract data from config files
	ExtractDataFromConfig(ProjectCfgPath, UserCfgPath, TEXT("ServerProfileDirectory"), ServerProfileDirectory);

	// Normalize paths
	ServerProfileDirectory = ServerProfileDirectory.Replace(TEXT("/"), TEXT("\\"));
	ServerProfileDirectory.RemoveFromEnd(TEXT("\\"));

	if (ServerProfileDirectory.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("ServerProfileDirectory path not found in user.cfg"));
		return false;
	}

	// Construct the full path for UnrealData.txt
	FString FilePath = FPaths::Combine(ServerProfileDirectory, TEXT("\\EmberAdminTools\\UnrealData.txt"));

	FVector DayZCameraPosition = ConvertUnrealToDayZCoords(CameraPosition);

	// Prepare the content to write
	FString Content = FString::Printf(TEXT("CameraPosition=%.2f %.2f %.2f"),
		DayZCameraPosition.X, DayZCameraPosition.Z, DayZCameraPosition.Y);

	// Write to file (this will overwrite if the file already exists)
	if (FFileHelper::SaveStringToFile(Content, *FilePath))
	{
		UE_LOG(LogTemp, Log, TEXT("Successfully wrote camera position to %s"), *FilePath);
		return true;
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to write camera position to %s"), *FilePath);
		return false;
	}
}

FString FLevelEditorToolbar::GetCurrentLevelName()
{
	FString LevelName = TEXT("Unknown");

	if (GEditor)
	{
		UWorld* EditorWorld = GEditor->GetEditorWorldContext().World();
		if (EditorWorld)
		{
			if (EditorWorld->PersistentLevel)
			{
				// Get the name of the persistent level
				LevelName = EditorWorld->PersistentLevel->GetOuter()->GetName();
			}
			else
			{
				// Fallback to the world's name if persistent level is not available
				LevelName = EditorWorld->GetName();
			}
		}
	}

	return LevelName;
}

void FLevelEditorToolbar::OnExportToolsClicked()
{
	UE_LOG(LogTemp, Log, TEXT("Export Tools button clicked"));
	// This function is just a placeholder since we're using the dropdown menu
}

TSharedRef<SWidget> FLevelEditorToolbar::GenerateExportToolsMenu()
{
	FMenuBuilder MenuBuilder(true, PluginCommands);

	// Central Economy Section
	MenuBuilder.BeginSection("CentralEconomySection", NSLOCTEXT("DayZTools", "CentralEconomySection", "Central Economy"));

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "ExportAllCE", "Export All"),
		NSLOCTEXT("DayZTools", "ExportAllCE_Tooltip", "Export all Central Economy data"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportAllCentralEconomy))
	);

	// Add Dynamic Events submenu under Central Economy
	MenuBuilder.AddSubMenu(
		NSLOCTEXT("DayZTools", "DynamicEvents", "Dynamic Events"),
		NSLOCTEXT("DayZTools", "DynamicEvents_Tooltip", "Export Dynamic Events data"),
		FNewMenuDelegate::CreateStatic(&FLevelEditorToolbar::GenerateDynamicEventsExportMenu)
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "TypesDefinitions", "Types Definitions"),
		NSLOCTEXT("DayZTools", "TypesDefinitions_Tooltip", "Export Types Definitions"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportTypesDefinitions))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "LootPoints", "Loot Points"),
		NSLOCTEXT("DayZTools", "LootPoints_Tooltip", "Export Loot Points"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportLootPoints))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "ServerMessages", "Server Messages"),
		NSLOCTEXT("DayZTools", "ServerMessages_Tooltip", "Export Server Messages"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportServerMessages))
	);

	MenuBuilder.EndSection();

	// World Section
	MenuBuilder.BeginSection("WorldSection", NSLOCTEXT("DayZTools", "WorldSection", "World"));

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "ExportAllWorld", "Export All"),
		NSLOCTEXT("DayZTools", "ExportAllWorld_Tooltip", "Export all World data"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportAllWorld))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "TerrainHeightmap", "Terrain Heightmap"),
		NSLOCTEXT("DayZTools", "TerrainHeightmap_Tooltip", "Export Terrain Heightmap"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportTerrainHeightmap))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "TerrainLayers", "Terrain Layers"),
		NSLOCTEXT("DayZTools", "TerrainLayers_Tooltip", "Export Terrain Layers"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportTerrainLayers))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "TerrainObjects", "Terrain Objects"),
		NSLOCTEXT("DayZTools", "TerrainObjects_Tooltip", "Export Terrain Objects"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportTerrainObjects))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "SurfaceMask", "Surface Mask"),
		NSLOCTEXT("DayZTools", "SurfaceMask_Tooltip", "Export Surface Mask as PNG"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportSurfaceMask))
	);

	MenuBuilder.EndSection();

	return MenuBuilder.MakeWidget();
}

void FLevelEditorToolbar::GenerateDynamicEventsExportMenu(FMenuBuilder& MenuBuilder)
{
	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "ExportAllDE", "Export All"),
		NSLOCTEXT("DayZTools", "ExportAllDE_Tooltip", "Export all Dynamic Events data"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportAllDynamicEvents))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "ExportDEDefinitions", "Dynamic Event Definitions"),
		NSLOCTEXT("DayZTools", "ExportDEDefinitions_Tooltip", "Export Dynamic Event Definitions"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportDynamicEventDefinitions))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "ExportDEGroupDefinitions", "Dynamic Event Group Definitions"),
		NSLOCTEXT("DayZTools", "ExportDEGroupDefinitions_Tooltip", "Export Dynamic Event Group Definitions"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportDynamicEventGroupDefinitions))
	);

	MenuBuilder.AddMenuEntry(
		NSLOCTEXT("DayZTools", "ExportDESpawns", "Dynamic Event Spawns"),
		NSLOCTEXT("DayZTools", "ExportDESpawns_Tooltip", "Export Dynamic Event Spawns"),
		FSlateIcon(),
		FUIAction(FExecuteAction::CreateStatic(&FLevelEditorToolbar::OnExportEventSpawns))
	);
}

//----------------------------------------------
//  Central Economy Export functions
//----------------------------------------------
void FLevelEditorToolbar::OnExportAllCentralEconomy()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting all Central Economy data..."));

    // Call all Central Economy export functions
    OnExportAllDynamicEvents();
    OnExportTypesDefinitions();
    OnExportLootPoints();

    FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "ExportAllCentralEconomyComplete", "All Central Economy data has been exported."));
}

//----------------------------------------------
//  Dynamic Events Export functions
//----------------------------------------------
void FLevelEditorToolbar::OnExportAllDynamicEvents()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting all Dynamic Events data..."));

    // Call all Dynamic Events export functions
    OnExportDynamicEventDefinitions();
    OnExportDynamicEventGroupDefinitions();
    OnExportEventSpawns();

    FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "ExportAllDynamicEventsComplete", "All Dynamic Events data has been exported."));
}

void FLevelEditorToolbar::OnExportDynamicEventDefinitions()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Dynamic Event Definitions to XML..."));

    // Get the mission path from settings
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    if (!Settings)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not access DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "SettingsError", "Could not access DayZ Tools settings"));
        return;
    }

    FString MissionPath = Settings->MissionPath;
    if (MissionPath.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Mission Path is not set in DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "MissionPathError", "Mission Path is not set in DayZ Tools settings. Please set it in Project Settings > Plugins > DayZ Tools."));
        return;
    }

    // Create the db directory if it doesn't exist
    FString DbPath = FPaths::Combine(MissionPath, TEXT("db"));
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*DbPath))
    {
        if (!PlatformFile.CreateDirectory(*DbPath))
        {
            UE_LOG(LogTemp, Error, TEXT("Could not create db directory"));
            FMessageDialog::Open(EAppMsgType::Ok, FText::Format(NSLOCTEXT("DayZTools", "CreateDirError", "Could not create directory: {0}"), FText::FromString(DbPath)));
            return;
        }
    }

    // Set the full path for events.xml
    FString EventsXmlPath = FPaths::Combine(DbPath, TEXT("events.xml"));

    // Load all DynamicEvent assets
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    TArray<FAssetData> AssetData;
    FARFilter Filter;

    // Use ClassPaths instead of ClassNames for UE5 compatibility
    Filter.ClassPaths.Add(UDynamicEvent::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    if (AssetData.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("No Dynamic Event assets found"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "NoEventsError", "No Dynamic Event assets found to export"));
        return;
    }

    // Load all event assets
    TArray<UDynamicEvent*> AllEvents;
    for (const FAssetData& Asset : AssetData)
    {
        UDynamicEvent* Event = Cast<UDynamicEvent>(Asset.GetAsset());
        if (Event)
        {
            AllEvents.Add(Event);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Found %d Dynamic Event assets to export"), AllEvents.Num());

    // Build the XML content
    FString XmlContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<events>\n");

    for (UDynamicEvent* Event : AllEvents)
    {
        if (!Event)
        {
            continue;
        }

        // Get the XML for this event from the ExportToXML function
        FString EventXml = Event->ExportToXML();

        // Add indentation and newline
        EventXml = TEXT("    ") + EventXml.Replace(TEXT("\n"), TEXT("\n    "));

        // Add to the XML content
        XmlContent += EventXml + TEXT("\n");
    }

    // Close the events element
    XmlContent += TEXT("</events>\n");

    // Write the XML file
    if (FFileHelper::SaveStringToFile(XmlContent, *EventsXmlPath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully exported %d events to %s"), AllEvents.Num(), *EventsXmlPath);
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportEventsSuccess", "Successfully exported {0} events to:\n{1}"),
            FText::AsNumber(AllEvents.Num()),
            FText::FromString(EventsXmlPath)));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to write XML file"));
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "WriteEventsFileError", "Failed to write XML file to:\n{0}"),
            FText::FromString(EventsXmlPath)));
    }
}

void FLevelEditorToolbar::OnExportDynamicEventGroupDefinitions()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Dynamic Event Group Definitions to XML..."));

    // Get the mission path from settings
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    if (!Settings)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not access DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "SettingsError", "Could not access DayZ Tools settings"));
        return;
    }

    FString MissionPath = Settings->MissionPath;
    if (MissionPath.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Mission Path is not set in DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "MissionPathError", "Mission Path is not set in DayZ Tools settings. Please set it in Project Settings > Plugins > DayZ Tools."));
        return;
    }

    // Set the full path for events.xml
    FString EventsXmlPath = FPaths::Combine(MissionPath, TEXT("cfgeventgroups.xml"));

    // Load all DynamicEvent assets
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    TArray<FAssetData> AssetData;
    FARFilter Filter;

    // Use ClassPaths instead of ClassNames for UE5 compatibility
    Filter.ClassPaths.Add(UCEDynamicEventGroup::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    if (AssetData.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("No Dynamic Event Group assets found"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "NoEventGroupsError", "No Dynamic Event Group assets found to export"));
        return;
    }

    // Load all event assets
    TArray<UCEDynamicEventGroup*> AllEventGroups;
    for (const FAssetData& Asset : AssetData)
    {
        UCEDynamicEventGroup* EventGroup = Cast<UCEDynamicEventGroup>(Asset.GetAsset());
        if (EventGroup)
        {
            AllEventGroups.Add(EventGroup);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Found %d Dynamic Event group assets to export"), AllEventGroups.Num());

    // Build the XML content
    FString XmlContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<eventgroupdef>\n");

    for (UCEDynamicEventGroup* EventGroup : AllEventGroups)
    {
        if (!EventGroup)
        {
            continue;
        }

        // Get the XML for this event from the ExportToXML function
        FString EventXml = EventGroup->ExportToXML();

        // Add indentation and newline
        EventXml = TEXT("    ") + EventXml.Replace(TEXT("\n"), TEXT("\n    "));

        // Add to the XML content
        XmlContent += EventXml + TEXT("\n");
    }

    // Close the events element
    XmlContent += TEXT("</eventgroupdef>\n");

    // Write the XML file
    if (FFileHelper::SaveStringToFile(XmlContent, *EventsXmlPath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully exported %d event groups to %s"), AllEventGroups.Num(), *EventsXmlPath);
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportEventGroupsSuccess", "Successfully exported {0} event groups to:\n{1}"),
            FText::AsNumber(AllEventGroups.Num()),
            FText::FromString(EventsXmlPath)));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to write XML file"));
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "WriteEventsFileError", "Failed to write XML file to:\n{0}"),
            FText::FromString(EventsXmlPath)));
    }
}

void FLevelEditorToolbar::OnExportEventSpawns()
{
	UE_LOG(LogTemp, Log, TEXT("Exporting Dynamic Event Spawners..."));

	// Get the editor world
	UWorld* EditorWorld = GEditor->GetEditorWorldContext().World();
	if (!EditorWorld)
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to get editor world"));
		return;
	}

	// Find all Dynamic Event Spawner actors in the level
	TArray<AActor*> FoundActors;
	UGameplayStatics::GetAllActorsOfClass(EditorWorld, ADynamicEventSpawner::StaticClass(), FoundActors);

	// Check if we found any spawners
	if (FoundActors.Num() == 0)
	{
		UE_LOG(LogTemp, Warning, TEXT("No Dynamic Event Spawners found in the level"));
		return;
	}

	UE_LOG(LogTemp, Log, TEXT("Found %d Dynamic Event Spawners"), FoundActors.Num());

	// Create a map to group spawners by event asset
	TMap<FString, TArray<ADynamicEventSpawner*>> EventSpawnerMap;

	// Process each spawner
	for (AActor* Actor : FoundActors)
	{
		ADynamicEventSpawner* Spawner = Cast<ADynamicEventSpawner>(Actor);
		if (!Spawner)
		{
			continue;
		}

		// Get the event asset path
		FString EventAssetPath = Spawner->EventAsset.ToSoftObjectPath().ToString();
		if (EventAssetPath.IsEmpty())
		{
			UE_LOG(LogTemp, Warning, TEXT("Spawner %s has no event asset assigned"), *Spawner->GetName());
			continue;
		}

		// Extract the event name from the path
		FString EventName;
		if (EventAssetPath.Split(TEXT("/"), nullptr, &EventName, ESearchCase::IgnoreCase, ESearchDir::FromEnd))
		{
			// Remove the extension if present
			EventName.Split(TEXT("."), &EventName, nullptr, ESearchCase::IgnoreCase, ESearchDir::FromEnd);

			// Add the spawner to the map
			if (!EventSpawnerMap.Contains(EventName))
			{
				EventSpawnerMap.Add(EventName, TArray<ADynamicEventSpawner*>());
			}
			EventSpawnerMap[EventName].Add(Spawner);
		}
	}

	// Start building the XML output
	FString XmlOutput = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\" ?>\n");
	XmlOutput += TEXT("<eventposdef>\n");

	// For each event, list all spawner positions
	for (const TPair<FString, TArray<ADynamicEventSpawner*>>& Pair : EventSpawnerMap)
	{
		// Add event tag with name attribute
		XmlOutput += FString::Printf(TEXT("\t<event name=\"%s\">\n"), *Pair.Key);

		// Try to load the Dynamic Event asset to check for Spawn Zone settings
		if (Pair.Value.Num() > 0)
		{
			ADynamicEventSpawner* FirstSpawner = Pair.Value[0];
			if (!FirstSpawner->EventAsset.IsNull())
			{
				UDynamicEvent* DynamicEvent = FirstSpawner->EventAsset.LoadSynchronous();
				if (DynamicEvent && DynamicEvent->bSpawnZoneEnabled)
				{
					// Add the zone tag if enabled
					FString ZoneTag = FString::Printf(TEXT("\t\t<zone smin=\"%d\" smax=\"%d\" dmin=\"%d\" dmax=\"%d\" r=\"%d\" />\n"),
						DynamicEvent->SpawnZoneSMin,
						DynamicEvent->SpawnZoneSMax,
						DynamicEvent->SpawnZoneDMin,
						DynamicEvent->SpawnZoneDMax,
						DynamicEvent->SpawnZoneR);
					XmlOutput += ZoneTag;
				}
			}
		}

		for (ADynamicEventSpawner* Spawner : Pair.Value)
		{
			// Get the spawner's transform
			FVector Location = Spawner->GetActorLocation();
			FRotator Rotation = Spawner->GetActorRotation();

			// In DayZ, X is the same, Z is Unreal's Y, and Y is Unreal's Z
			// Convert from centimeters (Unreal) to meters (DayZ) by dividing by 100
			float DayZX = Location.Y / 100.0f;
			float DayZZ = Location.X / 100.0f; // DayZ Z is Unreal Y
			float DayZY = Location.Z / 100.0f; // DayZ Y is Unreal Z
			float Angle = Rotation.Yaw;

			// Format the position tag
			FString PosTag;
			FString GroupAttribute = TEXT(""); // Initialize empty group attribute string

			// Check if EventGroupAsset is valid
			if (!Spawner->EventGroupAsset.IsNull())
			{
				FString GroupAssetPath = Spawner->EventGroupAsset.ToSoftObjectPath().ToString();
				FString GroupAssetName = FPaths::GetBaseFilename(GroupAssetPath);
				if (!GroupAssetName.IsEmpty())
				{
					GroupAttribute = FString::Printf(TEXT(" group=\"%s\""), *GroupAssetName);
				}
			}

			if (Spawner->bSpawnOnTerrain)
			{
				// If spawn on terrain is checked, don't include the Y (height) coordinate
				PosTag = FString::Printf(TEXT("\t\t<pos x=\"%.3f\" z=\"%.3f\" a=\"%.1f\"%s/>\n"), DayZX, DayZZ, Angle, *GroupAttribute);
			}
			else
			{
				// Include all coordinates
				PosTag = FString::Printf(TEXT("\t\t<pos x=\"%.3f\" z=\"%.3f\" a=\"%.1f\" y=\"%.3f\"%s/>\n"), DayZX, DayZZ, Angle, DayZY, *GroupAttribute);
			}
			XmlOutput += PosTag;
		}

		// Close the event tag
		XmlOutput += TEXT("\t</event>\n");
	}

	// Close the root tag
	XmlOutput += TEXT("</eventposdef>");

	// Log the output
	UE_LOG(LogTemp, Log, TEXT("\n%s"), *XmlOutput);

	// Get Mission Path from settings
	const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
	FString MissionPath = Settings->MissionPath;

	if (MissionPath.IsEmpty())
	{
		UE_LOG(LogTemp, Error, TEXT("MissionPath is not set in DayZ Tools Settings. Cannot export cfgeventspawns.xml."));
		FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "ExportEventSpawnsError", "MissionPath is not set in DayZ Tools Settings. Cannot export cfgeventspawns.xml."));
		return;
	}

	// Ensure the directory exists
	IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
	if (!PlatformFile.DirectoryExists(*MissionPath))
	{
		PlatformFile.CreateDirectoryTree(*MissionPath);
	}

	// Construct the full file path
	FString FilePath = FPaths::Combine(MissionPath, TEXT("cfgeventspawns.xml"));

	// Save the XML content to the file
	if (FFileHelper::SaveStringToFile(XmlOutput, *FilePath))
	{
		UE_LOG(LogTemp, Log, TEXT("Successfully exported event spawns to: %s"), *FilePath);
		FMessageDialog::Open(EAppMsgType::Ok, FText::Format(NSLOCTEXT("DayZTools", "ExportEventSpawnsSuccess", "Successfully exported event spawns to:\n{0}"), FText::FromString(FilePath)));
	}
	else
	{
		UE_LOG(LogTemp, Error, TEXT("Failed to export event spawns to: %s"), *FilePath);
		FMessageDialog::Open(EAppMsgType::Ok, FText::Format(NSLOCTEXT("DayZTools", "ExportEventSpawnsFailed", "Failed to export event spawns to:\n{0}"), FText::FromString(FilePath)));
	}
}

//----------------------------------------------
//  World Export functions
//----------------------------------------------
void FLevelEditorToolbar::OnExportTypesDefinitions()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Types Definitions to XML..."));

    // Get the mission path from settings
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    if (!Settings)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not access DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "SettingsError", "Could not access DayZ Tools settings"));
        return;
    }

    FString MissionPath = Settings->MissionPath;
    if (MissionPath.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Mission Path is not set in DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "MissionPathError", "Mission Path is not set in DayZ Tools settings. Please set it in Project Settings > Plugins > DayZ Tools."));
        return;
    }

    // Create the db directory if it doesn't exist
    FString DbPath = FPaths::Combine(MissionPath, TEXT("db"));
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*DbPath))
    {
        if (!PlatformFile.CreateDirectory(*DbPath))
        {
            UE_LOG(LogTemp, Error, TEXT("Could not create db directory"));
            FMessageDialog::Open(EAppMsgType::Ok, FText::Format(NSLOCTEXT("DayZTools", "CreateDirError", "Could not create directory: {0}"), FText::FromString(DbPath)));
            return;
        }
    }

    // Set the full path for types.xml
    FString TypesXmlPath = FPaths::Combine(DbPath, TEXT("types.xml"));

    // Load all CEType assets
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    TArray<FAssetData> AssetData;
    FARFilter Filter;

    // Use ClassPaths instead of ClassNames for UE5 compatibility
    Filter.ClassPaths.Add(UCEType::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    if (AssetData.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("No CE Type assets found"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "NoTypesError", "No CE Type assets found to export"));
        return;
    }

    // Load all types
    TArray<UCEType*> AllTypes;
    for (const FAssetData& Asset : AssetData)
    {
        UCEType* Type = Cast<UCEType>(Asset.GetAsset());
        if (Type)
        {
            AllTypes.Add(Type);
        }
    }

    // Build the XML content
    FString XmlContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<types>\n");

    for (UCEType* Type : AllTypes)
    {
        if (!Type)
        {
            continue;
        }

        // Use the ExportToXML function
        FString TypeXml = Type->ExportToXML();

        // Add indentation and newline
        TypeXml = TEXT("    ") + TypeXml.Replace(TEXT("\n"), TEXT("\n    "));

        // Add to the XML content
        XmlContent += TypeXml + TEXT("\n");
    }

    // Close the types element
    XmlContent += TEXT("</types>\n");

    // Write the XML file
    if (FFileHelper::SaveStringToFile(XmlContent, *TypesXmlPath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully exported %d types to %s"), AllTypes.Num(), *TypesXmlPath);
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportSuccess", "Successfully exported {0} types to:\n{1}"),
            FText::AsNumber(AllTypes.Num()),
            FText::FromString(TypesXmlPath)));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to write XML file"));
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "WriteFileError", "Failed to write XML file to:\n{0}"),
            FText::FromString(TypesXmlPath)));
    }
}

void FLevelEditorToolbar::OnExportAllWorld()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting all World data..."));

    // Get the current world
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get editor world"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "ExportAllWorldError", "Failed to get editor world."));
        return;
    }

    // Get the WRP file path from settings or use a default
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString WRPFilePath;

    if (Settings && !Settings->DefaultWRPPath.IsEmpty())
    {
        WRPFilePath = Settings->DefaultWRPPath;
    }
    else
    {
        // Default path if not set in settings
        WRPFilePath = TEXT("D:/Projects/DayZ/Ember/Ember_Trunk/EZ/worlds/ember/ember.wrp");
        UE_LOG(LogTemp, Warning, TEXT("DefaultWRPPath not set in settings, using default: %s"), *WRPFilePath);
    }

    // Export all terrain data
    bool bSuccess = true;

    // Export heightmap
    if (!ExportTerrainHeightmapImpl(World, WRPFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain heightmap"));
        bSuccess = false;
    }

    // Export layers
    if (!ExportTerrainLayersImpl(World, WRPFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain layers"));
        bSuccess = false;
    }

    // Export objects
    if (!ExportTerrainObjectsImpl(World, WRPFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain objects"));
        bSuccess = false;
    }

    // Export surface mask
    FString SurfaceMaskPath = TEXT("D:/test_terrain/");
    if (!ExportSurfaceMask(World, SurfaceMaskPath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export surface mask"));
        bSuccess = false;
    }

    if (bSuccess)
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportAllWorldComplete", "All World data has been successfully exported to:\n{0}"),
            FText::FromString(WRPFilePath)));
    }
    else
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportAllWorldPartialFail", "Some parts of the World data export failed. Check the log for details.\nExport path: {0}"),
            FText::FromString(WRPFilePath)));
    }
}

void FLevelEditorToolbar::OnExportTerrainHeightmap()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Terrain Heightmap..."));

    // Get the current world
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get editor world"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "ExportTerrainHeightmapError", "Failed to get editor world."));
        return;
    }

    // Get the WRP file path from settings or use a default
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString WRPFilePath;

    if (Settings && !Settings->DefaultWRPPath.IsEmpty())
    {
        WRPFilePath = Settings->DefaultWRPPath;
    }
    else
    {
        // Default path if not set in settings
        WRPFilePath = TEXT("D:/Projects/DayZ/Ember/Ember_Trunk/EZ/worlds/ember/ember.wrp");
        UE_LOG(LogTemp, Warning, TEXT("DefaultWRPPath not set in settings, using default: %s"), *WRPFilePath);
    }

    // Call the implementation function
    if (ExportTerrainHeightmapImpl(World, WRPFilePath))
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportTerrainHeightmapSuccess", "Successfully exported terrain heightmap to:\n{0}"),
            FText::FromString(WRPFilePath)));
    }
    else
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportTerrainHeightmapFailed", "Failed to export terrain heightmap to:\n{0}"),
            FText::FromString(WRPFilePath)));
    }
}

void FLevelEditorToolbar::OnExportTerrainLayers()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Terrain Layers..."));

    // Get the current world
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get editor world"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "ExportTerrainLayersError", "Failed to get editor world."));
        return;
    }

    // Get the WRP file path from settings or use a default
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString WRPFilePath;

    if (Settings && !Settings->DefaultWRPPath.IsEmpty())
    {
        WRPFilePath = Settings->DefaultWRPPath;
    }
    else
    {
        // Default path if not set in settings
        WRPFilePath = TEXT("D:/Projects/DayZ/Ember/Ember_Trunk/EZ/worlds/ember/ember.wrp");
        UE_LOG(LogTemp, Warning, TEXT("DefaultWRPPath not set in settings, using default: %s"), *WRPFilePath);
    }

    // Call the implementation function
    if (ExportTerrainLayersImpl(World, WRPFilePath))
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportTerrainLayersSuccess", "Successfully exported terrain layers to:\n{0}"),
            FText::FromString(WRPFilePath)));
    }
    else
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportTerrainLayersFailed", "Failed to export terrain layers to:\n{0}"),
            FText::FromString(WRPFilePath)));
    }
}

void FLevelEditorToolbar::OnExportTerrainObjects()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Terrain Objects..."));

    // Get the current world
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get editor world"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "ExportTerrainObjectsError", "Failed to get editor world."));
        return;
    }

    // Get the WRP file path from settings or use a default
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString WRPFilePath;

    if (Settings && !Settings->DefaultWRPPath.IsEmpty())
    {
        WRPFilePath = Settings->DefaultWRPPath;
    }
    else
    {
        // Default path if not set in settings
        WRPFilePath = TEXT("D:/Projects/DayZ/Ember/Ember_Trunk/EZ/worlds/ember/ember.wrp");
        UE_LOG(LogTemp, Warning, TEXT("DefaultWRPPath not set in settings, using default: %s"), *WRPFilePath);
    }

    // Call the implementation function
    if (ExportTerrainObjectsImpl(World, WRPFilePath))
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportTerrainObjectsSuccess", "Successfully exported terrain objects to:\n{0}"),
            FText::FromString(WRPFilePath)));
    }
    else
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportTerrainObjectsFailed", "Failed to export terrain objects to:\n{0}"),
            FText::FromString(WRPFilePath)));
    }
}

void FLevelEditorToolbar::OnExportSurfaceMask()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Surface Mask..."));

    // Get the current world
    UWorld* World = GEditor->GetEditorWorldContext().World();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get editor world"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "ExportSurfaceMaskError", "Failed to get editor world."));
        return;
    }

	FString FullMapName = World->GetMapName();
    FString WorldShortName = FPackageName::GetShortName(FullMapName);

    // Default output path
	FString OutputPath =FString::Printf(TEXT("D:/Projects/DayZ/Ember/Ember_Raw/EZ/worlds/%s/textures/surface_mask"), *WorldShortName);

    // Show "processing" message
    FScopedSlowTask SlowTask(100.0f, NSLOCTEXT("DayZTools", "ExportingMask", "Exporting Surface Mask..."));
    SlowTask.MakeDialog();
    SlowTask.EnterProgressFrame(50.0f);

    // Call the implementation function
    bool bSuccess = ExportSurfaceMask(World, OutputPath);
    
    // Give editor time to cleanup resources
    SlowTask.EnterProgressFrame(40.0f, NSLOCTEXT("DayZTools", "CleaningUp", "Cleaning up GPU resources..."));
    
    // Force additional cleanup
    FlushRenderingCommands();
    if (GEngine)
    {
        GEngine->ForceGarbageCollection(true);
    }
    
    // Final progress update
    SlowTask.EnterProgressFrame(10.0f);

    // Show result message
    if (bSuccess)
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportSurfaceMaskSuccess", "Successfully exported surface mask to:\n{0}"),
            FText::FromString(OutputPath + TEXT("terrain_surface_mask.png"))));
    }
    else
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportSurfaceMaskFailed", "Failed to export surface mask to:\n{0}"),
            FText::FromString(OutputPath)));
    }
    
    // Extra cleanup step after dialog closes
    GEditor->RedrawLevelEditingViewports();
    FlushRenderingCommands();
}

void FLevelEditorToolbar::OnExportLootPoints()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Loot Points to XML..."));

    // Get the mission path from settings
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    if (!Settings)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not access DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "SettingsError", "Could not access DayZ Tools settings"));
        return;
    }

    FString MissionPath = Settings->MissionPath;
    if (MissionPath.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Mission Path is not set in DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "MissionPathError", "Mission Path is not set in DayZ Tools settings. Please set it in Project Settings > Plugins > DayZ Tools."));
        return;
    }

    // Set the full path for mapgroupproto.xml
    FString MapGroupProtoXmlPath = FPaths::Combine(MissionPath, TEXT("mapgroupproto.xml"));

    // Load all CELootPoints assets
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    TArray<FAssetData> AssetData;
    FARFilter Filter;

    // Use ClassPaths instead of ClassNames for UE5 compatibility
    Filter.ClassPaths.Add(UCELootPoints::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    if (AssetData.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("No Loot Points assets found"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "NoLootPointsError", "No Loot Points assets found to export"));
        return;
    }

    // Load all loot points assets
    TArray<UCELootPoints*> AllLootPoints;
    for (const FAssetData& Asset : AssetData)
    {
        UCELootPoints* LootPoints = Cast<UCELootPoints>(Asset.GetAsset());
        if (LootPoints)
        {
            AllLootPoints.Add(LootPoints);
        }
    }

    UE_LOG(LogTemp, Log, TEXT("Found %d Loot Points assets to export"), AllLootPoints.Num());

    // Build the XML content with defaults section
    FString XmlContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n<prototype>\n");

    // Add defaults section
    XmlContent += TEXT("\t<defaults>\n");
    XmlContent += TEXT("\t\t<default name=\"group\" lootmax=\"6\" />\n");
    XmlContent += TEXT("\t\t<default name=\"container\" lootmax=\"4\" />\n");
    XmlContent += TEXT("\t\t<default name=\"keepInvalidPoints\" enabled=\"yes\" />\n");
    XmlContent += TEXT("\t\t<default name=\"clusterMatrix\" de=\"TrajectoryApple\" width=\"12\" height=\"12\"/>\n");
    XmlContent += TEXT("\t\t<default name=\"clusterMatrix\" de=\"TrajectoryStones\" width=\"12\" height=\"12\"/>\n");
    XmlContent += TEXT("\t\t<default name=\"clusterMatrix\" de=\"TrajectoryConiferous\" width=\"12\" height=\"12\"/>\n");
    XmlContent += TEXT("\t\t<default name=\"clusterMatrix\" de=\"TrajectoryDeciduous\" width=\"12\" height=\"12\"/>\n");
    XmlContent += TEXT("\t\t<default name=\"clusterMatrix\" de=\"TrajectoryHumus\" width=\"12\" height=\"12\"/>\n");
    XmlContent += TEXT("\t\t<default name=\"clusterMatrix\" de=\"TrajectoryPear\" width=\"12\" height=\"12\"/>\n");
    XmlContent += TEXT("\t\t<default name=\"clusterMatrix\" de=\"TrajectoryPlum\" width=\"12\" height=\"12\"/>\n");
    XmlContent += TEXT("\t</defaults>\n");

    for (UCELootPoints* LootPoints : AllLootPoints)
    {
        if (!LootPoints)
        {
            continue;
        }

        // Get the XML for this loot points from the ExportToXMLWithAllProperties function
        FString LootPointsXml = LootPoints->ExportToXMLWithAllProperties();

        // Log the XML output for debugging
        UE_LOG(LogTemp, Warning, TEXT("OnExportLootPoints - XML output for %s:\n%s"), *LootPoints->GetName(), *LootPointsXml);

        // Add to the XML content without modifying indentation
        XmlContent += LootPointsXml + TEXT("\n");
    }

    // Close the prototype element
    XmlContent += TEXT("</prototype>\n");

    // Log the final XML content
    UE_LOG(LogTemp, Warning, TEXT("OnExportLootPoints - Final XML content:\n%s"), *XmlContent);

    // Write the XML file
    if (FFileHelper::SaveStringToFile(XmlContent, *MapGroupProtoXmlPath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully exported %d loot points to %s"), AllLootPoints.Num(), *MapGroupProtoXmlPath);
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportLootPointsSuccess", "Successfully exported {0} loot points to:\n{1}"),
            FText::AsNumber(AllLootPoints.Num()),
            FText::FromString(MapGroupProtoXmlPath)));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to write XML file"));
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "WriteLootPointsFileError", "Failed to write XML file to:\n{0}"),
            FText::FromString(MapGroupProtoXmlPath)));
    }
}

void FLevelEditorToolbar::OnExportServerMessages()
{
    UE_LOG(LogTemp, Log, TEXT("Exporting Server Messages to XML..."));

    // Get the mission path from settings
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    if (!Settings)
    {
        UE_LOG(LogTemp, Error, TEXT("Could not access DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "SettingsError", "Could not access DayZ Tools settings"));
        return;
    }

    FString MissionPath = Settings->MissionPath;
    if (MissionPath.IsEmpty())
    {
        UE_LOG(LogTemp, Error, TEXT("Mission Path is not set in DayZ Tools settings"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "MissionPathError", "Mission Path is not set in DayZ Tools settings. Please set it in Project Settings > Plugins > DayZ Tools."));
        return;
    }

	// Create the db directory if it doesn't exist
    FString DbPath = FPaths::Combine(MissionPath, TEXT("db"));
    IPlatformFile& PlatformFile = FPlatformFileManager::Get().GetPlatformFile();
    if (!PlatformFile.DirectoryExists(*DbPath))
    {
        if (!PlatformFile.CreateDirectory(*DbPath))
        {
            UE_LOG(LogTemp, Error, TEXT("Could not create db directory"));
            FMessageDialog::Open(EAppMsgType::Ok, FText::Format(NSLOCTEXT("DayZTools", "CreateDirError", "Could not create directory: {0}"), FText::FromString(DbPath)));
            return;
        }
    }

    // Load all ServerMessages assets
    FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");
    TArray<FAssetData> AssetData;
    FARFilter Filter;

    // Use ClassPaths for UE5 compatibility
    Filter.ClassPaths.Add(UCEServerMessages::StaticClass()->GetClassPathName());
    Filter.bRecursiveClasses = true;
    AssetRegistryModule.Get().GetAssets(Filter, AssetData);

    if (AssetData.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("No Server Messages assets found"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "NoServerMessagesError", "No Server Messages assets found to export"));
        return;
    }

    // Use the first Server Messages asset found
    UCEServerMessages* ServerMessages = Cast<UCEServerMessages>(AssetData[0].GetAsset());
    if (!ServerMessages)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load Server Messages asset"));
        FMessageDialog::Open(EAppMsgType::Ok, NSLOCTEXT("DayZTools", "LoadServerMessagesError", "Failed to load Server Messages asset"));
        return;
    }

    // Generate XML content
    FString XmlContent = TEXT("<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n");
    XmlContent += ServerMessages->ExportToXML();

    // Set the full path for messages.xml
    FString MessagesXmlPath = FPaths::Combine(DbPath, TEXT("messages.xml"));

    // Write the XML file
    if (FFileHelper::SaveStringToFile(XmlContent, *MessagesXmlPath, FFileHelper::EEncodingOptions::ForceUTF8WithoutBOM))
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully exported Server Messages to %s"), *MessagesXmlPath);
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "ExportServerMessagesSuccess", "Successfully exported Server Messages to:\n{0}"),
            FText::FromString(MessagesXmlPath)));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to write XML file"));
        FMessageDialog::Open(EAppMsgType::Ok, FText::Format(
            NSLOCTEXT("DayZTools", "WriteServerMessagesFileError", "Failed to write XML file to:\n{0}"),
            FText::FromString(MessagesXmlPath)));
    }
}

#undef LOCTEXT_NAMESPACE