#include "MiniLZO.h"
#include <cassert>

// A small helper for ctz (count trailing zeros). 
// The C# code uses a De<PERSON><PERSON><PERSON><PERSON> approach. We'll replicate a simpler function here.
static inline int ctz32(uint32_t x)
{
#if defined(_MSC_VER)
    unsigned long idx;
    _BitScanForward(&idx, x);
    return (int)idx;
#elif defined(__GNUC__) || defined(__clang__)
    return __builtin_ctz(x);
#else
    // fallback
    if (x == 0) return 32;
    int r = 0;
    while ((x & 1) == 0) { x >>= 1; r++; }
    return r;
#endif
}

// The "core" compression routine used by lzo1x_1_compress
static uint32_t lzo1x_1_compress_core(const uint8_t* inBuf, uint32_t inLen,
    uint8_t* outBuf, uint32_t& outLen, uint32_t ti,
    uint16_t* dict)
{
    const uint8_t* ip = inBuf;
    uint8_t* op = outBuf;
    const uint8_t* inEnd = inBuf + inLen;
    const uint8_t* ipEnd = inBuf + inLen - 20;
    const uint8_t* ii = ip;

    outLen = 0;

    // minimal "hash function" approach with a 14-bit index
    auto DIndex = [&](uint32_t v) -> uint32_t
        {
            // this is roughly: (((v * 0x1824429d) >> (32-14)) & ((1<<14)-1))
            // truncated for c++.
            const uint32_t prime = 0x1824429d;
            uint64_t mul = (uint64_t)v * (uint64_t)prime;
            // shift right (32 - 14) = 18
            uint32_t d = (uint32_t)(mul >> 18);
            return d & ((1 << 14) - 1);
        };

    // Append a function to copy a big literal chunk
    auto COPY4 = [&](uint8_t*& dst, const uint8_t* src) {
        *(uint32_t*)dst = *(const uint32_t*)src;
        dst += 4;
        };

    while (true)
    {
        const uint8_t* mPos;
        uint32_t mOff;
        uint32_t mLen;

        literal:
        ip += 1 + ((ip - ii) >> 5);
    next:
        if (ip >= ipEnd) {
            break;
        }
        uint32_t dv = *(const uint32_t*)ip;
        uint32_t dindex = DIndex(dv);
        mPos = inBuf + dict[dindex];
        dict[dindex] = (uint16_t)(ip - inBuf);
        if (dv != *(const uint32_t*)mPos) {
            goto literal;
        }

        ii -= ti;
        ti = 0;

        // copy literal run
        {
            uint32_t t = (uint32_t)(ip - ii);
            if (t != 0)
            {
                if (t <= 3)
                {
                    op[-2] |= (uint8_t)t;
                    for (uint32_t n = 0; n < t; n++)
                        *op++ = *(ii + n);
                }
                else if (t <= 16)
                {
                    *op++ = (uint8_t)(t - 3);
                    // copy t bytes
                    uint32_t left = t;
                    const uint8_t* src = ii;
                    while (left >= 4)
                    {
                        COPY4(op, src);
                        src += 4;
                        left -= 4;
                    }
                    while (left--)
                        *op++ = *src++;
                }
                else
                {
                    if (t <= 18)
                        *op++ = (uint8_t)(t - 3);
                    else
                    {
                        uint32_t tt = t - 18;
                        *op++ = 0;
                        while (tt > 255)
                        {
                            tt -= 255;
                            *op++ = 0;
                        }
                        *op++ = (uint8_t)(tt);
                    }
                    // copy t bytes in 16-chunks
                    const uint8_t* src = ii;
                    uint32_t left = t;
                    while (left >= 16)
                    {
                        COPY4(op, src); COPY4(op, src);
                        COPY4(op, src); COPY4(op, src);
                        left -= 16;
                    }
                    while (left--)
                        *op++ = *src++;
                }
            }
        }

        // match length
        mLen = 4; // we already matched 4 bytes
        {
            // see how many more bytes match
            // stop if we approach ipEnd
            while ((ip + mLen + 4 <= ipEnd) &&
                (*(const uint32_t*)(ip + mLen) == *(const uint32_t*)(mPos + mLen))) {
                mLen += 4;
            }
            if (ip + mLen < ipEnd)
            {
                // we have a mismatch => partial
                // find exact mismatch by ctz
                uint32_t diff = *(const uint32_t*)(ip + mLen) ^ *(const uint32_t*)(mPos + mLen);
                if (diff != 0) {
                    int c = ctz32(diff) / 8;
                    mLen += c;
                }
            }
        }
        mOff = (uint32_t)(ip - mPos);
        ip += mLen;
        ii = ip;

        // encode offset+length
        if (mLen <= 8 && mOff <= 0x0800)
        {
            mOff -= 1;
            *op++ = (uint8_t)(((mLen - 1) << 5) | ((mOff & 7) << 2));
            *op++ = (uint8_t)(mOff >> 3);
        }
        else if (mOff <= 0x4000)
        {
            mOff -= 1;
            if (mLen <= 33)
                *op++ = (uint8_t)(32 | (mLen - 2));
            else
            {
                uint32_t ml2 = mLen - 33;
                *op++ = 32;
                while (ml2 > 255)
                {
                    ml2 -= 255;
                    *op++ = 0;
                }
                *op++ = (uint8_t)ml2;
            }
            *op++ = (uint8_t)(mOff << 2);
            *op++ = (uint8_t)(mOff >> 6);
        }
        else
        {
            mOff -= 0x4000;
            if (mLen <= 9)
            {
                *op++ = (uint8_t)(16 | ((mOff >> 11) & 8) | (mLen - 2));
            }
            else
            {
                uint32_t ml3 = mLen - 9;
                *op++ = (uint8_t)(16 | ((mOff >> 11) & 8));
                while (ml3 > 255)
                {
                    ml3 -= 255;
                    *op++ = 0;
                }
                *op++ = (uint8_t)ml3;
            }
            *op++ = (uint8_t)(mOff << 2);
            *op++ = (uint8_t)(mOff >> 6);
        }
        goto next;
    }

    // final length
    outLen = (uint32_t)(op - outBuf);
    return (uint32_t)(inEnd - (ii - ti));
}


// ---------------------------------------------------------------------------
// Updated lzo1x_1_compress that mirrors the chunk-based approach from the C# code
// ---------------------------------------------------------------------------
int lzo1x_1_compress(const uint8_t* inBuf, uint32_t inLen,
    uint8_t* outBuf, uint32_t& outLen,
    uint8_t* wrkmem)
{
    // We'll accumulate our output in 'op' and keep track of total out bytes in 'finalOutLen'
    uint8_t* op = outBuf;
    const uint8_t* ip = inBuf;

    // We'll track 'l' as the number of unprocessed bytes, and 't' for leftover-literal
    uint32_t l = inLen;
    uint32_t t = 0;          // same 't' usage as C#
    uint32_t finalOutLen = 0;  // total size in outBuf so far

    // We'll repeatedly process chunks of up to 49152 bytes, just like the C# loop:
    //    while (l > 20) { chunk = min(l, 49152); zero dictionary; call core; update t/op/l; }
    while (l > 20)
    {
        uint32_t ll = (l <= 49152) ? l : 49152;   // chunk size
        uintptr_t ll_end = (uintptr_t)ip + ll;

        // (Optional) replicate the exact C# boundary check:
        // if ((ll_end + ((t + ll) >> 5)) <= ll_end ||
        //     (ip + ll) >= some_end_condition) {
        //     break;
        // }

        // Zero the dictionary for each chunk
        std::memset(wrkmem, 0, (1 << 14) * sizeof(uint16_t));
        uint16_t* dict = (uint16_t*)wrkmem;

        // We'll compress this chunk with lzo1x_1_compress_core
        uint32_t outLenPart = 0;  // chunk output
        t = lzo1x_1_compress_core(ip, ll, op, outLenPart, t, dict);

        ip += ll;         // advance input pointer by chunk size
        op += outLenPart; // advance output pointer by how much we wrote
        l -= ll;          // reduce the total input left
        finalOutLen += outLenPart;
    }

    // After chunking, the leftover 'l' bytes are handled by adding them to 't'
    // (the "literal run" leftover) and writing them out if needed:
    t += l;   // accumulate leftover
    l = 0;    // we've consumed them logically

    if (t > 0)
    {
        // The leftover logic is identical to the final block in the C# code
        const uint8_t* ii = inBuf + inLen - t;  // pointer to the leftover start
        if (op == outBuf && t <= 238)
        {
            // If we've written nothing so far (op == outBuf), and leftover <= 238
            *op++ = (uint8_t)(17 + t);
            finalOutLen += 1;
        }
        else if (t <= 3)
        {
            // Increase the previous token
            // (op[-2] must exist, so hopefully some output is there)
            op[-2] = (uint8_t)(op[-2] + t);
        }
        else if (t <= 18)
        {
            *op++ = (uint8_t)(t - 3);
            finalOutLen++;
        }
        else
        {
            uint32_t tt = t - 18;
            *op++ = 0;
            finalOutLen++;
            while (tt > 255)
            {
                tt -= 255;
                *op++ = 0;
                finalOutLen++;
            }
            *op++ = (uint8_t)tt;
            finalOutLen++;
        }
        // Now copy the leftover bytes
        for (uint32_t i = 0; i < t; i++)
        {
            *op++ = *(ii + i);
        }
        finalOutLen += t;
    }

    // Write the 3-byte terminator [16|1, 0, 0]
    *op++ = (uint8_t)(16 | 1);
    *op++ = 0;
    *op++ = 0;
    finalOutLen += 3;

    // The final compressed length is the offset from outBuf
    outLen = finalOutLen;
    return 0; // success
}

int lzo1x_decompress(const uint8_t* inBuf, uint32_t inLen,
    uint8_t* outBuf, uint32_t& outLen)
{
    // This code is basically the c# "lzo1x_decompress" function.
    // We'll replicate it in c++.
    const uint8_t* ip = inBuf;
    const uint8_t* ipEnd = inBuf + inLen;
    uint8_t* op = outBuf;
    uint8_t* opStart = outBuf;
    uint8_t* opEnd = outBuf + outLen; // to check for overruns

    auto COPY4 = [&](uint8_t*& dst, const uint8_t*& src) {
        *(uint32_t*)dst = *(const uint32_t*)src;
        dst += 4; src += 4;
        };

    auto copy_match = [&](uint8_t*& opp, const uint8_t*& mp, uint32_t& T) {
        *opp++ = *mp++;
        *opp++ = *mp++;
        while (T-- > 0) { *opp++ = *mp++; }
        };

    auto match_next = [&](uint8_t*& opp, const uint8_t*& ipp, uint32_t& T) {
        while (T-- > 0) { *opp++ = *ipp++; }
        T = *ipp++;
        };

    if (inLen == 0)
    {
        outLen = 0;
        return 0;
    }

    bool gt_first_literal_run = false;
    bool gt_match_done = false;

    uint32_t t;
    if (*ip > 17)
    {
        t = (uint32_t)(*ip++ - 17);
        if (t < 4)
        {
            match_next(op, ip, t);
        }
        else
        {
            // copy t bytes
            while (t-- > 0) { *op++ = *ip++; }
            gt_first_literal_run = true;
        }
    }

    for (;;)
    {
        if (gt_first_literal_run)
        {
            gt_first_literal_run = false;
            t = *ip++;
            if (t >= 16) goto match;
            // copy t + 3 bytes
            if (t == 0)
            {
                while (*ip == 0)
                {
                    t += 255;
                    ip++;
                }
                t += (uint32_t)(15 + *ip++);
            }
            // copy 4 bytes into op
            if ((opEnd - op) < (ptrdiff_t)(t + 4)) { /* overrun check, omitted */ }
            COPY4(op, ip);
            t -= 4;
            if (t > 0)
            {
                if (t >= 4)
                {
                    do
                    {
                        COPY4(op, ip);
                        t -= 4;
                    } while (t >= 4);
                    while (t-- > 0) *op++ = *ip++;
                }
                else
                {
                    while (t-- > 0) { *op++ = *ip++; }
                }
            }
            t = *ip++;
        }

        if (ip >= ipEnd) break;
        if (t >= 16) {
        match:
            // handle matches
            for (;;)
            {
                const uint8_t* mPos;
                if (t >= 64)
                {
                    mPos = op - 1;
                    mPos -= (t >> 2) & 7;
                    mPos -= (*ip++) << 3;
                    t = (t >> 5) - 1;
                    if ((opEnd - op) < (ptrdiff_t)(t + 2)) { /* overrun check*/ }
                    copy_match(op, mPos, t);
                }
                else if (t >= 32)
                {
                    t &= 31;
                    if (t == 0)
                    {
                        while (*ip == 0)
                        {
                            t += 255;
                            ip++;
                        }
                        t += (uint32_t)(31 + *ip++);
                    }
                    const uint32_t off = (*(const uint16_t*)ip) >> 2;
                    ip += 2;
                    mPos = op - 1 - off;
                    if ((opEnd - op) < (ptrdiff_t)(t + 2)) { /* overrun check*/ }
                    if ((op - mPos) < 4) { /* check for overlap*/ }
                    if (t >= 2 * 4 - (3 - 1) && (op - mPos) >= 4)
                    {
                        // copy in 4-byte chunks
                        *(uint32_t*)op = *(const uint32_t*)mPos; op += 4; mPos += 4; t -= 4 - (3 - 1);
                        while (t >= 4)
                        {
                            *(uint32_t*)op = *(const uint32_t*)mPos; op += 4; mPos += 4; t -= 4;
                        }
                        while (t-- > 0) *op++ = *mPos++;
                    }
                    else
                    {
                        *op++ = *mPos++; *op++ = *mPos++;
                        while (t-- > 0) *op++ = *mPos++;
                    }
                }
                else if (t >= 16)
                {
                    mPos = op;
                    mPos -= (t & 8) << 11;
                    t &= 7;
                    if (t == 0)
                    {
                        while (*ip == 0)
                        {
                            t += 255;
                            ip++;
                        }
                        t += (uint32_t)(7 + *ip++);
                    }
                    const uint32_t off = (*(const uint16_t*)ip) >> 2;
                    ip += 2;
                    mPos -= off;
                    if (mPos == op)
                    {
                        // might be end
                        goto _eof_found;
                    }
                    mPos -= 0x4000;
                    if ((opEnd - op) < (ptrdiff_t)(t + 2)) { /* check*/ }
                    if (t >= 2 * 4 - (3 - 1) && (op - mPos) >= 4)
                    {
                        *(uint32_t*)op = *(const uint32_t*)mPos; op += 4; mPos += 4; t -= 4 - (3 - 1);
                        while (t >= 4)
                        {
                            *(uint32_t*)op = *(const uint32_t*)mPos; op += 4; mPos += 4; t -= 4;
                        }
                        while (t-- > 0) *op++ = *mPos++;
                    }
                    else
                    {
                        *op++ = *mPos++;
                        *op++ = *mPos++;
                        while (t-- > 0) *op++ = *mPos++;
                    }
                }
                else
                {
                    // t < 16
                    mPos = op - 1;
                    mPos -= (t >> 2);
                    mPos -= (*ip++) << 2;
                    *op++ = *mPos++;
                    *op++ = *mPos;
                }

                // match_done:
                t = (uint32_t)(ip[-2] & 3);
                if (t == 0)
                    break;
                // match_next:
                if ((opEnd - op) < (ptrdiff_t)t) { /* check*/ }
                *op++ = *ip++;
                if (t > 1) {
                    *op++ = *ip++;
                    if (t > 2)
                        *op++ = *ip++;
                }
                t = *ip++;
            }
        }
        else
        {
            if (t == 0)
            {
                while (*ip == 0)
                {
                    t += 255;
                    ip++;
                }
                t += (uint32_t)(15 + *ip++);
            }
            if ((opEnd - op) < (ptrdiff_t)(t + 3)) { /* check*/ }
            // copy 4 bytes
            COPY4(op, ip);
            t -= 4;
            if (t > 0)
            {
                if (t >= 4)
                {
                    do
                    {
                        COPY4(op, ip);
                        t -= 4;
                    } while (t >= 4);
                    while (t-- > 0) *op++ = *ip++;
                }
                else
                {
                    while (t-- > 0) *op++ = *ip++;
                }
            }
            t = *ip++;
            goto match;
        }
    }

_eof_found:;
    uint32_t written = (uint32_t)(op - opStart);
    outLen = written;
    // success if ip == ipEnd, else partial
    if (ip == ipEnd) {
        return 0;
    }
    else if (ip < ipEnd) {
        // leftover data
        return -8;
    }
    return -4; // overshoot
}
