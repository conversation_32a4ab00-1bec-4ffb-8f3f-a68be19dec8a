#include "Mipmap.h"
#include "MiniLZO.h"
#include <stdexcept>
#include <algorithm>

Mipmap::Mipmap(FBinaryReader& input, int offset)
    : m_Offset(offset)
    , m_IsLZOCompressed(false)
    , m_HasMagicLZW(false)
{
    m_Width = input.ReadUInt16();
    m_Height = input.ReadUInt16();

    if (m_Width == MAGIC_LZW_W && m_Height == MAGIC_LZW_H)
    {
        m_HasMagicLZW = true;
        m_Width = input.ReadUInt16();
        m_Height = input.ReadUInt16();
    }

    if ((m_Width & 0x8000) != 0)
    {
        m_Width &= 0x7FFF;
        m_IsLZOCompressed = true;
    }

    m_DataSize = input.ReadUInt24();
    m_DataOffset = static_cast<int>(input.GetPosition());
    input.SetPosition(input.GetPosition() + m_DataSize); // skip data
}

Mipmap::Mipmap(uint16_t width, uint16_t height, const std::vector<uint8_t>& data, bool useLZOCompression)
    : m_Offset(0)
    , m_DataOffset(0)
    , m_IsLZOCompressed(useLZOCompression)
    , m_HasMagicLZW(false)
    , m_Width(width)
    , m_Height(height)
    , m_DataSize(static_cast<uint32_t>(data.size()))
    , m_Data(data)
{
}

std::vector<uint8_t> Mipmap::GetRawPixelData(FBinaryReader& input, PAAType type) const
{
    input.SetPosition(m_DataOffset);
    uint32_t expectedSize = static_cast<uint32_t>(m_Width) * m_Height;

    switch (type)
    {
    case PAAType::AI88:
    case PAAType::RGBA_5551:
    case PAAType::RGBA_4444:
        expectedSize *= 2;
        return input.ReadLZSS(expectedSize, true);

    case PAAType::P8:
        return !m_HasMagicLZW
            ? input.ReadCompressedIndices(static_cast<int32_t>(m_DataSize), expectedSize)
            : input.ReadLZSS(expectedSize, true);

    case PAAType::RGBA_8888:
        expectedSize *= 4;
        return input.ReadLZSS(expectedSize, true);

    case PAAType::DXT1:
        expectedSize /= 2;
        // Fall through to DXT2 case

    case PAAType::DXT2:
    case PAAType::DXT3:
    case PAAType::DXT4:
    case PAAType::DXT5:
        return !m_IsLZOCompressed
            ? input.ReadBytes(static_cast<int32_t>(m_DataSize))
            : input.ReadLZO(expectedSize);

    default:
        throw std::invalid_argument("Unexpected PAA type");
    }
}

void Mipmap::SetRawPixelData(const std::vector<uint8_t>& data, bool useLZOCompression)
{
    m_Data = data;
    m_DataSize = static_cast<uint32_t>(data.size());
    m_IsLZOCompressed = useLZOCompression;
}

void Mipmap::Write(FBinaryWriter& output, PAAType type, int& offset)
{
    m_Offset = static_cast<int>(output.GetPosition());

    // Write width (with compression flag if needed)
    uint16_t widthToWrite = m_Width;
    if (m_IsLZOCompressed)
    {
        widthToWrite |= 0x8000; // Set the high bit to indicate LZO compression
    }
    output.WriteUInt16(widthToWrite);

    // Write height
    output.WriteUInt16(m_Height);

    // Compress the data if needed
    std::vector<uint8_t> dataToWrite;

    if (m_IsLZOCompressed && type >= PAAType::DXT1 && type <= PAAType::DXT5)
    {
        // Compress DXT data with LZO
        dataToWrite = CompressWithLZO(m_Data);
    }
    else if (type == PAAType::AI88 || type == PAAType::RGBA_5551 || type == PAAType::RGBA_4444 ||
             type == PAAType::RGBA_8888 || (type == PAAType::P8 && m_HasMagicLZW))
    {
        // Compress with LZSS
        dataToWrite = CompressWithLZSS(m_Data);
    }
    else if (type == PAAType::P8 && !m_HasMagicLZW)
    {
        // Compress with special P8 compression
        dataToWrite = CompressP8(m_Data);
    }
    else
    {
        // No compression
        dataToWrite = m_Data;
    }

    // Write data size (24-bit)
    uint32_t dataSize = static_cast<uint32_t>(dataToWrite.size());
    output.WriteUInt24(dataSize);

    // Record the data offset
    m_DataOffset = static_cast<int>(output.GetPosition());

    // Write the data
    output.WriteBytes(dataToWrite);

    // Update the offset for the next mipmap
    offset = static_cast<int>(output.GetPosition());
}

// Helper method to compress data with LZO
std::vector<uint8_t> Mipmap::CompressWithLZO(const std::vector<uint8_t>& data)
{
    if (data.empty())
        return data;

    try
    {
        // Allocate a buffer for the compressed data
        // LZO compression can expand data in worst case, so allocate more space
        std::vector<uint8_t> compressedData(data.size() + data.size() / 16 + 64 + 3);

        // Allocate working memory for LZO
        std::vector<uint8_t> workMem((1 << 14) * sizeof(uint16_t));

        // Compress the data
        uint32_t compressedSize = 0;
        int result = lzo1x_1_compress(
            data.data(),
            static_cast<uint32_t>(data.size()),
            compressedData.data(),
            compressedSize,
            workMem.data()
        );

        if (result != 0 || compressedSize == 0)
        {
            // Compression failed, return the original data
            UE_LOG(LogTemp, Warning, TEXT("LZO compression failed with result %d, using uncompressed data"), result);
            return data;
        }

        // Resize the output buffer to the actual compressed size
        compressedData.resize(compressedSize);

        // If the compressed data is larger than the original, return the original
        if (compressedSize >= data.size())
        {
            UE_LOG(LogTemp, Verbose, TEXT("LZO compression resulted in larger data (%d vs %d), using uncompressed data"),
                compressedSize, data.size());
            return data;
        }

        UE_LOG(LogTemp, Verbose, TEXT("LZO compression successful: %d bytes -> %d bytes (%.1f%%)"),
            data.size(), compressedSize, (float)compressedSize * 100.0f / (float)data.size());
        return compressedData;
    }
    catch (const std::exception& e)
    {
        UE_LOG(LogTemp, Error, TEXT("Exception in CompressWithLZO: %s"), UTF8_TO_TCHAR(e.what()));
        return data;
    }
    catch (...)
    {
        UE_LOG(LogTemp, Error, TEXT("Unknown exception in CompressWithLZO"));
        return data;
    }
}

// Helper method to compress data with LZSS
std::vector<uint8_t> Mipmap::CompressWithLZSS(const std::vector<uint8_t>& data)
{
    // For PAA files, we'll use LZO compression instead of LZSS
    // This is because the PAA format typically uses LZO for all compression
    // and the LZSS implementation is more complex
    return CompressWithLZO(data);
}

// Helper method to compress P8 data
std::vector<uint8_t> Mipmap::CompressP8(const std::vector<uint8_t>& data)
{
    // Since we're focusing on DXT5 format, we'll use a simplified approach for P8
    // In a real implementation, this would use a specialized P8 compression algorithm
    return CompressWithLZO(data);
}