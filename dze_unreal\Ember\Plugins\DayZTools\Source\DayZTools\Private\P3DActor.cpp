#include "P3DActor.h"
#include "Components/SceneComponent.h"
#include "Components/StaticMeshComponent.h" // Include StaticMeshComponent

AP3DActor::AP3DActor()
{
    // Disable ticking to improve performance
    PrimaryActorTick.bCanEverTick = false;

    // Initialize custom properties
    P3DPath = TEXT("");
    bAutoCenter = false;
    ObjectCenter = FVector(0.0f, 0.0f, 0.0f);

    // Initialize Proxy Settings
    bIncludeInLOD1 = true;
    bIncludeInLOD2 = false;
    bIncludeInLOD3 = false;
    bIncludeInLOD4 = false;
    bIncludeInLOD5 = false;
    bIncludeInLOD6 = false;
    bIncludeInLOD7 = false;
    bIncludeInLOD8 = false;
    bIncludeInGeometry = false;
    bIncludeInMemory = false;
    bIncludeInRoadWay = false;
    bIncludeInPaths = false;
    bIncludeInViewGeometry = false;
    bIncludeInFireGeometry = false;
}

void AP3DActor::SetStaticMesh(UStaticMesh* NewMesh)
{
    // Set or update your static mesh here. For example:
    // if (auto* StaticMeshComp = FindComponentByClass<UStaticMeshComponent>())
    // {
    //     StaticMeshComp->SetStaticMesh(NewMesh);
    // }
}
