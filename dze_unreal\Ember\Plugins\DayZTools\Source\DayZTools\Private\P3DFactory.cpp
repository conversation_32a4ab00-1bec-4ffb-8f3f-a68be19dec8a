#include "P3DFactory.h"
#include "P3DActor.h"
#include "P3DBlueprint.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "Misc/MessageDialog.h"

UP3DFactory::UP3DFactory()
{
    bCreateNew = true;
    bEditAfterNew = true;
    SupportedClass = UP3DBlueprint::StaticClass();

    ParentClass = AP3DActor::StaticClass();
}

bool UP3DFactory::ConfigureProperties()
{
    return true;
}

UObject* UP3DFactory::FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    if (!ParentClass || !FKismetEditorUtilities::CanCreateBlueprintOfClass(ParentClass))
    {
        FMessageDialog::Open(EAppMsgType::Ok, FText::FromString(TEXT("Cannot create a Blueprint based on the specified class.")));
        return nullptr;
    }

    // Create the Blueprint using UP3DBlueprint class
    UP3DBlueprint* NewBlueprint = Cast<UP3DBlueprint>(FKismetEditorUtilities::CreateBlueprint(
        ParentClass,
        InParent,
        Name,
        BPTYPE_Normal,
        UP3DBlueprint::StaticClass(),
        UBlueprintGeneratedClass::StaticClass(),
        "P3DFactory"
    ));

    return NewBlueprint;
}
