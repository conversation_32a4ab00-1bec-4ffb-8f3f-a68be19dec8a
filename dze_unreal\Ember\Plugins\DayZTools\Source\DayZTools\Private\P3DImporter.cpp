#include "P3DImporter.h"
#include "P3DActor.h"
#include "P3DFactory.h"
#include "Kismet2/KismetEditorUtilities.h"
#include "EditorAssetLibrary.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "Editor/UnrealEd/Public/Editor.h" // Ensure access to editor utilities
#include "Engine/BlueprintGeneratedClass.h"
#include "Engine/StaticMesh.h"
#include "StaticMeshAttributes.h"
#include "MeshDescription.h"
#include "Materials/MaterialInterface.h"
#include "MaterialDomain.h"
#include "MaterialShared.h"
#include "Materials/Material.h"
#include "Materials/MaterialRenderProxy.h"
#include "EditorFramework/AssetImportData.h"
#include "DayZToolsSettings.h"
#include "Materials/MaterialInstanceConstant.h"
#include "HAL/FileManager.h"
#include "Misc/Paths.h"
#include "StaticMeshResources.h"
#include "StaticMeshDescription.h"
#include "Engine/Texture.h"
#include "Engine/TextureDefines.h"
#include "PackageTools.h"
#include "P3DBlueprint.h"
#include "UObject/Package.h"
#include "Components/SceneComponent.h"
#include "GameFramework/Actor.h"
#include "Misc/ScopedSlowTask.h"
#include "Editor/ContentBrowser/Public/ContentBrowserModule.h"
#include "Editor/ContentBrowser/Private/SContentBrowser.h"
#include "ContentBrowserModule.h"
#include "AssetRegistry/AssetRegistryModule.h"

#include "Engine/SimpleConstructionScript.h"
#include "Engine/SCS_Node.h"
#include "Kismet2/BlueprintEditorUtils.h"

#include "P3DData.h"



UP3DImporter::UP3DImporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UStaticMesh::StaticClass();
    Formats.Add(TEXT("p3d;P3D Model File"));
    bCreateNew = false;
    bEditAfterNew = true;
    bEditorImport = true;
    bText = false;
}


UObject* UP3DImporter::FactoryCreateFile(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags,
    const FString& Filename, const TCHAR* Parms,
    FFeedbackContext* Warn, bool& bOutOperationCanceled)
{
    CurrentFilename = Filename;
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *Filename))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load file: %s"), *Filename);
        return nullptr;
    }

    const uint8* Buffer = FileData.GetData();
    const uint8* BufferEnd = Buffer + FileData.Num();

    return FactoryCreateBinary(InClass, InParent, InName, Flags, nullptr, Filename.GetCharArray().GetData(), Buffer, BufferEnd, Warn);
}

bool WaitForMeshCompilation(UStaticMesh* Mesh, float TimeoutSeconds = 5.0f)
{
    if (!Mesh)
        return false;

    double StartTime = FPlatformTime::Seconds();
    while (Mesh->IsCompiling())
    {
        FPlatformProcess::Sleep(0.1f);

        // Check for timeout
        if ((FPlatformTime::Seconds() - StartTime) > TimeoutSeconds)
        {
            UE_LOG(LogTemp, Warning, TEXT("Timeout waiting for mesh %s to compile"), *Mesh->GetName());
            return false;
        }
    }
    return true;
}

UP3DImporter::P3DData UP3DImporter::LoadP3DDataOnly(const FString& FilePath, bool& bSuccess)
{
    bSuccess = false;

    // 1) Load file into a byte array
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *FilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to read P3D file from disk: %s"), *FilePath);
        return P3DData();
    }

    // 2) Convert TArray to pointers
    const uint8* Buffer = FileData.GetData();
    const uint8* BufferEnd = Buffer + FileData.Num();

    // 3) Set up any relevant variables
    this->CurrentFilename = FilePath;

    // 4) Parse the data using your existing function
    P3DData ParsedData = ReadP3DFile(Buffer, BufferEnd);

    // Check if it�s valid
    if (ParsedData.LODs.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("No LODs found in %s"), *FilePath);
    }
    else
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully parsed %s - LOD count: %d"), *FilePath, ParsedData.LODs.Num());
    }

    bSuccess = true;
    return ParsedData;
}



UObject* UP3DImporter::FactoryCreateBinary(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, UObject* Context, const TCHAR* Type, const uint8*& Buffer, const uint8* BufferEnd, FFeedbackContext* Warn)
{
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString TrunkPath = FPaths::ConvertRelativePathToFull(Settings->ProjectTrunkPath);
    FString FullFilePathNormalized = NormalizePath(CurrentFilename);
    FString TrunkPathNormalized = NormalizePath(Settings->ProjectTrunkPath);

    if (FullFilePathNormalized.StartsWith(TEXT("P:/")))
    {
        FullFilePathNormalized = FullFilePathNormalized.Replace(TEXT("P:/"), *TrunkPathNormalized);
    }

    FullFilePathNormalized = FullFilePathNormalized.TrimEnd();
    TrunkPathNormalized = TrunkPathNormalized.TrimEnd();

    FullFilePathNormalized = FullFilePathNormalized.Replace(TEXT("\\"), TEXT("/"));
    TrunkPathNormalized = TrunkPathNormalized.Replace(TEXT("\\"), TEXT("/"));

    //UE_LOG(LogTemp, Warning, TEXT("FullFilePathNormalized: %s"), *FullFilePathNormalized);
    //UE_LOG(LogTemp, Warning, TEXT("TrunkPathNormalized: %s"), *TrunkPathNormalized);

    FString RelativeAssetPath;
    if (FullFilePathNormalized.StartsWith(TrunkPathNormalized))
    {
        RelativeAssetPath = FullFilePathNormalized.RightChop(TrunkPathNormalized.Len());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("FullFilePath does not start with TrunkPath"));
        RelativeAssetPath = FPaths::GetCleanFilename(FullFilePathNormalized);
    }

    RelativeAssetPath = RelativeAssetPath.TrimStartAndEnd();
    if (RelativeAssetPath.StartsWith(TEXT("/")))
    {
        RelativeAssetPath = RelativeAssetPath.RightChop(1);
    }

    //UE_LOG(LogTemp, Warning, TEXT("Computed RelativeAssetPath: %s"), *RelativeAssetPath);
    FString ContentPath = FString::Printf(TEXT("/Game/%s"), *FPaths::GetPath(RelativeAssetPath));
    ContentPath = ContentPath.TrimEnd();
    if (ContentPath.EndsWith(TEXT("/")))
    {
        ContentPath = ContentPath.LeftChop(1);
    }

    ContentPath = UPackageTools::SanitizePackageName(ContentPath);

    FString ContentRelativePath = ContentPath.RightChop(5); // Remove "/Game" from the start
    FString P3DFilename = FPaths::GetCleanFilename(CurrentFilename);
    FString ContentRelativeP3DPath = FPaths::Combine(ContentRelativePath, P3DFilename);

    ContentRelativeP3DPath = ContentRelativeP3DPath.Replace(TEXT("\\"), TEXT("/"));

    if (ContentRelativeP3DPath.StartsWith(TEXT("/")))
    {
        ContentRelativeP3DPath = ContentRelativeP3DPath.RightChop(1);
    }

    //UE_LOG(LogTemp, Warning, TEXT("ContentRelativeP3DPath: %s"), *ContentRelativeP3DPath);
    UE_LOG(LogTemp, Warning, TEXT("Final ContentPath: %s"), *ContentPath);

    if (!IFileManager::Get().DirectoryExists(*ContentPath))
    {
        IFileManager::Get().MakeDirectory(*ContentPath, true);
    }

    FString DataFolderPath = ContentPath + "/data";
    if (!IFileManager::Get().DirectoryExists(*DataFolderPath))
    {
        IFileManager::Get().MakeDirectory(*DataFolderPath, true);
    }

    FString PackageName = DataFolderPath + "/" + FPaths::GetBaseFilename(RelativeAssetPath);
    PackageName = UPackageTools::SanitizePackageName(PackageName);

    UPackage* FinalPackage = CreatePackage(*PackageName);
    if (!FinalPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create or find the package: %s"), *PackageName);
        return nullptr;
    }

    InParent = FinalPackage;

    P3DData P3DData = ReadP3DFile(Buffer, BufferEnd);
    UE_LOG(LogTemp, Warning, TEXT("Read P3D file. Number of LODs: %d"), P3DData.LODs.Num());

    if (P3DData.LODs.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("No valid LODs found in P3D file"));
        return nullptr;
    }

    TopLOD = P3DData.LODs[0];

    // ---------------------------
    // 1. Compute and Shift Vertices
    // ---------------------------

    bool bShouldAutoCenter = true; // Default to true if property not found

    for (const auto& LODPair : P3DData.LODs)
    {
        const LOD& lod = LODPair.Value;

        if (lod.Properties.Contains("autocenter"))
        {
            FString AutoCenterValue = lod.Properties["autocenter"];
            bShouldAutoCenter = (AutoCenterValue != "0");
            //UE_LOG(LogTemp, Warning, TEXT("Autocenter property found: %s"), *AutoCenterValue);
        }
    }

    if (bShouldAutoCenter)
    {
        FBox MeshBounds = CalculateCombinedBounds(P3DData);
        FVector MeshCenter = MeshBounds.GetCenter();
        ProxyCenter = MeshCenter;
        ObjectCenter = MeshCenter;
        //UE_LOG(LogTemp, Warning, TEXT("Mesh Center: %s"), *MeshCenter.ToString());
        //UE_LOG(LogTemp, Warning, TEXT("Auto-centering enabled. Shifting vertices."));
        for (FVector& Vertex : TopLOD.Vertices)
        {
            Vertex -= MeshCenter;
        }
    }
    else
    {
        ProxyCenter.X = 0.0f;
        ProxyCenter.Y = 0.0f;
        ProxyCenter.Z = 0.0f;

        ObjectCenter.X = 0.0f;
        ObjectCenter.Y = 0.0f;
        ObjectCenter.Z = 0.0f;

        UE_LOG(LogTemp, Warning, TEXT("Auto-centering disabled or no vertices to shift."));
    }

    //UE_LOG(LogTemp, Warning, TEXT("UVSet 0 UV Count: %d"), TopLOD.UVSets[0].Num());
    for (int32 UVSetIndex = 1; UVSetIndex < TopLOD.UVSets.Num(); ++UVSetIndex)
    {
        //UE_LOG(LogTemp, Warning, TEXT("UVSet %d UV Count: %d"), UVSetIndex, TopLOD.UVSets[UVSetIndex].Num());
    }

    FScopedTransaction Transaction(NSLOCTEXT("UnrealEd", "ImportP3D", "Import P3D"));

    StaticMesh = NewObject<UStaticMesh>(
        InParent, InClass, *FPaths::GetBaseFilename(RelativeAssetPath), Flags);
    if (!StaticMesh)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create StaticMesh object"));
        return nullptr;
    }
    this->StaticMesh = StaticMesh;

    StaticMesh->PreEditChange(nullptr);
    StaticMesh->bAutoComputeLODScreenSize = false;
    StaticMesh->SetNumSourceModels(0);
    CreatedMaterials.Empty();
    TMap<FString, UTexture*> CreatedTextures;

    for (const auto& LODPair : P3DData.LODs)
    {
        const LOD& CurrentLOD = LODPair.Value;
        for (const FString& Path : CurrentLOD.Materials)
        {
            FString CleanPath = Path.Replace(TEXT("\\"), TEXT("/"));
            if (CleanPath.StartsWith("#"))
            {
                //UE_LOG(LogTemp, Warning, TEXT("Skipping texture import: Path starts with '#' (%s)"), *CleanPath);
                continue;
            }
            if (FPaths::GetExtension(CleanPath).ToLower() == TEXT("paa") && !CreatedTextures.Contains(CleanPath))
            {
                // Uncomment and implement texture import as needed
                /*
                UTexture* Texture = ImportTexture(CleanPath, InParent);
                if (Texture)
                {
                    CreatedTextures.Add(CleanPath, Texture);
                    UE_LOG(LogTemp, Warning, TEXT("Imported texture: %s"), *CleanPath);
                }
                else
                {
                    UE_LOG(LogTemp, Warning, TEXT("Failed to import texture: %s. Creating placeholder texture."), *CleanPath);
                    // Create a placeholder texture if necessary
                }
                */
            }
        }
    }

    for (auto& LODPair : P3DData.LODs)
    {
        ResolutionLODs.Add(LODPair.Key, &LODPair.Value);
    }

    if (!ResolutionLODs.Contains(0))
    {
        UE_LOG(LogTemp, Warning, TEXT("Top LOD not found in ResolutionLODs. Adding it manually."));
        ResolutionLODs.Add(0, &TopLOD);
    }

    TSet<FString> AllUsedMaterials;
    for (int32 i = 0; i < TopLOD.FaceMaterialIndices.Num(); ++i)
    {
        if (TopLOD.MaterialNames.IsValidIndex(TopLOD.FaceMaterialIndices[i]))
        {
            AllUsedMaterials.Add(TopLOD.MaterialNames[TopLOD.FaceMaterialIndices[i]]);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("Material index %d is out of bounds for TopLOD.MaterialNames."), TopLOD.FaceMaterialIndices[i]);
        }
    }

    TArray<FStaticMaterial> StaticMaterials;
    for (const FString& MaterialName : AllUsedMaterials)
    {
        if (MaterialName.IsEmpty())
        {
            //UE_LOG(LogTemp, Warning, TEXT("Encountered empty MaterialName. Skipping."));
            continue;
        }

        UMaterialInterface* Material = CreateOrRetrieveMaterial(MaterialName);
        if (Material)
        {
            FStaticMaterial StaticMaterial(Material, FName(*MaterialName));
            StaticMaterials.Add(StaticMaterial);
            UE_LOG(LogTemp, Warning, TEXT("Imported Material: %s"), *MaterialName);
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("Failed to retrieve or create material: %s"), *MaterialName);
        }
    }

    if (StaticMaterials.Num() > 0)
    {
        StaticMesh->SetStaticMaterials(StaticMaterials);
    }

    TMap<FString, int32> MaterialNameToIndexMap;
    for (int32 i = 0; i < StaticMaterials.Num(); ++i)
    {
        MaterialNameToIndexMap.Add(StaticMaterials[i].MaterialSlotName.ToString(), i);
    }

    {
        int32 LODIndex = 0; // Top LOD is index 0

        UE_LOG(LogTemp, Warning, TEXT("Processing Top LOD. Resolution: %f, Vertices: %d, Faces: %d, UV Sets: %d"),
            TopLOD.Resolution, TopLOD.Vertices.Num(), TopLOD.Faces.Num(), TopLOD.UVSets.Num());

        FStaticMeshSourceModel& SourceModel = StaticMesh->AddSourceModel();
        float ScreenSize = FMath::Clamp(1.0f / TopLOD.Resolution, 0.0f, 1.0f);
        SourceModel.ScreenSize.Default = ScreenSize;

        // Set build settings for this LOD
        FMeshBuildSettings& BuildSettings = SourceModel.BuildSettings;
        BuildSettings.bRecomputeNormals = true;
        BuildSettings.bRecomputeTangents = true;
        BuildSettings.bUseMikkTSpace = true;
        BuildSettings.bGenerateLightmapUVs = false;
        BuildSettings.bBuildReversedIndexBuffer = false;
        BuildSettings.bUseFullPrecisionUVs = true;

        FMeshDescription* MeshDescription = StaticMesh->CreateMeshDescription(LODIndex);
        if (MeshDescription == nullptr)
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to create MeshDescription for LOD %d"), LODIndex);
            return nullptr;
        }

        FStaticMeshAttributes Attributes(*MeshDescription);
        Attributes.Register();

        TVertexAttributesRef<FVector3f> VertexPositions = Attributes.GetVertexPositions();
        TVertexInstanceAttributesRef<FVector3f> VertexInstanceNormals = Attributes.GetVertexInstanceNormals();
        TVertexInstanceAttributesRef<FVector2f> VertexInstanceUVs = Attributes.GetVertexInstanceUVs();

        int32 NumUVSets = TopLOD.UVSets.Num();
        //UE_LOG(LogTemp, Warning, TEXT("Total Number of UV Sets: %d"), NumUVSets);
        VertexInstanceUVs.SetNumChannels(NumUVSets);

        const float ScaleFactor = 100.0f;

        TArray<FVertexID> VertexIDs;
        for (const FVector& Position : TopLOD.Vertices)
        {
            FVertexID VertexID = MeshDescription->CreateVertex();
            if (Position.ContainsNaN() || Position.IsNearlyZero())
            {
                UE_LOG(LogTemp, Error, TEXT("Invalid vertex data in LOD %d. Assigning default position."), LODIndex);
                VertexPositions[VertexID] = FVector3f(0.0f, 0.0f, 0.0f); // Assign a default position
            }
            else
            {
                VertexPositions[VertexID] = FVector3f(Position * ScaleFactor);
            }
            VertexIDs.Add(VertexID);
        }

        TMap<int32, FPolygonGroupID> FaceMaterialToPolygonGroupMap;
        for (int32 i = 0; i < TopLOD.MaterialNames.Num(); ++i)
        {
            const FString& MaterialName = TopLOD.MaterialNames[i];

            if (MaterialName.IsEmpty())
            {
                //UE_LOG(LogTemp, Warning, TEXT("Skipping empty material name at index %d in LOD %d"), i, LODIndex);
                continue;
            }

            FPolygonGroupID PolygonGroupID = MeshDescription->CreatePolygonGroup();
            Attributes.GetPolygonGroupMaterialSlotNames()[PolygonGroupID] = FName(*MaterialName);
            FaceMaterialToPolygonGroupMap.Add(i, PolygonGroupID);

            //UE_LOG(LogTemp, Log, TEXT("Created PolygonGroup for material %s at index %d in LOD %d"), *MaterialName, i, LODIndex);
        }

        TArray<int32> UVIndices;
        UVIndices.Init(0, NumUVSets);

        for (int32 FaceIdx = 0; FaceIdx < TopLOD.Faces.Num(); ++FaceIdx)
        {
            const Face& CurrentFace = TopLOD.Faces[FaceIdx];
            int32 NumSides = CurrentFace.NumSides;

            if (NumSides < 3 || NumSides > 4)
            {
                UE_LOG(LogTemp, Error, TEXT("Invalid number of sides %d for face %d"), NumSides, FaceIdx);
                continue;
            }

            if (!TopLOD.FaceMaterialIndices.IsValidIndex(FaceIdx))
            {
                UE_LOG(LogTemp, Error, TEXT("Invalid material index for face %d"), FaceIdx);
                continue;
            }
            int32 LODMaterialIndex = TopLOD.FaceMaterialIndices[FaceIdx];
            if (!FaceMaterialToPolygonGroupMap.Contains(LODMaterialIndex))
            {
                UE_LOG(LogTemp, Error, TEXT("PolygonGroup not found for material index %d in LOD %d"), LODMaterialIndex, LODIndex);
                continue;
            }

            FPolygonGroupID CurrentPolygonGroupID = FaceMaterialToPolygonGroupMap[LODMaterialIndex];

            TArray<FVertexInstanceID> PolygonVertexInstances;
            bool ShouldSkipFace = false;

            for (int32 VertexIdx = 0; VertexIdx < NumSides; ++VertexIdx)
            {
                int32 VertexIndex = CurrentFace.VertexIndices[VertexIdx];
                int32 NormalIndex = CurrentFace.NormalIndices[VertexIdx];

                if (!VertexIDs.IsValidIndex(VertexIndex))
                {
                    UE_LOG(LogTemp, Error, TEXT("Invalid vertex index %d in LOD %d"), VertexIndex, LODIndex);
                    ShouldSkipFace = true;
                    break;
                }

                FVertexID CurrentVertexID = VertexIDs[VertexIndex];
                FVertexInstanceID VertexInstanceID = MeshDescription->CreateVertexInstance(CurrentVertexID);

                for (int32 UVSetIndex = 0; UVSetIndex < NumUVSets; ++UVSetIndex)
                {
                    if (CurrentFace.UVsPerUVSet.Contains(UVSetIndex))
                    {
                        const TArray<FVector2D>& UVs = CurrentFace.UVsPerUVSet[UVSetIndex];
                        if (UVs.IsValidIndex(VertexIdx))
                        {
                            FVector2f UV = FVector2f(UVs[VertexIdx].X, UVs[VertexIdx].Y);
                            VertexInstanceUVs.Set(VertexInstanceID, UVSetIndex, UV);
                        }
                        else
                        {
                            UE_LOG(LogTemp, Warning, TEXT("Invalid UV index %d for UV set %d in face %d"), VertexIdx, UVSetIndex, FaceIdx);
                            VertexInstanceUVs.Set(VertexInstanceID, UVSetIndex, FVector2f(0.0f, 0.0f));
                        }
                    }
                    else
                    {
                        VertexInstanceUVs.Set(VertexInstanceID, UVSetIndex, FVector2f(0.0f, 0.0f));
                        UE_LOG(LogTemp, Warning, TEXT("UVSet %d not found for face %d"), UVSetIndex, FaceIdx);
                    }
                }

                if (TopLOD.Normals.IsValidIndex(NormalIndex))
                {
                    FVector3f Normal = FVector3f(TopLOD.Normals[NormalIndex]);
                    VertexInstanceNormals.Set(VertexInstanceID, Normal);
                }
                else
                {
                    UE_LOG(LogTemp, Warning, TEXT("Invalid normal index %d in LOD %d"), NormalIndex, LODIndex);
                }

                PolygonVertexInstances.Add(VertexInstanceID);
            }

            if (!ShouldSkipFace)
            {
                MeshDescription->CreatePolygon(CurrentPolygonGroupID, PolygonVertexInstances);
            }
        }

        StaticMesh->CommitMeshDescription(LODIndex);
        UE_LOG(LogTemp, Log, TEXT("Completed mesh construction for LOD %d"), LODIndex);
    }

    StaticMesh->SetLODGroup(NAME_None);
    StaticMesh->bAutoComputeLODScreenSize = false;
    StaticMesh->bAllowCPUAccess = true;
    StaticMesh->NeverStream = true;
    StaticMesh->ImportVersion = EImportStaticMeshVersion::LastVersion;
    StaticMesh->CreateBodySetup();
    StaticMesh->SetLightingGuid();
    StaticMesh->Build();

    if (StaticMesh->AssetImportData == nullptr)
    {
        StaticMesh->AssetImportData = NewObject<UAssetImportData>(StaticMesh, UAssetImportData::StaticClass());
    }
    StaticMesh->AssetImportData->Update(CurrentFilename);
    StaticMesh->PostEditChange();
    UE_LOG(LogTemp, Warning, TEXT("StaticMesh created with %d LOD(s)"), StaticMesh->GetNumSourceModels());

    UBlueprint* P3DPrefabBluerint = CreateP3DActorBlueprint(RelativeAssetPath, PackageName, StaticMesh, ContentRelativeP3DPath, P3DData);

    /////////////////////////
    //// SET UP PROXIES ////
    ///////////////////////

    TArray<FProxyData> ProxiesToProcess = TopLOD.ProxyData;
    ImportProxies(ProxiesToProcess, P3DData, P3DPrefabBluerint);

    GEditor->GetEditorSubsystem<UAssetEditorSubsystem>()->CloseAllAssetEditors();

    if (GEditor)
    {
        FContentBrowserModule& ContentBrowserModule = FModuleManager::LoadModuleChecked<FContentBrowserModule>("ContentBrowser");
        FAssetRegistryModule& AssetRegistryModule = FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");

        // Create the path and asset filter
        TArray<FString> PathsToScan;
        PathsToScan.Add(FPackageName::GetLongPackagePath(P3DPrefabBluerint->GetPathName()));

        FARFilter Filter;
        Filter.PackagePaths.Add(FName(*PathsToScan[0]));

        // Get and set the assets
        TArray<FAssetData> AssetDatas;
        AssetRegistryModule.Get().GetAssets(Filter, AssetDatas);

        // Force sync to our blueprint
        //ContentBrowserModule.Get().SyncBrowserToAssets(AssetDatas);
    }

    return StaticMesh;
}


UP3DImporter::P3DData UP3DImporter::ReadP3DFile(const uint8*& Buffer, const uint8* BufferEnd)
{
    P3DData Data;

    if (Buffer + 16 > BufferEnd)
    {
        UE_LOG(LogTemp, Error, TEXT("File is too small to contain valid header information"));
        return Data;
    }

    uint8 SignatureBytes[4];
    FMemory::Memcpy(SignatureBytes, Buffer, 4);
    Buffer += 4;

    if (SignatureBytes[0] != 'M' || SignatureBytes[1] != 'L' || SignatureBytes[2] != 'O' || SignatureBytes[3] != 'D')
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid P3D file format."));
        return Data;
    }

    uint32 Version = *((uint32*)Buffer);
    Buffer += 4;
    uint32 NumLODs = *((uint32*)Buffer);
    Buffer += 4;

    UE_LOG(LogTemp, Warning, TEXT("P3D Version: %u, Number of LODs: %u"), Version, NumLODs);

    for (uint32 i = 0; i < NumLODs; ++i)
    {
        LOD LodData = ReadLOD(Buffer, BufferEnd);

        Data.LODs.Add(i, LodData);
    }

    return Data;
}


UP3DImporter::LOD UP3DImporter::ReadLOD(const uint8*& Buffer, const uint8* BufferEnd)
{
    LOD LodData;
    if (Buffer + 4 > BufferEnd)
    {
        UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading LOD signature"));
        return LodData;
    }
    char Signature[5] = { 0 };
    FMemory::Memcpy(Signature, Buffer, 4);
    Buffer += 4;

    if (FString(Signature) != TEXT("P3DM"))
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid LOD format. Expected 'P3DM', got '%s'"), *FString(Signature));
        return LodData;
    }

    if (Buffer + 24 > BufferEnd)
    {
        UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading LOD header"));
        return LodData;
    }
    LodData.VersionMajor = *((uint32*)Buffer);
    Buffer += 4;
    LodData.VersionMinor = *((uint32*)Buffer);
    Buffer += 4;
    uint32 NumPoints = *((uint32*)Buffer);
    Buffer += 4;
    uint32 NumFaceNormals = *((uint32*)Buffer);
    Buffer += 4;
    uint32 NumFaces = *((uint32*)Buffer);
    Buffer += 4;
    uint32 Flags = *((uint32*)Buffer);
    Buffer += 4;

    UE_LOG(LogTemp, Warning, TEXT("LOD - Points: %u, Face Normals: %u, Faces: %u"), NumPoints, NumFaceNormals, NumFaces);

    // Read points
    for (uint32 i = 0; i < NumPoints; ++i)
    {
        FVector Position;
        Position.X = *((float*)Buffer);
        Buffer += 4;
        Position.Z = *((float*)Buffer);
        Buffer += 4;
        Position.Y = -(*((float*)Buffer)); // Negate Y to match Unreal's coordinate system
        Buffer += 4;
        uint32 PointFlags = *((uint32*)Buffer);
        Buffer += 4;
        LodData.Vertices.Add(Position);
    }

    UE_LOG(LogTemp, Warning, TEXT("Vertices read: %d"), LodData.Vertices.Num());

    // Read normals
    for (uint32 i = 0; i < NumFaceNormals; ++i)
    {
        FVector Normal;
        Normal.X = *((float*)Buffer);
        Buffer += 4;
        Normal.Z = *((float*)Buffer);
        Buffer += 4;
        Normal.Y = *((float*)Buffer);
        Buffer += 4;
        LodData.Normals.Add(Normal);
    }

    UE_LOG(LogTemp, Warning, TEXT("Normals read: %d"), LodData.Normals.Num());
    LodData.UVSets.Add(TArray<FVector2D>());

    // Read faces
    for (uint32 i = 0; i < NumFaces; ++i)
    {
        if (Buffer + 4 > BufferEnd)
        {
            UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading NumSides for face %u"), i);
            return LodData;
        }

        uint32 NumSides = *((uint32*)Buffer);
        Buffer += 4;

        if (NumSides == 0 || NumSides > 4)
        {
            UE_LOG(LogTemp, Error, TEXT("Invalid NumSides %u for face %u"), NumSides, i);
            return LodData;
        }

        Face CurrentFace;
        CurrentFace.NumSides = NumSides;
        CurrentFace.UVsPerUVSet.Add(0, TArray<FVector2D>()); // Initialize UV Set 0 for this face

        for (uint32 j = 0; j < NumSides; ++j)
        {
            if (Buffer + 16 > BufferEnd)
            {
                UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading vertex/normal/UV data for face %u, side %u"), i, j);
                return LodData;
            }

            int32 VertexIndex = *((int32*)Buffer);
            Buffer += 4;
            int32 NormalIndex = *((int32*)Buffer);
            Buffer += 4;
            float U = *((float*)Buffer);
            Buffer += 4;
            float V = *((float*)Buffer);
            Buffer += 4;

            CurrentFace.VertexIndices.Add(VertexIndex);
            CurrentFace.NormalIndices.Add(NormalIndex);
            CurrentFace.UVsPerUVSet[0].Add(FVector2D(U, V));
        }

        for (uint32 j = NumSides; j < 4; ++j)
        {
            if (Buffer + 16 > BufferEnd)
            {
                UE_LOG(LogTemp, Error, TEXT("Buffer overrun while skipping unused data for face %u"), i);
                return LodData;
            }
            Buffer += 16; // Skip vertex/normal/UV data
        }

        // Read FaceFlags
        if (Buffer + 4 > BufferEnd)
        {
            UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading FaceFlags for face %u"), i);
            return LodData;
        }
        uint32 FaceFlags = *((uint32*)Buffer);
        Buffer += 4;

        // Read TextureName and MaterialPath
        FString TextureName = ReadNullTerminatedString(Buffer, BufferEnd, 1024);
        FString MaterialPath = ReadNullTerminatedString(Buffer, BufferEnd, 1024);

        // Store material information
        LodData.Materials.Add(MaterialPath);  // Store the full material path
        LodData.Materials.Add(TextureName);   // Store the full texture path

        // Handle empty MaterialPath by assigning a default material name
        FString CleanMaterialName = FPaths::GetBaseFilename(MaterialPath);
        int32 MaterialIndex = LodData.MaterialNames.AddUnique(CleanMaterialName);
        LodData.FaceMaterialIndices.Add(MaterialIndex);

        // Store texture information for face textures
        if (!TextureName.IsEmpty() && !TextureName.StartsWith("#"))
        {
            // Add texture to a map of face textures for this material
            if (!LodData.FaceTextures.Contains(CleanMaterialName))
            {
                LodData.FaceTextures.Add(CleanMaterialName, TextureName);
            }
        }

        // Store face data
        LodData.Faces.Add(CurrentFace);
    }

    UE_LOG(LogTemp, Warning, TEXT("Faces read: %d, UVs read: %d"), LodData.Faces.Num(), LodData.UVSets[0].Num());

    // Read TAGG section
    if (Buffer + 4 <= BufferEnd)
    {
        char TaggSignature[5] = { 0 };
        FMemory::Memcpy(TaggSignature, Buffer, 4);
        Buffer += 4;

        if (FString(TaggSignature) == TEXT("TAGG"))
        {
            while (Buffer < BufferEnd)
            {
                if (Buffer + 1 > BufferEnd) break;
                uint8 TaggValid = *Buffer++;
                if (TaggValid == 0) break;

                FString TagName = ReadNullTerminatedString(Buffer, BufferEnd);

                if (Buffer + 4 > BufferEnd) break;
                uint32 TagSize = *((uint32*)Buffer);
                Buffer += 4;

                if (TagName == TEXT("#EndOfFile#"))
                {
                    break;
                }
                else if (TagName == TEXT("#Property#"))
                {
                    UE_LOG(LogTemp, Warning, TEXT("READING PROPERTY TAGGG size: %d"), TagSize);
                    if (TagSize == 128)
                    {
                        if (Buffer + 128 > BufferEnd)
                        {
                            UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading Property data"));
                            break;
                        }
                        char IdBuffer[64] = { 0 };
                        char ValueBuffer[64] = { 0 };
                        // Read ID
                        FMemory::Memcpy(IdBuffer, Buffer, 63);
                        Buffer += 64;
                        // Read Value
                        FMemory::Memcpy(ValueBuffer, Buffer, 63);
                        Buffer += 64;
                        FString Id = FString(ANSI_TO_TCHAR(IdBuffer)).TrimEnd();
                        FString Value = FString(ANSI_TO_TCHAR(ValueBuffer)).TrimEnd();
                        LodData.Properties.Add(Id, Value);
                        UE_LOG(LogTemp, Warning, TEXT("Read Property: %s = %s"), *Id, *Value);
                    }
                    else
                    {
                        UE_LOG(LogTemp, Warning, TEXT("Unexpected size for #Property# tag: %d"), TagSize);
                        if (Buffer + TagSize > BufferEnd)
                        {
                            UE_LOG(LogTemp, Error, TEXT("Buffer overrun while skipping unexpected Property data"));
                            break;
                        }
                        Buffer += TagSize; // Skip the unexpected data
                    }
                }
                else if (TagName == TEXT("#UVSet#"))
                {
                    if (Buffer + 4 > BufferEnd)
                    {
                        UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading UVSet index"));
                        break;
                    }

                    uint32 UVSetIndex = *((uint32*)Buffer);
                    Buffer += 4;
                    UE_LOG(LogTemp, Warning, TEXT("Reading UVSetIndex: %u"), UVSetIndex);

                    if (UVSetIndex >= static_cast<uint32>(LodData.UVSets.Num()))
                    {
                        LodData.UVSets.AddDefaulted(UVSetIndex - LodData.UVSets.Num() + 1);
                    }

                    int32 TotalFaceVertices = 0;
                    for (const Face& face : LodData.Faces)
                    {
                        TotalFaceVertices += face.NumSides;
                    }

                    for (int32 FaceIdx = 0; FaceIdx < LodData.Faces.Num(); ++FaceIdx)
                    {
                        Face& CurrentFace = LodData.Faces[FaceIdx];
                        for (uint32 VertexIdx = 0; VertexIdx < CurrentFace.NumSides; ++VertexIdx)
                        {
                            if (Buffer + 8 > BufferEnd)
                            {
                                UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading UV data for UVSet %u, Face %d, Vertex %d"), UVSetIndex, FaceIdx, VertexIdx);
                                break;
                            }

                            float U = *((float*)Buffer);
                            Buffer += 4;
                            float V = *((float*)Buffer);
                            Buffer += 4;

                            if (!CurrentFace.UVsPerUVSet.Contains(UVSetIndex))
                            {
                                CurrentFace.UVsPerUVSet.Add(UVSetIndex, TArray<FVector2D>());
                            }

                            CurrentFace.UVsPerUVSet[UVSetIndex].Add(FVector2D(U, V));
                        }
                    }

                    UE_LOG(LogTemp, Warning, TEXT("Additional UV set %d read and assigned to faces"), UVSetIndex);
                }

                else if (TagName.StartsWith(TEXT("proxy:"), ESearchCase::IgnoreCase))
                {
                    // Handle proxy faces
                    UE_LOG(LogTemp, Warning, TEXT("Reading proxy tag: %s"), *TagName);

                    // Read points
                    if (Buffer + NumPoints > BufferEnd)
                    {
                        UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading proxy points"));
                        break;
                    }

                    TArray<uint8> ProxyPoints;
                    ProxyPoints.SetNum(NumPoints);
                    FMemory::Memcpy(ProxyPoints.GetData(), Buffer, NumPoints);
                    Buffer += NumPoints;

                    TArray<int32> VertexIndices;
                    for (int32 i = 0; i < ProxyPoints.Num(); ++i)
                    {
                        if (ProxyPoints[i] == 1)
                        {
                            VertexIndices.Add(i);
                        }
                    }

                    // Verify we found exactly 3 vertices
                    if (VertexIndices.Num() != 3)
                    {
                        UE_LOG(LogTemp, Error, TEXT("Invalid number of vertices found: %d (expected 3)"), VertexIndices.Num());
                        break;
                    }

                    if (Buffer + NumFaces > BufferEnd)
                    {
                        UE_LOG(LogTemp, Error, TEXT("Buffer overrun while reading proxy faces"));
                        break;
                    }

                    TArray<uint8> ProxyFaces;
                    ProxyFaces.SetNum(NumFaces);
                    FMemory::Memcpy(ProxyFaces.GetData(), Buffer, NumFaces);
                    Buffer += NumFaces;

                    FVector A = LodData.Vertices[VertexIndices[0]];
                    FVector B = LodData.Vertices[VertexIndices[1]];
                    FVector C = LodData.Vertices[VertexIndices[2]];

                    // Calculate side lengths
                    float AB_Length = FVector::Distance(A, B);
                    float BC_Length = FVector::Distance(B, C);
                    float CA_Length = FVector::Distance(C, A);

                    //UE_LOG(LogTemp, Warning, TEXT("Side lengths - AB: %f, BC: %f, CA: %f"),AB_Length, BC_Length, CA_Length);

                    // Verify triangle inequality
                    if (AB_Length + BC_Length <= CA_Length ||
                        BC_Length + CA_Length <= AB_Length ||
                        CA_Length + AB_Length <= BC_Length)
                    {
                        UE_LOG(LogTemp, Error, TEXT("Invalid triangle: sides do not satisfy triangle inequality"));
                        UE_LOG(LogTemp, Error, TEXT("Sides: AB=%f, BC=%f, CA=%f"), AB_Length, BC_Length, CA_Length);
                        break;
                    }

                    float AngleA = UP3DImporter::CalculateAngle(B, C, A);
                    float AngleB = UP3DImporter::CalculateAngle(C, A, B);
                    float AngleC = UP3DImporter::CalculateAngle(A, B, C);

                    float AngleSum = AngleA + AngleB + AngleC;
                    const float ExpectedSum = 180.0f;
                    const float Tolerance = 0.1f;

                    if (!FMath::IsNearlyEqual(AngleSum, ExpectedSum, Tolerance))
                    {
                        UE_LOG(LogTemp, Warning, TEXT("Warning: Sum of angles (%f) differs from expected 180 degrees"),
                            AngleSum);
                    }

                    // Log the results
                    //UE_LOG(LogTemp, Log, TEXT("Triangle Angles:"));
                    //UE_LOG(LogTemp, Log, TEXT("Angle at A: %.2f degrees"), AngleA);
                    //UE_LOG(LogTemp, Log, TEXT("Angle at B: %.2f degrees"), AngleB);
                    //UE_LOG(LogTemp, Log, TEXT("Angle at C: %.2f degrees"), AngleC);
                    //UE_LOG(LogTemp, Log, TEXT("Sum of angles: %.2f degrees"), AngleSum);

                    FProxyData ProxyData;
                    ProxyData.Name = TagName;
                    ProxyData.Points = ProxyPoints;
                    ProxyData.Faces = ProxyFaces;

                    int32 Point1Index = 0;  // Index of the 90 degree angle point
                    int32 Point2Index = 1;  // Index of the closest point
                    int32 Point3Index = 2;  // Index of the remaining point
                    float ShortestDistance = MAX_FLT;

                    // First find the 90 degree point (Point 1)
                    if (FMath::IsNearlyEqual(AngleA, 90.0f, 5.0f))
                    {
                        Point1Index = 0;
                    }
                    else if (FMath::IsNearlyEqual(AngleB, 90.0f, 5.0f))
                    {
                        Point1Index = 1;
                    }
                    else if (FMath::IsNearlyEqual(AngleC, 90.0f, 5.0f))
                    {
                        Point1Index = 2;
                    }
                    else
                    {
                        UE_LOG(LogTemp, Warning, TEXT("No 90 degree angle found in triangle"));
                        // Handle error case or find the closest to 90 degrees
                        float DiffA = FMath::Abs(AngleA - 90.0f);
                        float DiffB = FMath::Abs(AngleB - 90.0f);
                        float DiffC = FMath::Abs(AngleC - 90.0f);

                        if (DiffA <= DiffB && DiffA <= DiffC)
                            Point1Index = 0;
                        else if (DiffB <= DiffA && DiffB <= DiffC)
                            Point1Index = 1;
                        else
                            Point1Index = 2;
                    }

                    TArray<FVector> Points = { A, B, C };
                    FVector Point1 = Points[Point1Index];

                    for (int32 i = 0; i < 3; ++i)
                    {
                        if (i != Point1Index)
                        {
                            float Distance = FVector::Distance(Point1, Points[i]);
                            if (Distance < ShortestDistance)
                            {
                                ShortestDistance = Distance;
                                Point2Index = i;
                            }
                        }
                    }

                    Point3Index = 3 - Point1Index - Point2Index;

                    FVector Point2 = Points[Point2Index];
                    FVector Point3 = Points[Point3Index];

                    // Base point (origin) for the proxy
                    // Previous code remains the same until rotation calculation part...

                    // Base point (origin) for the proxy
                    FVector Origin = Point1;

                    // Calculate the primary axes based on the triangle's orientation
                    FVector Forward = (Point2 - Point1).GetSafeNormal();  // This should align with Y axis
                    FVector Up = (Point3 - Point1).GetSafeNormal();       // This should align with Z axis

                    // Calculate Right vector and negate it to match the reference orientation
                    FVector Right = -FVector::CrossProduct(Forward, Up).GetSafeNormal();

                    // Negate Forward to achieve 180-degree X-axis rotation
                    Forward = -Forward;

                    // Ensure Up is perpendicular to Forward and Right
                    Up = FVector::CrossProduct(Right, Forward).GetSafeNormal();

                    // Create rotation matrix where:
                    // - Forward vector (Y axis) is the first edge of the triangle (negated)
                    // - Up vector (Z axis) is the second edge of the triangle
                    // - Right vector (negated X axis) is perpendicular to both
                    FMatrix RotationMatrix(
                        FPlane(Right.X, Right.Y, Right.Z, 0.0f),    // X axis (negated)
                        FPlane(Forward.X, Forward.Y, Forward.Z, 0.0f), // Y axis (negated)
                        FPlane(Up.X, Up.Y, Up.Z, 0.0f),      // Z axis
                        FPlane(0.0f, 0.0f, 0.0f, 1.0f)
                    );

                    // Convert rotation matrix to rotator
                    FRotator FinalRotation = RotationMatrix.Rotator();

                    // Normalize the rotator to ensure angles are in valid ranges
                    FinalRotation.Normalize();

                    // Set position and calculated rotation for the proxy
                    ProxyData.Position = Origin;
                    ProxyData.Rotation = FinalRotation;

                    // Debug output to verify the calculated rotation
                    UE_LOG(LogTemp, Log, TEXT("Rotation - Roll: %.2f, Pitch: %.2f, Yaw: %.2f"),
                        FinalRotation.Roll, FinalRotation.Pitch, FinalRotation.Yaw);

                    LodData.ProxyData.Add(ProxyData);
                }
                else
                {
                    // Skip unknown tags
                    if (Buffer + TagSize > BufferEnd)
                    {
                        UE_LOG(LogTemp, Error, TEXT("Buffer overrun while skipping unknown TAGG data"));
                        break;
                    }
                    Buffer += TagSize;
                }
            }
        }
    }

    // Read resolution
    if (Buffer + 4 <= BufferEnd)
    {
        LodData.Resolution = *((float*)Buffer);
        Buffer += 4;
    }

    UE_LOG(LogTemp, Warning, TEXT("LOD Resolution: %f"), LodData.Resolution);

    return LodData;
}


bool UP3DImporter::DoesSupportClass(UClass* Class)
{
    return (Class == UStaticMesh::StaticClass());
}


UClass* UP3DImporter::ResolveSupportedClass()
{
    return UStaticMesh::StaticClass();
}


bool UP3DImporter::CanReimport(UObject* Obj, TArray<FString>& OutFilenames)
{
    UStaticMesh* Mesh = Cast<UStaticMesh>(Obj);
    if (Mesh && Mesh->AssetImportData)
    {
        Mesh->AssetImportData->ExtractFilenames(OutFilenames);
        return true;
    }
    return false;
}


void UP3DImporter::SetReimportPaths(UObject* Obj, const TArray<FString>& NewReimportPaths)
{
    UStaticMesh* Mesh = Cast<UStaticMesh>(Obj);
    if (Mesh && ensure(NewReimportPaths.Num() == 1))
    {
        Mesh->AssetImportData->UpdateFilenameOnly(NewReimportPaths[0]);
    }
}


EReimportResult::Type UP3DImporter::Reimport(UObject* Obj)
{
    UStaticMesh* Mesh = Cast<UStaticMesh>(Obj);
    if (!Mesh)
    {
        return EReimportResult::Failed;
    }

    const FString Filename = Mesh->AssetImportData->GetFirstFilename();
    if (!Filename.Len() || !FPaths::FileExists(Filename))
    {
        return EReimportResult::Failed;
    }

    bool OutCanceled = false;
    if (ImportObject(Mesh->GetClass(), Mesh->GetOuter(), *Mesh->GetName(), RF_Public | RF_Standalone, Filename, nullptr, OutCanceled) != nullptr)
    {
        return EReimportResult::Succeeded;
    }

    return EReimportResult::Failed;
}


int32 UP3DImporter::GetPriority() const
{
    return ImportPriority;
}


//////////////////////////////////
/////// HELPER FUNCTIONS ////////
////////////////////////////////


float UP3DImporter::CalculateAngle(const FVector& Point1, const FVector& Point2, const FVector& Center)
{
    FVector Vec1 = (Point1 - Center);
    FVector Vec2 = (Point2 - Center);
    Vec1.Normalize();
    Vec2.Normalize();
    float DotProduct = FVector::DotProduct(Vec1, Vec2);
    DotProduct = FMath::Clamp(DotProduct, -1.0f, 1.0f);
    float AngleDegrees = FMath::RadiansToDegrees(FMath::Acos(DotProduct));
    //UE_LOG(LogTemp, Warning, TEXT("Angle Calculation Debug:"));
    //UE_LOG(LogTemp, Warning, TEXT("Vec1: %s"), *Vec1.ToString());
    //UE_LOG(LogTemp, Warning, TEXT("Vec2: %s"), *Vec2.ToString());
    //UE_LOG(LogTemp, Warning, TEXT("Dot Product: %f"), DotProduct);
    //UE_LOG(LogTemp, Warning, TEXT("Resulting Angle: %f"), AngleDegrees);

    return AngleDegrees;
}


URvmatAsset* UP3DImporter::ImportRvmatMaterial(const FString& RelativeMaterialPath, UObject* InParent)
{
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString TrunkPath = Settings->ProjectTrunkPath;

    // Ensure RelativeMaterialPath doesn't start with the TrunkPath
    FString CleanRelativePath = RelativeMaterialPath;
    if (CleanRelativePath.StartsWith(TrunkPath))
    {
        CleanRelativePath.RemoveFromStart(TrunkPath);
    }
    CleanRelativePath = CleanRelativePath.Replace(TEXT("\\"), TEXT("/")).TrimStartAndEnd();

    // Combine trunk path with relative material path for the full file path
    FString FullMaterialPath = FPaths::Combine(TrunkPath, CleanRelativePath);

    // Create the content path based on the relative path
    FString ContentPath = FString::Printf(TEXT("/Game/%s"), *FPaths::GetPath(CleanRelativePath));
    FString AssetName = FPaths::GetBaseFilename(CleanRelativePath);

    // Log paths for debugging
    //UE_LOG(LogTemp, Warning, TEXT("ImportRvmatMaterial - RelativePath: %s"), *CleanRelativePath);
    //UE_LOG(LogTemp, Warning, TEXT("ImportRvmatMaterial - FullPath: %s"), *FullMaterialPath);
    //UE_LOG(LogTemp, Warning, TEXT("ImportRvmatMaterial - ContentPath: %s"), *ContentPath);
    //UE_LOG(LogTemp, Warning, TEXT("ImportRvmatMaterial - AssetName: %s"), *AssetName);

    // Ensure the destination folder exists
    if (!UEditorAssetLibrary::DoesDirectoryExist(ContentPath))
    {
        UEditorAssetLibrary::MakeDirectory(ContentPath);
    }

    // Check if the RVMAT asset already exists to prevent re-import
    FString AssetPath = FString::Printf(TEXT("%s/%s.%s"), *ContentPath, *AssetName, *AssetName);
    URvmatAsset* ExistingRvmat = LoadObject<URvmatAsset>(nullptr, *AssetPath);
    if (ExistingRvmat)
    {
        return ExistingRvmat;
    }

    // If not, import the RVMAT
    FAssetToolsModule& AssetToolsModule = FModuleManager::GetModuleChecked<FAssetToolsModule>("AssetTools");
    TArray<FString> FileNames;
    FileNames.Add(FullMaterialPath);

    TArray<UObject*> ImportedAssets = AssetToolsModule.Get().ImportAssets(FileNames, ContentPath);

    if (ImportedAssets.Num() > 0)
    {
        URvmatAsset* ImportedRvmat = Cast<URvmatAsset>(ImportedAssets[0]);
        if (ImportedRvmat)
        {
            return ImportedRvmat;
        }
    }

    UE_LOG(LogTemp, Error, TEXT("Failed to import RVMAT: %s"), *FullMaterialPath);
    return nullptr;
}


UTexture* UP3DImporter::ImportTexture(const FString& RelativeTexturePath, UObject* InParent)
{
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString TrunkPath = Settings->ProjectTrunkPath;

    // Ensure RelativeTexturePath doesn't start with the TrunkPath
    FString CleanRelativePath = RelativeTexturePath;
    if (CleanRelativePath.StartsWith(TrunkPath))
    {
        CleanRelativePath.RemoveFromStart(TrunkPath);
    }
    CleanRelativePath = CleanRelativePath.Replace(TEXT("\\"), TEXT("/")).TrimStartAndEnd();

    // Combine trunk path with relative texture path for the full file path
    FString FullTexturePath = FPaths::Combine(TrunkPath, CleanRelativePath);

    FullTexturePath = FullTexturePath.Replace(TEXT("\\"), TEXT("/"));

    // Create the content path based on the relative path
    FString ContentPath = FString::Printf(TEXT("/Game/%s"), *FPaths::GetPath(CleanRelativePath));
    FString AssetName = FPaths::GetBaseFilename(CleanRelativePath);

    UE_LOG(LogTemp, Warning, TEXT("ImportTexture - RelativePath: %s"), *CleanRelativePath);
    UE_LOG(LogTemp, Warning, TEXT("ImportTexture - FullPath: %s"), *FullTexturePath);
    UE_LOG(LogTemp, Warning, TEXT("ImportTexture - ContentPath: %s"), *ContentPath);
    UE_LOG(LogTemp, Warning, TEXT("ImportTexture - AssetName: %s"), *AssetName);

    // Check if the file exists
    if (!FPaths::FileExists(FullTexturePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Texture file does not exist: %s"), *FullTexturePath);
        return nullptr;
    }

    // Ensure the destination folder exists
    if (!UEditorAssetLibrary::DoesDirectoryExist(ContentPath))
    {
        UEditorAssetLibrary::MakeDirectory(ContentPath);
    }

    // Check if the texture already exists
    FString AssetPath = FString::Printf(TEXT("%s/%s.%s"), *ContentPath, *AssetName, *AssetName);
    UTexture* ExistingTexture = LoadObject<UTexture>(nullptr, *AssetPath);
    if (ExistingTexture)
    {
        return ExistingTexture;
    }

    // If not, import the texture
    FAssetToolsModule& AssetToolsModule = FModuleManager::GetModuleChecked<FAssetToolsModule>("AssetTools");
    TArray<FString> FileNames;
    FileNames.Add(FullTexturePath);

    TArray<UObject*> ImportedAssets = AssetToolsModule.Get().ImportAssets(FileNames, ContentPath);

    if (ImportedAssets.Num() > 0)
    {
        UTexture* ImportedTexture = Cast<UTexture>(ImportedAssets[0]);
        if (ImportedTexture)
        {
            FAssetRegistryModule::AssetCreated(ImportedTexture);
            return ImportedTexture;
        }
    }

    UE_LOG(LogTemp, Error, TEXT("Failed to import texture: %s"), *FullTexturePath);
    return nullptr;
}


UMaterialInterface* UP3DImporter::CreateOrRetrieveMaterial(const FString& MaterialName)
{
    // --- DEBUG PRINT ---
    UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Processing MaterialName=\"%s\""), *MaterialName);
    // --- END DEBUG PRINT ---

    if (CreatedMaterials.Contains(MaterialName))
    {
        // --- DEBUG PRINT ---
        UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Found existing material in cache for \"%s\""), *MaterialName);
        // --- END DEBUG PRINT ---
        return CreatedMaterials[MaterialName];
    }

    FString MaterialPath;
    // --- DEBUG PRINT ---
    UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Searching for MaterialPath for \"%s\""), *MaterialName);
    // --- END DEBUG PRINT ---

    // Find the material path (Assuming this logic correctly finds the RVMAT path based on MaterialName)
    // This part relies on how MaterialPath is associated with MaterialName elsewhere (likely during LOD reading)
    // We keep the existing lookup logic from the previous state.
    for (int32 i = 0; i < TopLOD.Materials.Num(); i += 2) // Step by 2 assuming pairs
    {
        if (FPaths::GetBaseFilename(TopLOD.Materials[i]) == MaterialName)
        {
            MaterialPath = TopLOD.Materials[i];
            // --- DEBUG PRINT ---
            UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Found potential MaterialPath=\"%s\" for \"%s\" via TopLOD.Materials iteration"), *MaterialPath, *MaterialName);
            // --- END DEBUG PRINT ---
            break;
        }
    }
    if (MaterialPath.IsEmpty())
    {
         UE_LOG(LogTemp, Warning, TEXT("CreateOrRetrieveMaterial: Could not find MaterialPath for \"%s\" using TopLOD.Materials iteration. Check ReadLOD logic."), *MaterialName);
    }

    UMaterialInterface* Material = nullptr;

    // First try to import the RVMAT material
    if (!MaterialPath.IsEmpty())
    {
        // --- DEBUG PRINT ---
        UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Attempting to import RVMAT: \"%s\""), *MaterialPath);
        // --- END DEBUG PRINT ---
        URvmatAsset* MaterialInstance = ImportRvmatMaterial(MaterialPath, StaticMesh->GetOuter());
        if (MaterialInstance)
        {
            // --- DEBUG PRINT ---
            UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Successfully imported/retrieved RVMAT: \"%s\""), *MaterialInstance->GetPathName());
            // --- END DEBUG PRINT ---
            Material = MaterialInstance;

            // --- MODIFIED SUPER SHADER CHECK ---
            bool bIsSuperShader = false;
            // --- DEBUG PRINT ---
            UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Checking if \"%s\" is a Super Shader by checking parent material..."), *MaterialName);
            // --- END DEBUG PRINT ---

            UMaterialInterface* ParentMaterial = MaterialInstance->GetMaterial(); // Get the base material
            if (ParentMaterial)
            {
                FString ParentMaterialName = ParentMaterial->GetName();
                // --- DEBUG PRINT ---
                UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Parent material for \"%s\" is \"%s\""), *MaterialName, *ParentMaterialName);
                // --- END DEBUG PRINT ---
                if (ParentMaterialName == TEXT("MM_Super")) // Check if the parent is MM_Super
                {
                    bIsSuperShader = true;
                    // --- DEBUG PRINT ---
                    UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: \"%s\" IS a Super Shader (Parent is MM_Super)."), *MaterialName);
                    // --- END DEBUG PRINT ---
                }
            }
            else
            {
                 // --- DEBUG PRINT ---
                 UE_LOG(LogTemp, Warning, TEXT("CreateOrRetrieveMaterial: Could not get parent material for \"%s\""), *MaterialName);
                 // --- END DEBUG PRINT ---
            }

             if (!bIsSuperShader)
             {
                  // --- DEBUG PRINT ---
                  UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: \"%s\" is NOT a Super Shader (Parent is not MM_Super or could not be determined)."), *MaterialName);
                  // --- END DEBUG PRINT ---
             }

            // If it's a Super shader, look up the face texture from the FaceTextures map
            if (bIsSuperShader)
            {
                // *** CORRECTED LOGIC TO USE FaceTextures MAP ***
                FString FaceTexturePath;
                // --- DEBUG PRINT ---
                UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Super Shader detected. Looking for FaceTexturePath in TopLOD.FaceTextures with key \"%s\""), *MaterialName);
                // --- END DEBUG PRINT ---
                if (TopLOD.FaceTextures.Contains(MaterialName))
                {
                    FaceTexturePath = TopLOD.FaceTextures[MaterialName];
                    // --- DEBUG PRINT ---
                    UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Found FaceTexturePath=\"%s\" for key \"%s\""), *FaceTexturePath, *MaterialName);
                    // --- END DEBUG PRINT ---
                }
                else
                {
                    // --- DEBUG PRINT ---
                    UE_LOG(LogTemp, Warning, TEXT("CreateOrRetrieveMaterial: Key \"%s\" not found in TopLOD.FaceTextures."), *MaterialName);
                    // --- END DEBUG PRINT ---
                }
                // *** END CORRECTED LOGIC ***

                if (!FaceTexturePath.IsEmpty() && !FaceTexturePath.StartsWith("#"))
                {
                    // --- DEBUG PRINT ---
                    UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Attempting to import face texture: \"%s\""), *FaceTexturePath);
                    // --- END DEBUG PRINT ---
                    UTexture* DiffuseTexture = ImportTexture(FaceTexturePath, StaticMesh->GetOuter());
                    if (DiffuseTexture)
                    {
                        // --- DEBUG PRINT ---
                        UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Successfully imported/retrieved face texture: \"%s\""), *DiffuseTexture->GetPathName());
                        // --- END DEBUG PRINT ---
                        // Set the texture parameter for the diffuse texture
                        if (UMaterialInstanceConstant* MIC = Cast<UMaterialInstanceConstant>(MaterialInstance))
                        {
                             // --- CORRECTED TEXTURE SLOT TO Stage0 ---
                             FName TextureParameterName = FName("Stage0");
                             // --- DEBUG PRINT ---
                             UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Setting Texture Parameter '%s' on MIC \"%s\" with Texture \"%s\""), *TextureParameterName.ToString(), *MIC->GetPathName(), *DiffuseTexture->GetPathName());
                             // --- END DEBUG PRINT ---
                             MIC->SetTextureParameterValueEditorOnly(TextureParameterName, DiffuseTexture);
                             UE_LOG(LogTemp, Warning, TEXT("Set face texture %s as diffuse (%s) for Super shader material %s"),
                                 *FaceTexturePath, *TextureParameterName.ToString(), *MaterialName);
                        }
                        else
                        {
                             UE_LOG(LogTemp, Warning, TEXT("Material %s is Super Shader but not a MaterialInstanceConstant. Cannot set face texture."), *MaterialName);
                        }
                    }
                     else
                    {
                        UE_LOG(LogTemp, Warning, TEXT("Failed to import face texture %s for Super shader material %s"),
                            *FaceTexturePath, *MaterialName);
                    }
                }
                 else
                {
                    UE_LOG(LogTemp, Warning, TEXT("Super shader material %s found, but no valid face texture path in FaceTextures map or path starts with # ('%s')."), *MaterialName, *FaceTexturePath);
                }
            }
            // Non-super shaders should get their textures from the RVMAT itself during ImportRvmatMaterial
        }
         else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to import RVMAT for material: %s using path: %s"), *MaterialName, *MaterialPath);
        }
    }
     else
    {
         UE_LOG(LogTemp, Error, TEXT("Could not find MaterialPath for MaterialName: %s"), *MaterialName);
    }


    if (Material)
    {
        // --- DEBUG PRINT ---
        UE_LOG(LogTemp, Log, TEXT("CreateOrRetrieveMaterial: Adding material \"%s\" to cache."), *MaterialName);
        // --- END DEBUG PRINT ---
        CreatedMaterials.Add(MaterialName, Material);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create or retrieve material for: %s"), *MaterialName);
    }


    return Material;
}


FBox UP3DImporter::CalculateCombinedBounds(const P3DData& Data) const
{
    FBox CombinedBounds(ForceInitToZero); // Initialize to a zero box

    bool bHasValidLOD = false; // Flag to check if any LOD meets the criteria

    for (const auto& LODPair : Data.LODs)
    {
        const int32 LODIndex = LODPair.Key;
        const LOD CurrentLOD = LODPair.Value;

        if (CurrentLOD.Vertices.Num() == 0)
        {
            UE_LOG(LogTemp, Warning, TEXT("LOD %d has no vertices. Skipping."), LODIndex);
            continue;
        }

        FBox LODBounds = FBox(CurrentLOD.Vertices);

        if (!bHasValidLOD)
        {
            CombinedBounds = LODBounds; // Initialize combined bounds with the first valid LOD
            bHasValidLOD = true;
        }
        else
        {
            CombinedBounds += LODBounds; // Expand combined bounds to include the current LOD
        }
    }

    if (bHasValidLOD)
    {
        UE_LOG(LogTemp, Log, TEXT("Combined Bounds - Min: %s, Max: %s"),
            *CombinedBounds.Min.ToString(),
            *CombinedBounds.Max.ToString());
    }
    else
    {
        CombinedBounds = FBox(EForceInit::ForceInit); // Reset to an invalid box
    }

    return CombinedBounds;
}


bool UP3DImporter::ReadBoolean(const uint8*& Buffer, const uint8* BufferEnd)
{
    if (Buffer < BufferEnd)
    {
        bool Value = (*Buffer != 0);
        Buffer++;
        return Value;
    }
    UE_LOG(LogTemp, Warning, TEXT("Reached end of buffer while reading boolean"));
    return false;
}


FString UP3DImporter::NormalizePath(const FString& Path)
{
    FString NormalizedPath = Path;
    FPaths::MakeStandardFilename(NormalizedPath);
    NormalizedPath = FPaths::ConvertRelativePathToFull(NormalizedPath);
    return NormalizedPath;
}


FString UP3DImporter::ReadNullTerminatedString(const uint8*& Buffer, const uint8* BufferEnd, int32 MaxLength)
{
    FString Result;
    int32 CharCount = 0;
    while (Buffer < BufferEnd && *Buffer != 0 && CharCount < MaxLength)
    {
        Result += (TCHAR)*Buffer;
        Buffer++;
        CharCount++;
    }
    if (Buffer < BufferEnd && *Buffer == 0)
    {
        Buffer++; // Skip the null terminator
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Reached end of buffer or max length while reading string"));
    }

    return Result;
}

bool AreNearlyEqual(double a, double b, double relTol = 1e-7)
{
    double diff = FMath::Abs(a - b);
    double maxAB = FMath::Max(FMath::Abs(a), FMath::Abs(b));
    return diff <= relTol * maxAB;
}


UBlueprint* UP3DImporter::CreateP3DActorBlueprint(
    const FString& RelativeAssetPath,
    const FString& PackageName,
    UStaticMesh* Mesh,
    const FString& ContentRelativeP3DPath,
    const P3DData& P3DData)
{
    // Construct the actor name and package path
    FString P3DActorName = FString::Printf(TEXT("PF_%s"), *FPaths::GetBaseFilename(RelativeAssetPath));
    // Get the parent folder path (outside of /data/)
    FString ParentFolderPath = FPaths::GetPath(FPaths::GetPath(PackageName));

    // Create the blueprint in the parent folder, not in /data/
    FString P3DActorPackagePath = ParentFolderPath + TEXT("/") + P3DActorName;

    UE_LOG(LogTemp, Warning, TEXT("Creating P3DActor blueprint at: %s"), *P3DActorPackagePath);

    // Attempt to load existing blueprint
    UBlueprint* Blueprint = LoadObject<UBlueprint>(nullptr, *P3DActorPackagePath);
    bool bIsExisting = false;

    if (Blueprint)
    {
        UE_LOG(LogTemp, Warning, TEXT("P3DActor blueprint already exists: %s"), *P3DActorPackagePath);
        bIsExisting = true;

        // For existing blueprints, we'll clear all components and recreate them
        // This ensures we don't get duplicates during reimport
        if (USimpleConstructionScript* SCS = Blueprint->SimpleConstructionScript)
        {
            // Get all nodes
            TArray<USCS_Node*> AllNodes = SCS->GetAllNodes();

            // Remove all nodes
            for (USCS_Node* Node : AllNodes)
            {
                SCS->RemoveNode(Node);
            }

            UE_LOG(LogTemp, Warning, TEXT("Cleared all components from existing blueprint"));
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("Creating new P3DActor blueprint"));

        // Create the package if it doesn't exist
        UPackage* P3DActorPackage = CreatePackage(*P3DActorPackagePath);
        if (!P3DActorPackage)
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to create package: %s"), *P3DActorPackagePath);
            return nullptr;
        }

        // Create the new blueprint with NO default root component
        Blueprint = FKismetEditorUtilities::CreateBlueprint(
            AP3DActor::StaticClass(),
            P3DActorPackage,
            FName(*P3DActorName),
            BPTYPE_Normal,
            UP3DBlueprint::StaticClass(),
            UBlueprintGeneratedClass::StaticClass(),
            FName("P3DImporter")
        );

        if (!Blueprint)
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to create P3DActor blueprint"));
            return nullptr;
        }

        // Ensure the SCS is clean for a new blueprint
        if (USimpleConstructionScript* SCS = Blueprint->SimpleConstructionScript)
        {
            // Remove any auto-generated components
            if (USCS_Node* ExistingRoot = SCS->GetDefaultSceneRootNode())
            {
                SCS->RemoveNode(ExistingRoot);
            }
        }
    }

    // Start a transaction
    const FScopedTransaction Transaction(NSLOCTEXT("UnrealEd", "CreateOrUpdateP3DActor", "Create or Update P3D Actor"));

    // Modify the blueprint
    Blueprint->Modify();

    // Get the Simple Construction Script
    USimpleConstructionScript* SCS = Blueprint->SimpleConstructionScript;
    if (!SCS)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get Simple Construction Script"));
        return nullptr;
    }

    // Clear existing nodes if any
    TArray<USCS_Node*> AllNodes = SCS->GetAllNodes();
    for (USCS_Node* Node : AllNodes)
    {
        SCS->RemoveNode(Node);
    }

    // Create root scene component node
    // Create root scene component node with a unique name
    FString RootName = TEXT("DefaultSceneRoot");
    int32 RootCounter = 0;
    while (SCS->FindSCSNode(*RootName) != nullptr)
    {
        RootCounter++;
        RootName = FString::Printf(TEXT("DefaultSceneRoot_%d"), RootCounter);
    }

    USCS_Node* RootNode = SCS->CreateNode(USceneComponent::StaticClass(), *RootName);
    if (!RootNode)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create root node"));
        return nullptr;
    }

    // Set up root component properties
    USceneComponent* RootTemplate = Cast<USceneComponent>(RootNode->ComponentTemplate);
    if (RootTemplate)
    {
        RootTemplate->Mobility = EComponentMobility::Movable;
    }

    // Add root node to SCS and force a blueprint update
    SCS->AddNode(RootNode);
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);

    // Create a unique name for the mesh component
    FString MeshName = TEXT("MeshComponent");
    int32 MeshCounter = 0;
    while (SCS->FindSCSNode(*MeshName) != nullptr)
    {
        MeshCounter++;
        MeshName = FString::Printf(TEXT("MeshComponent_%d"), MeshCounter);
    }

    // Create static mesh component node
    USCS_Node* MeshNode = SCS->CreateNode(UStaticMeshComponent::StaticClass(), *MeshName);
    if (!MeshNode)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create mesh node"));
        return nullptr;
    }

    // Set up mesh component properties
    UStaticMeshComponent* MeshTemplate = Cast<UStaticMeshComponent>(MeshNode->ComponentTemplate);
    if (MeshTemplate)
    {
        MeshTemplate->SetStaticMesh(Mesh);
        MeshTemplate->Mobility = EComponentMobility::Movable;
        MeshTemplate->SetRelativeLocation(FVector::ZeroVector);
        MeshTemplate->SetRelativeRotation(FRotator::ZeroRotator);
        MeshTemplate->SetRelativeScale3D(FVector::OneVector);
        MeshTemplate->bEditableWhenInherited = true;
    }

    // Set metadata and properties
    MeshNode->SetMetaData(TEXT("Category"), TEXT("Default"));
    MeshNode->VariableGuid = FGuid::NewGuid();
    MeshNode->bIsParentComponentNative = false;

    // Set parent to root node
    MeshNode->ParentComponentOrVariableName = RootNode->GetVariableName();

    // Add mesh node to SCS
    SCS->AddNode(MeshNode);

    // Compile blueprint first to ensure we have a valid CDO
    FKismetEditorUtilities::CompileBlueprint(Blueprint);

    // Get and modify the CDO for custom properties
    AP3DActor* CDO = Cast<AP3DActor>(Blueprint->GeneratedClass->GetDefaultObject());
    if (CDO)
    {
        CDO->Modify();

        // Set P3D properties
        CDO->P3DPath = ContentRelativeP3DPath;

        // Determine bAutoCenter based on P3DData
        bool bShouldAutoCenter = true;

        CDO->bIncludeInLOD1 = true;
        CDO->bIncludeInLOD2 = false;
        CDO->bIncludeInLOD3 = false;
        CDO->bIncludeInLOD4 = false;
        CDO->bIncludeInLOD5 = false;
        CDO->bIncludeInLOD6 = false;
        CDO->bIncludeInLOD7 = false;
        CDO->bIncludeInLOD8 = false;
        CDO->bIncludeInGeometry = false;
        CDO->bIncludeInMemory = false;
        CDO->bIncludeInRoadWay = false;
        CDO->bIncludeInPaths = false;
        CDO->bIncludeInViewGeometry = false;
        CDO->bIncludeInFireGeometry = false;

        for (const auto& LODPair : P3DData.LODs)
        {
            const LOD& CurrentLOD = LODPair.Value;

            // SET PROXY INCLUDES

            if (CurrentLOD.Resolution == 1.0)
                CDO->bIncludeInLOD1 = true;
            else if (CurrentLOD.Resolution == 2.0)
                CDO->bIncludeInLOD2 = true;
            else if (CurrentLOD.Resolution == 3.0)
                CDO->bIncludeInLOD3 = true;
            else if (CurrentLOD.Resolution == 4.0)
                CDO->bIncludeInLOD4 = true;
            else if (CurrentLOD.Resolution == 5.0)
                CDO->bIncludeInLOD5 = true;
            else if (CurrentLOD.Resolution == 6.0)
                CDO->bIncludeInLOD6 = true;
            else if (CurrentLOD.Resolution == 7.0)
                CDO->bIncludeInLOD7 = true;
            else if (CurrentLOD.Resolution == 8.0)
                CDO->bIncludeInLOD8 = true;

            //GEOMETRY
            else if (AreNearlyEqual(CurrentLOD.Resolution, 1.0e13))
                CDO->bIncludeInGeometry = true;
            //MEMORY
            else if (AreNearlyEqual(CurrentLOD.Resolution, 1.0e15))
                CDO->bIncludeInMemory = true;
            //ROADWAY
            else if (AreNearlyEqual(CurrentLOD.Resolution, 3.0e15))
                CDO->bIncludeInRoadWay = true;
            //PATH
            else if (AreNearlyEqual(CurrentLOD.Resolution, 4.0e15))
                CDO->bIncludeInPaths = true;
            //VIEW GEOMETRY
            else if (AreNearlyEqual(CurrentLOD.Resolution, 6.0e15))
                CDO->bIncludeInViewGeometry = true;
            //FIRE GEOMETRY
            else if (AreNearlyEqual(CurrentLOD.Resolution, 7.0e15))
                CDO->bIncludeInFireGeometry = true;

            if (CurrentLOD.Properties.Contains("autocenter"))
            {
                FString AutoCenterValue = CurrentLOD.Properties["autocenter"];
                bShouldAutoCenter = (AutoCenterValue != "0");
                break;
            }
        }

        if (!bShouldAutoCenter && P3DData.AdditionalProperties.Contains("autocenter"))
        {
            FString AutoCenterValue = P3DData.AdditionalProperties["autocenter"];
            bShouldAutoCenter = (AutoCenterValue != "0");
        }

        CDO->bAutoCenter = bShouldAutoCenter;
        CDO->ObjectCenter = ObjectCenter;
    }

    // Ensure Blueprint is up to date
    FBlueprintEditorUtils::MarkBlueprintAsStructurallyModified(Blueprint);

    // Compile again after all modifications
    FKismetEditorUtilities::CompileBlueprint(Blueprint);

    if (!bIsExisting)
    {
        FAssetRegistryModule::AssetCreated(Blueprint);
    }

    Blueprint->PostEditChange();
    Blueprint->MarkPackageDirty();

    UE_LOG(LogTemp, Warning, TEXT("Successfully %s P3DActor blueprint"),
        bIsExisting ? TEXT("updated") : TEXT("created"));

    return Blueprint;
}

UStaticMesh* UP3DImporter::ImportP3DFromPath(const FString& P3DFilePath, FName InName, EObjectFlags Flags)
{
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString TrunkPath = FPaths::ConvertRelativePathToFull(Settings->ProjectTrunkPath);
    FString FullFilePathNormalized = NormalizePath(CurrentFilename);
    FString TrunkPathNormalized = NormalizePath(Settings->ProjectTrunkPath);

    if (FullFilePathNormalized.StartsWith(TEXT("P:/")))
    {
        FullFilePathNormalized = FullFilePathNormalized.Replace(TEXT("P:/"), *TrunkPathNormalized);
    }

    FullFilePathNormalized = FullFilePathNormalized.TrimEnd();
    TrunkPathNormalized = TrunkPathNormalized.TrimEnd();

    FullFilePathNormalized = FullFilePathNormalized.Replace(TEXT("\\"), TEXT("/"));
    TrunkPathNormalized = TrunkPathNormalized.Replace(TEXT("\\"), TEXT("/"));

    //UE_LOG(LogTemp, Warning, TEXT("FullFilePathNormalized: %s"), *FullFilePathNormalized);
    //UE_LOG(LogTemp, Warning, TEXT("TrunkPathNormalized: %s"), *TrunkPathNormalized);

    FString RelativeAssetPath;
    if (FullFilePathNormalized.StartsWith(TrunkPathNormalized))
    {
        RelativeAssetPath = FullFilePathNormalized.RightChop(TrunkPathNormalized.Len());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("FullFilePath does not start with TrunkPath"));
        RelativeAssetPath = FPaths::GetCleanFilename(FullFilePathNormalized);
    }

    RelativeAssetPath = RelativeAssetPath.TrimStartAndEnd();
    if (RelativeAssetPath.StartsWith(TEXT("/")))
    {
        RelativeAssetPath = RelativeAssetPath.RightChop(1);
    }

    //UE_LOG(LogTemp, Warning, TEXT("Computed RelativeAssetPath: %s"), *RelativeAssetPath);
    FString ContentPath = FString::Printf(TEXT("/Game/%s"), *FPaths::GetPath(RelativeAssetPath));
    ContentPath = ContentPath.TrimEnd();
    if (ContentPath.EndsWith(TEXT("/")))
    {
        ContentPath = ContentPath.LeftChop(1);
    }

    ContentPath = UPackageTools::SanitizePackageName(ContentPath);

    FString ContentRelativePath = ContentPath.RightChop(5); // Remove "/Game" from the start
    FString P3DFilename = FPaths::GetCleanFilename(CurrentFilename);
    FString ContentRelativeP3DPath = FPaths::Combine(ContentRelativePath, P3DFilename);

    ContentRelativeP3DPath = ContentRelativeP3DPath.Replace(TEXT("\\"), TEXT("/"));

    if (ContentRelativeP3DPath.StartsWith(TEXT("/")))
    {
        ContentRelativeP3DPath = ContentRelativeP3DPath.RightChop(1);
    }

    //UE_LOG(LogTemp, Warning, TEXT("ContentRelativeP3DPath: %s"), *ContentRelativeP3DPath);
    UE_LOG(LogTemp, Warning, TEXT("Final ContentPath: %s"), *ContentPath);

    if (!IFileManager::Get().DirectoryExists(*ContentPath))
    {
        IFileManager::Get().MakeDirectory(*ContentPath, true);
    }

    FString DataFolderPath = ContentPath + "/data";
    if (!IFileManager::Get().DirectoryExists(*DataFolderPath))
    {
        IFileManager::Get().MakeDirectory(*DataFolderPath, true);
    }

    FString PackageName = DataFolderPath + "/" + FPaths::GetBaseFilename(RelativeAssetPath);
    PackageName = UPackageTools::SanitizePackageName(PackageName);

    UPackage* FinalPackage = CreatePackage(*PackageName);
    if (!FinalPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create or find the package: %s"), *PackageName);
        return nullptr;
    }

    if (!FPaths::FileExists(P3DFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("P3D file does not exist: %s"), *P3DFilePath);
        return nullptr;
    }

    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *P3DFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load P3D file into buffer: %s"), *P3DFilePath);
        return nullptr;
    }

    const uint8* Buffer = FileData.GetData();
    const uint8* BufferEnd = Buffer + FileData.Num();

    // Initialize the import operation
    bool bOperationCanceled = false;

    // Call FactoryCreateFile to process the import
    UObject* ImportedObject = FactoryCreateFile(UStaticMesh::StaticClass(), FinalPackage, InName, Flags, P3DFilePath, nullptr, GWarn, bOperationCanceled);

    // Check if the imported object is a UStaticMesh
    if (ImportedObject && ImportedObject->IsA<UStaticMesh>())
    {
        UStaticMesh* ImportedStaticMesh = Cast<UStaticMesh>(ImportedObject);
        UE_LOG(LogTemp, Log, TEXT("Successfully imported P3D mesh: %s"), *ImportedStaticMesh->GetName());
        // Save the package to disk to avoid blocking issues
        //FString PackageFileName = FPackageName::LongPackageNameToFilename(ContentPath, FPackageName::GetAssetPackageExtension());
        //UPackage::SavePackage(FinalPackage, ImportedStaticMesh, EObjectFlags::RF_Public | RF_Standalone, *PackageFileName);
        return ImportedStaticMesh;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to import P3D mesh from file: %s"), *P3DFilePath);
        return nullptr;
    }
}


bool UP3DImporter::AddProxyToBlueprint(UBlueprint* P3DPrefabBluerint, UBlueprint* ProxyPrefabBlueprint, const FVector& Position, const FRotator& Rotation, const FVector& MeshCenter)
{
    if (!P3DPrefabBluerint || !ProxyPrefabBlueprint)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid blueprints"));
        return false;
    }

    // Get the proxy's default object and check mesh status
    AP3DActor* ProxyDefaultObject = Cast<AP3DActor>(ProxyPrefabBlueprint->GeneratedClass->GetDefaultObject());
    if (!ProxyDefaultObject)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to get proxy default object for %s"), *ProxyPrefabBlueprint->GetName());
        return false;
    }

    // Get all static mesh components and check their status
    TArray<UStaticMeshComponent*> MeshComponents;
    ProxyDefaultObject->GetComponents<UStaticMeshComponent>(MeshComponents);

    // Try a few times to wait for mesh compilation
    const int32 MaxTries = 3;
    int32 CurrentTry = 0;
    bool bMeshesReady = false;

    while (CurrentTry < MaxTries && !bMeshesReady)
    {
        bMeshesReady = true;

        for (UStaticMeshComponent* MeshComp : MeshComponents)
        {
            if (!MeshComp || !MeshComp->GetStaticMesh())
            {
                continue;
            }

            UStaticMesh* Mesh = MeshComp->GetStaticMesh();

            if (Mesh->IsCompiling())
            {
                bMeshesReady = false;
                UE_LOG(LogTemp, Warning, TEXT("Mesh %s is still compiling (attempt %d/%d)"),
                    *Mesh->GetName(), CurrentTry + 1, MaxTries);

                // Wait a bit before next try
                FPlatformProcess::Sleep(0.1f);
                break;
            }

            // Additional checks for mesh validity
            if (!Mesh->IsValidLowLevel() || !Mesh->HasValidRenderData())
            {
                UE_LOG(LogTemp, Warning, TEXT("Mesh %s has invalid render data"), *Mesh->GetName());
                return false;
            }

            // Ensure mesh is fully loaded
            //if (!Mesh->IsFullyLoaded())
            //{
            //    Mesh->ConditionalPostLoad();
            //}

            // Force navigation relevancy off to avoid the ensure failure
            MeshComp->bNavigationRelevant = false;
        }

        CurrentTry++;
    }

    if (!bMeshesReady)
    {
        UE_LOG(LogTemp, Warning, TEXT("Skipping proxy %s after %d attempts - meshes not ready"),
            *ProxyPrefabBlueprint->GetName(), MaxTries);
        return false;
    }

    // Start a transaction
    const FScopedTransaction Transaction(NSLOCTEXT("UnrealEd", "AddProxyToBlueprint", "Add Proxy to Blueprint"));

    // Modify the blueprint
    P3DPrefabBluerint->Modify();

    // Get the Blueprint's Simple Construction Script
    USimpleConstructionScript* SCS = P3DPrefabBluerint->SimpleConstructionScript;
    if (!SCS)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get Simple Construction Script"));
        return false;
    }

    // Find the root node
    USCS_Node* RootNode = SCS->GetDefaultSceneRootNode();
    if (!RootNode)
    {
        UE_LOG(LogTemp, Error, TEXT("No root node found"));
        return false;
    }

    // Generate a unique name for the component
    FString BaseName = ProxyPrefabBlueprint->GetName().Replace(TEXT("PF_"), TEXT(""));
    FString UniqueName;
    int32 Counter = 0;
    do
    {
        UniqueName = (Counter == 0) ? BaseName : FString::Printf(TEXT("%s_%d"), *BaseName, Counter);
        Counter++;
    } while (SCS->FindSCSNode(*UniqueName) != nullptr);

    // Create new SCS node for Child Actor Component
    USCS_Node* NewNode = SCS->CreateNode(UChildActorComponent::StaticClass(), *UniqueName);
    if (!NewNode)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create SCS node"));
        return false;
    }

    // Set up the child actor component properties
    UChildActorComponent* ChildActorTemplate = Cast<UChildActorComponent>(NewNode->ComponentTemplate);
    if (!ChildActorTemplate)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get component template"));
        return false;
    }

    // Set the child actor class
    if (UClass* GeneratedClass = ProxyPrefabBlueprint->GeneratedClass)
    {
        if (GeneratedClass->IsChildOf(AP3DActor::StaticClass()))
        {
            if (ProxyPrefabBlueprint->Status == EBlueprintStatus::BS_Error)
            {
                UE_LOG(LogTemp, Warning, TEXT("Skipping proxy %s because its blueprint has compilation errors"),
                    *ProxyPrefabBlueprint->GetName());
                return false;
            }

            TSubclassOf<AP3DActor> ChildClass(GeneratedClass);
            ChildActorTemplate->SetChildActorClass(ChildClass);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Proxy blueprint is not a P3DActor class"));
            return false;
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get generated class from proxy blueprint"));
        return false;
    }

    // Set transform
    FVector RelativePosition = (Position * 100.0f) + (Cast<AP3DActor>(ProxyPrefabBlueprint->GeneratedClass->GetDefaultObject())->ObjectCenter * 100.0f) - (MeshCenter * 100.0f);
    ChildActorTemplate->SetRelativeLocation(RelativePosition);
    ChildActorTemplate->SetRelativeRotation(Rotation);

    // Set metadata and properties
    NewNode->SetMetaData(TEXT("Category"), TEXT("Default"));
    NewNode->VariableGuid = FGuid::NewGuid();

    // Set parent component
    NewNode->ParentComponentOrVariableName = RootNode->GetVariableName();

    // Add the node to the hierarchy
    SCS->AddNode(NewNode);

    // Compile blueprint
    FKismetEditorUtilities::CompileBlueprint(P3DPrefabBluerint);

    // Check compilation status
    if (P3DPrefabBluerint->Status == EBlueprintStatus::BS_Error)
    {
        UE_LOG(LogTemp, Warning, TEXT("Failed to compile blueprint after adding proxy %s - removing node"),
            *ProxyPrefabBlueprint->GetName());
        SCS->RemoveNode(NewNode);
        return false;
    }

    UE_LOG(LogTemp, Log, TEXT("Successfully added proxy component: %s"), *UniqueName);
    return true;
}

void UP3DImporter::ImportProxies(const TArray<FProxyData>& ProxyDataArray, P3DData p3ddata, UBlueprint* P3DPrefabBluerint)
{
    // Start by clearing any existing components from the blueprint
    if (P3DPrefabBluerint)
    {
        USimpleConstructionScript* SCS = P3DPrefabBluerint->SimpleConstructionScript;
        if (SCS)
        {
            // Get all nodes
            TArray<USCS_Node*> AllNodes = SCS->GetAllNodes();

            // First, identify the root node and the primary mesh component
            USCS_Node* RootNode = nullptr;
            USCS_Node* PrimaryMeshNode = nullptr;

            for (USCS_Node* Node : AllNodes)
            {
                if (Node->ComponentTemplate && Node->ComponentTemplate->IsA<USceneComponent>() &&
                    Node->GetVariableName().ToString().StartsWith(TEXT("DefaultSceneRoot")))
                {
                    RootNode = Node;
                }
                else if (Node->ComponentTemplate && Node->ComponentTemplate->IsA<UStaticMeshComponent>() &&
                         Node->GetVariableName().ToString().StartsWith(TEXT("MeshComponent")))
                {
                    PrimaryMeshNode = Node;
                }
            }

            // Remove all nodes except the root
            for (USCS_Node* Node : AllNodes)
            {
                if (Node != RootNode)
                {
                    SCS->RemoveNode(Node);
                }
            }

            // If we found a primary mesh node, recreate it
            if (PrimaryMeshNode && PrimaryMeshNode->ComponentTemplate)
            {
                // Create a new mesh component node
                FString MeshName = TEXT("MeshComponent");
                USCS_Node* NewMeshNode = SCS->CreateNode(UStaticMeshComponent::StaticClass(), *MeshName);

                if (NewMeshNode)
                {
                    // Copy properties from the original mesh component
                    UStaticMeshComponent* OriginalMeshTemplate = Cast<UStaticMeshComponent>(PrimaryMeshNode->ComponentTemplate);
                    UStaticMeshComponent* NewMeshTemplate = Cast<UStaticMeshComponent>(NewMeshNode->ComponentTemplate);

                    if (OriginalMeshTemplate && NewMeshTemplate)
                    {
                        NewMeshTemplate->SetStaticMesh(OriginalMeshTemplate->GetStaticMesh());
                        NewMeshTemplate->Mobility = OriginalMeshTemplate->Mobility;
                        NewMeshTemplate->SetRelativeLocation(OriginalMeshTemplate->GetRelativeLocation());
                        NewMeshTemplate->SetRelativeRotation(OriginalMeshTemplate->GetRelativeRotation());
                        NewMeshTemplate->SetRelativeScale3D(OriginalMeshTemplate->GetRelativeScale3D());
                        NewMeshTemplate->bEditableWhenInherited = OriginalMeshTemplate->bEditableWhenInherited;
                    }

                    // Set metadata and properties
                    NewMeshNode->SetMetaData(TEXT("Category"), TEXT("Default"));
                    NewMeshNode->VariableGuid = FGuid::NewGuid();
                    NewMeshNode->bIsParentComponentNative = false;

                    // Set parent to root node
                    if (RootNode)
                    {
                        NewMeshNode->ParentComponentOrVariableName = RootNode->GetVariableName();
                    }

                    // Add mesh node to SCS
                    SCS->AddNode(NewMeshNode);
                }
            }

            // Compile blueprint after removing nodes
            FKismetEditorUtilities::CompileBlueprint(P3DPrefabBluerint);
            UE_LOG(LogTemp, Warning, TEXT("Cleared existing components from blueprint and recreated primary mesh component"));
        }
    }

    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString TrunkPath = FPaths::ConvertRelativePathToFull(Settings->ProjectTrunkPath);
    FString TrunkPathNormalized = NormalizePath(Settings->ProjectTrunkPath);

    FBox MeshBounds = CalculateCombinedBounds(p3ddata);
    FVector MeshCenter = MeshBounds.GetCenter();

    UE_LOG(LogTemp, Log, TEXT("MESH CENTER: %f, %f, %f"), MeshCenter.X, MeshCenter.Y, MeshCenter.Z);

    if (ProxyDataArray.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("No proxies found."));
        return;
    }

    for (int32 ProxyIndex = 0; ProxyIndex < ProxyDataArray.Num(); ++ProxyIndex)
    {
        const FProxyData& Proxy = ProxyDataArray[ProxyIndex];
        UE_LOG(LogTemp, Warning, TEXT("Processing Proxy %d / %d"), ProxyIndex + 1, ProxyDataArray.Num());

        // Replace "proxy:" with the trunk path and change extension to .p3d
        FString P3DPath = Proxy.Name.Replace(TEXT("proxy:"), *TrunkPathNormalized);
        P3DPath = P3DPath.Replace(TEXT("\\"), TEXT("/")).TrimStartAndEnd();
        P3DPath = FPaths::GetPath(P3DPath) + "/" + FPaths::GetBaseFilename(P3DPath) + TEXT(".p3d");
        UE_LOG(LogTemp, Warning, TEXT("    P3D Path: %s"), *P3DPath);

        FString tempCurrentFilename = CurrentFilename;

        FString FullFilePathNormalized = NormalizePath(P3DPath);


        if (FullFilePathNormalized.StartsWith(TEXT("P:/")))
        {
            FullFilePathNormalized = FullFilePathNormalized.Replace(TEXT("P:/"), *TrunkPathNormalized);
        }

        FullFilePathNormalized = FullFilePathNormalized.TrimEnd();
        TrunkPathNormalized = TrunkPathNormalized.TrimEnd();

        FullFilePathNormalized = FullFilePathNormalized.Replace(TEXT("\\"), TEXT("/"));
        TrunkPathNormalized = TrunkPathNormalized.Replace(TEXT("\\"), TEXT("/"));

        //UE_LOG(LogTemp, Warning, TEXT("FullFilePathNormalized: %s"), *FullFilePathNormalized);
        //UE_LOG(LogTemp, Warning, TEXT("TrunkPathNormalized: %s"), *TrunkPathNormalized);

        FString RelativeAssetPath;
        if (FullFilePathNormalized.StartsWith(TrunkPathNormalized))
        {
            RelativeAssetPath = FullFilePathNormalized.RightChop(TrunkPathNormalized.Len());
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("FullFilePath does not start with TrunkPath"));
            RelativeAssetPath = FPaths::GetCleanFilename(FullFilePathNormalized);
        }

        RelativeAssetPath = RelativeAssetPath.TrimStartAndEnd();
        if (RelativeAssetPath.StartsWith(TEXT("/")))
        {
            RelativeAssetPath = RelativeAssetPath.RightChop(1);
        }

        //UE_LOG(LogTemp, Warning, TEXT("Computed RelativeAssetPath: %s"), *RelativeAssetPath);
        FString ContentPath = FString::Printf(TEXT("/Game/%s"), *FPaths::GetPath(RelativeAssetPath));
        ContentPath = ContentPath.TrimEnd();
        if (ContentPath.EndsWith(TEXT("/")))
        {
            ContentPath = ContentPath.LeftChop(1);
        }

        ContentPath = UPackageTools::SanitizePackageName(ContentPath);

        FString ContentRelativePath = ContentPath.RightChop(5); // Remove "/Game" from the start
        FString P3DFilename = FPaths::GetCleanFilename(tempCurrentFilename);
        FString ContentRelativeP3DPath = FPaths::Combine(ContentRelativePath, P3DFilename);

        // Get base name for the proxy
        FString ProxyBaseName = FPaths::GetBaseFilename(P3DPath);
        FString PrefabBlueprintPath = ContentPath + TEXT("/PF_") + ProxyBaseName + TEXT(".PF_") + ProxyBaseName;

        // Try to load existing proxy blueprint first
        UBlueprint* ProxyPrefabBlueprint = LoadObject<UBlueprint>(nullptr, *PrefabBlueprintPath);
        bool bNeedsImport = false;

        if (!ProxyPrefabBlueprint)
        {
            UE_LOG(LogTemp, Log, TEXT("Proxy blueprint not found, importing: %s"), *PrefabBlueprintPath);
            bNeedsImport = true;

            // Import the P3D mesh only if needed
            bool bOperationCanceled = false;
            UStaticMesh* ImportedMesh = ImportP3DFromPath(P3DPath, FName(*ProxyBaseName), RF_Public | RF_Standalone);

            if (bOperationCanceled || !ImportedMesh)
            {
                UE_LOG(LogTemp, Error, TEXT("Failed to import P3D mesh for proxy %d"), ProxyIndex + 1);
                continue;
            }

            // Try loading the blueprint again after import
            ProxyPrefabBlueprint = LoadObject<UBlueprint>(nullptr, *PrefabBlueprintPath);
            if (!ProxyPrefabBlueprint)
            {
                UE_LOG(LogTemp, Error, TEXT("Failed to find proxy prefab after import!"));
                continue;
            }
        }
        else
        {
            UE_LOG(LogTemp, Log, TEXT("PROXY BLUEPRINT ALREADY EXISTS. Skipping re-import: %s"), *PrefabBlueprintPath);
        }

        // Add the proxy to the parent blueprint
        bool bSuccess = AddProxyToBlueprint(
            P3DPrefabBluerint,
            ProxyPrefabBlueprint,
            Proxy.Position,
            Proxy.Rotation,
            MeshCenter
        );

        if (!bSuccess)
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to add proxy blueprint %s to parent blueprint"),
                *ProxyPrefabBlueprint->GetName());
            continue;
        }

        // If we got this far, compile and save the parent blueprint
        if (P3DPrefabBluerint)
        {
            if (bNeedsImport)
            {
                UE_LOG(LogTemp, Log, TEXT("Compiling parent blueprint after new import"));
                FKismetEditorUtilities::CompileBlueprint(P3DPrefabBluerint);
                P3DPrefabBluerint->Modify();
                P3DPrefabBluerint->PostEditChange();
                P3DPrefabBluerint->MarkPackageDirty();

                FlushAsyncLoading();
                FlushRenderingCommands();
            }
        }

        UE_LOG(LogTemp, Warning, TEXT("Finished Processing Proxy %d / %d"), ProxyIndex + 1, ProxyDataArray.Num());
    }
}



