#include "PAA.h"
#include "PixelFormatConversion.h"
#include "ChannelSwizzling.h"
#include "MiniLZO.h"
#include <fstream>
#include <stdexcept>
#include <cassert>
#include <array>

PAA::PAA(const std::string& file)
{
    std::ifstream fileStream(file, std::ios::binary);
    std::vector<uint8_t> data((std::istreambuf_iterator<char>(fileStream)), std::istreambuf_iterator<char>());
    FBinaryReader reader(data);
    Read(reader, !file.ends_with(".pac"));
}

PAA::PAA(const std::vector<uint8_t>& data, bool isPac)
{
    FBinaryReader reader(data);
    Read(reader, isPac);
}

PAA::PAA(FBinaryReader& stream, bool isPac)
{
    Read(stream, isPac);
}

PAAType PAA::MagicNumberToType(uint16_t magic)
{
    switch (magic)
    {
    case 0x4444: return PAAType::RGBA_4444;
    case 0x8080: return PAAType::AI88;
    case 0x1555: return PAAType::RGBA_5551;
    case 0xff01: return PAAType::DXT1;
    case 0xff02: return PAAType::DXT2;
    case 0xff03: return PAAType::DXT3;
    case 0xff04: return PAAType::DXT4;
    case 0xff05: return PAAType::DXT5;
    default: return PAAType::UNDEFINED;
    }
}

void PAA::Read(FBinaryReader& input, bool isPac)
{
    uint16_t magic = input.ReadUInt16();
    PAAType type = MagicNumberToType(magic);
    if (type == PAAType::UNDEFINED)
    {
        type = (!isPac) ? PAAType::RGBA_4444 : PAAType::P8;
        input.SetPosition(input.GetPosition() - 2);
    }
    m_Type = type;

    m_Palette = Palette(type);
    m_Palette.Read(input, m_MipmapOffsets);

    m_Mipmaps.reserve(16);
    int i = 0;
    while (input.ReadUInt32() != 0)
    {
        input.SetPosition(input.GetPosition() - 4);

        assert(input.GetPosition() == m_MipmapOffsets[i]);
        m_Mipmaps.emplace_back(input, m_MipmapOffsets[i++]);
    }
    if (input.ReadUInt16() != 0)
        throw std::runtime_error("Expected two more zero's at end of file.");
}

std::vector<uint8_t> PAA::GetARGB32PixelData(const std::vector<uint8_t>& paaData, bool isPac, int mipmapIndex)
{
    PAA paa(paaData, isPac);
    return GetARGB32PixelData(paa, paaData, mipmapIndex);
}

std::vector<uint8_t> PAA::GetARGB32PixelData(const PAA& paa, const std::vector<uint8_t>& paaData, int mipmapIndex)
{
    const Mipmap& mipmap = paa[mipmapIndex];
    return GetARGB32PixelData(paa, paaData, mipmap);
}

std::vector<uint8_t> PAA::GetARGB32PixelData(const PAA& paa, const std::vector<uint8_t>& paaData, const Mipmap& mipmap)
{
    FBinaryReader input(paaData);
    std::vector<uint8_t> rawData = mipmap.GetRawPixelData(input, paa.GetType());

    std::vector<uint8_t> argbPixels;
    switch (paa.GetType())
    {
    case PAAType::RGBA_8888:
    case PAAType::P8:
        return PixelFormatConversion::P8ToARGB32(rawData, paa.GetPalette()); // never uses swizzlingwd
    case PAAType::DXT1:
    case PAAType::DXT2:
    case PAAType::DXT3:
    case PAAType::DXT4:
    case PAAType::DXT5:
        argbPixels = PixelFormatConversion::DXTToARGB32(rawData, mipmap.GetWidth(), mipmap.GetHeight(), static_cast<int>(paa.GetType()));
        break;
    case PAAType::RGBA_4444:
        argbPixels = PixelFormatConversion::ARGB16ToARGB32(rawData);
        break;
    case PAAType::RGBA_5551:
        argbPixels = PixelFormatConversion::ARGB1555ToARGB32(rawData);
        break;
    case PAAType::AI88:
        argbPixels = PixelFormatConversion::AI88ToARGB32(rawData);
        break;
    default:
        throw std::runtime_error("Cannot retrieve pixel data from this PaaType: " + std::to_string(static_cast<int>(paa.GetType())));
    }

    ChannelSwizzling::Apply(argbPixels, paa.GetPalette().GetChannelSwizzle());
    return argbPixels;
}
