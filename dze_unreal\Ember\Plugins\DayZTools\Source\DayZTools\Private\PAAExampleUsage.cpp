#include "PAA.h"
#include "PixelFormatConversion.h"
#include <string>
#include <vector>
#include <fstream>

// Example function to convert a PNG file to PAA
bool ConvertPNGToPAA(const std::string& pngFilePath, const std::string& paaFilePath, PAAType paaType)
{
    
    // 4. Save the PAA file
    return false;
}

// Example function to convert a PAA file to PNG
bool ConvertPAAToPNG(const std::string& paaFilePath, const std::string& pngFilePath)
{
    
    return true;
}

// Example function to create a PAA file from scratch
bool CreatePAAFromScratch(const std::string& paaFilePath, PAAType paaType)
{
    return false;
}
