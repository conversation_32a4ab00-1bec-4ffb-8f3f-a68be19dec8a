#include "PAAImporter.h"
#include "PAA.h"
#include "PAAType.h"
#include "Engine/Texture2D.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Engine/Texture.h"
#include "Misc/FileHelper.h"
#include "HAL/FileManager.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"
#include "PackageTools.h" // For UPackageTools
#include "DayZToolsSettings.h" // Ensure this is correctly included and implemented

// Constructor
UPAAImporter::UPAAImporter(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = UTexture2D::StaticClass();
    Formats.Add(TEXT("paa;PAA Texture File"));
    bCreateNew = false;
    bEditorImport = true;
}

UObject* UPAAImporter::FactoryCreateFile(
    UClass* InClass,
    UObject* InParent,
    FName InName,
    EObjectFlags Flags,
    const FString& Filename,
    const TCHAR* Parms,
    FFeedbackContext* Warn,
    bool& bOutOperationCanceled
)
{
    CurrentFilename = Filename;
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *Filename))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load file: %s"), *Filename);
        return nullptr;
    }

    const uint8* Buffer = FileData.GetData();
    const uint8* BufferEnd = Buffer + FileData.Num();

    bOutOperationCanceled = false;

    return FactoryCreateBinary(InClass, InParent, InName, Flags, nullptr, nullptr, Buffer, BufferEnd, Warn);
}

UObject* UPAAImporter::FactoryCreateBinary(
    UClass* InClass,
    UObject* InParent,
    FName InName,
    EObjectFlags Flags,
    UObject* Context,
    const TCHAR* Type,
    const uint8*& Buffer,
    const uint8* BufferEnd,
    FFeedbackContext* Warn
)
{
    if (CurrentFilename.IsEmpty())
    {
        //UE_LOG(LogTemp, Error, TEXT("CurrentFilename is not set."));
        return nullptr;
    }

    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString TrunkPath = FPaths::ConvertRelativePathToFull(Settings->ProjectTrunkPath);
    FString FullFilePathNormalized = NormalizePath(CurrentFilename);
    FString TrunkPathNormalized = NormalizePath(Settings->ProjectTrunkPath);

    if (FullFilePathNormalized.StartsWith(TEXT("P:/")))
    {
        FullFilePathNormalized = FullFilePathNormalized.Replace(TEXT("P:/"), *TrunkPathNormalized);
    }

    FullFilePathNormalized = FullFilePathNormalized.TrimEnd();
    TrunkPathNormalized = TrunkPathNormalized.TrimEnd();
    FullFilePathNormalized = FullFilePathNormalized.Replace(TEXT("\\"), TEXT("/"));
    TrunkPathNormalized = TrunkPathNormalized.Replace(TEXT("\\"), TEXT("/"));

    FString RelativeAssetPath;
    if (FullFilePathNormalized.StartsWith(TrunkPathNormalized))
    {
        RelativeAssetPath = FullFilePathNormalized.RightChop(TrunkPathNormalized.Len());
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("FullFilePath does not start with TrunkPath"));
        RelativeAssetPath = FPaths::GetCleanFilename(FullFilePathNormalized);
    }

    RelativeAssetPath = RelativeAssetPath.TrimStartAndEnd();
    if (RelativeAssetPath.StartsWith(TEXT("/")))
    {
        RelativeAssetPath = RelativeAssetPath.RightChop(1);
    }

    FString ContentPath = FString::Printf(TEXT("/Game/%s"), *FPaths::GetPath(RelativeAssetPath));
    ContentPath = ContentPath.TrimEnd();
    if (ContentPath.EndsWith(TEXT("/")))
    {
        ContentPath = ContentPath.LeftChop(1);
    }

    ContentPath = UPackageTools::SanitizePackageName(ContentPath);

    FString ContentRelativePath = ContentPath.RightChop(5); // Remove "/Game" from the start
    FString TextureFilename = FPaths::GetCleanFilename(CurrentFilename);
    FString ContentRelativeTexturePath = FPaths::Combine(ContentRelativePath, TextureFilename);
    ContentRelativeTexturePath = ContentRelativeTexturePath.Replace(TEXT("\\"), TEXT("/"));

    if (ContentRelativeTexturePath.StartsWith(TEXT("/")))
    {
        ContentRelativeTexturePath = ContentRelativeTexturePath.RightChop(1);
    }

    //UE_LOG(LogTemp, Warning, TEXT("ContentRelativeTexturePath: %s"), *ContentRelativeTexturePath);

    // Log the final content path
    //UE_LOG(LogTemp, Warning, TEXT("Final ContentPath: %s"), *ContentPath);

    FString AbsoluteContentPath = FPackageName::LongPackageNameToFilename(ContentPath, FPackageName::GetAssetPackageExtension());
    if (!IFileManager::Get().DirectoryExists(*AbsoluteContentPath))
    {
        if (!IFileManager::Get().MakeDirectory(*AbsoluteContentPath, true))
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to create directory: %s"), *AbsoluteContentPath);
            return nullptr;
        }
    }

    FString PackageName = ContentPath + "/" + FPaths::GetBaseFilename(RelativeAssetPath);
    PackageName = UPackageTools::SanitizePackageName(PackageName);

    UPackage* FinalPackage = CreatePackage(*PackageName);
    if (!FinalPackage)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to create or find the package: %s"), *PackageName);
        return nullptr;
    }

    FString FullAssetPath = PackageName + TEXT(".") + InName.ToString();
    UTexture2D* ExistingTexture = Cast<UTexture2D>(StaticFindObject(UTexture2D::StaticClass(), nullptr, *FullAssetPath, false));

    if (ExistingTexture)
    {
        //UE_LOG(LogTemp, Log, TEXT("Existing texture found: %s. Overwriting."), *FullAssetPath);

        if (ImportTexture(ExistingTexture, Buffer, BufferEnd))
        {
            ExistingTexture->PostEditChange();
            ExistingTexture->MarkPackageDirty();
            return ExistingTexture;
        }
        return nullptr;
    }

    UTexture2D* NewTexture = NewObject<UTexture2D>(FinalPackage, InClass, InName, Flags);
    if (ImportTexture(NewTexture, Buffer, BufferEnd))
    {
        // Notify the asset registry about the new asset
        FAssetRegistryModule::AssetCreated(NewTexture);
        FinalPackage->MarkPackageDirty();
        return NewTexture;
    }

    return nullptr;
}

bool UPAAImporter::ImportTexture(UTexture2D* Texture, const uint8*& Buffer, const uint8* BufferEnd)
{
    int32 BufferSize = BufferEnd - Buffer;
    //UE_LOG(LogTemp, Log, TEXT("Importing PAA Texture..."));
    //UE_LOG(LogTemp, Log, TEXT("PAA Buffer Size: %d"), BufferSize);

    std::vector<uint8_t> BufferVector(Buffer, Buffer + BufferSize);
    PAA PaaFile(BufferVector, false);

    int32 Width = PaaFile.GetMipmaps()[0].GetWidth();
    int32 Height = PaaFile.GetMipmaps()[0].GetHeight();
    //UE_LOG(LogTemp, Log, TEXT("PAA Dimensions: %d x %d"), Width, Height);

    std::vector<uint8_t> PixelData = PaaFile.GetARGB32PixelData(BufferVector, false, 0);

    Texture->Source.Init(Width, Height, 1, 1, TSF_BGRA8);
    uint8* MipData = Texture->Source.LockMip(0);
    FMemory::Memcpy(MipData, PixelData.data(), PixelData.size());
    Texture->Source.UnlockMip(0);

    FString TextureName = Texture->GetName();

    if (TextureName.EndsWith(TEXT("_ca")) || TextureName.EndsWith(TEXT("_co")) || TextureName.EndsWith(TEXT("_mc")))
    {
        Texture->SRGB = true;
        Texture->CompressionSettings = TC_Default;
    }
    else if (TextureName.EndsWith(TEXT("_no")) || TextureName.EndsWith(TEXT("_nohq")) || TextureName.EndsWith(TEXT("_nopx")))
    {
        Texture->SRGB = false;
        Texture->CompressionSettings = TC_Normalmap;
    }
    else
    {
        Texture->SRGB = false;
        Texture->CompressionSettings = TC_Masks;
    }

    Texture->MipGenSettings = TMGS_FromTextureGroup;
    Texture->UpdateResource();

    Texture->MarkPackageDirty();

    return true;
}

FString UPAAImporter::NormalizePath(const FString& Path)
{
    FString NormalizedPath = Path.Replace(TEXT("\\"), TEXT("/"));
    NormalizedPath = FPaths::ConvertRelativePathToFull(NormalizedPath);
    return NormalizedPath;
}

bool UPAAImporter::DoesSupportClass(UClass* Class)
{
    return Class == UTexture2D::StaticClass();
}

UClass* UPAAImporter::ResolveSupportedClass()
{
    return UTexture2D::StaticClass();
}
