#include "PCGSpeciesDistributor.h"
#include "PCGContext.h"
#include "Data/PCGPointData.h"
#include "Metadata/PCGMetadata.h"
#include "Metadata/PCGMetadataAccessor.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "LandscapeLayerInfoObject.h"
#include "BiomeData.h"
#include "Engine/AssetManager.h"

UPCGSpeciesDistributorSettings::UPCGSpeciesDistributorSettings()
{
    bUseSeed = true; // Use seed for deterministic placement
}

FPCGElementPtr UPCGSpeciesDistributorSettings::CreateElement() const
{
    return MakeShared<FPCGSpeciesDistributorElement>();
}

TArray<FPCGPinProperties> UPCGSpeciesDistributorSettings::InputPinProperties() const
{
    TArray<FPCGPinProperties> Properties;
    FPCGPinProperties& InputPin = Properties.Emplace_GetRef(PCGPinConstants::DefaultInputLabel, EPCGDataType::Point);
    InputPin.SetRequiredPin();
    return Properties;
}

TArray<FPCGPinProperties> UPCGSpeciesDistributorSettings::OutputPinProperties() const
{
    TArray<FPCGPinProperties> Properties;
    Properties.Emplace(PCGPinConstants::DefaultOutputLabel, EPCGDataType::Point);
    return Properties;
}

float FPCGSpeciesDistributorElement::CalculateAgeFromViability(
    const FVector& Location,
    const TArray<FPCGPoint>& AllPoints,
    const UPCGMetadata* Metadata,
    const FBiomeSpecies& Species,
    const UPCGSpeciesDistributorSettings* Settings) const
{
    // Use settings
    float ViabilityThreshold = Settings->ViabilityEdgeThreshold;
    float MaxAgeDistance = Settings->MaxAgeDistance;
    // Find the nearest edge where viability drops below threshold
    float MinDistanceToEdge = FLT_MAX;
    bool bIsInsideViableArea = false;

    // First check if current location is viable
    for (const FPCGPoint& Point : AllPoints)
    {
        float Distance = FVector::Dist2D(Location, Point.Transform.GetLocation());
        if (Distance < 100.0f) // Within sampling distance
        {
            float PointViability = CalculateSpeciesViability(Point, Metadata, Species);
            if (PointViability >= ViabilityThreshold)
            {
                bIsInsideViableArea = true;
                break;
            }
        }
    }

    // Find distance to nearest edge
    for (const FPCGPoint& Point : AllPoints)
    {
        float PointViability = CalculateSpeciesViability(Point, Metadata, Species);
        bool bPointIsViable = PointViability >= ViabilityThreshold;

        // We're looking for edge transitions
        if (bIsInsideViableArea != bPointIsViable)
        {
            float Distance = FVector::Dist2D(Location, Point.Transform.GetLocation());
            MinDistanceToEdge = FMath::Min(MinDistanceToEdge, Distance);
        }
    }

    // Normalize distance to 0-1 range based on max age distance
    //float MaxAgeDistance = 500.0f; // This could be exposed as a setting
    float NormalizedAge = FMath::Clamp(MinDistanceToEdge / MaxAgeDistance, 0.0f, 1.0f);

    return bIsInsideViableArea ? NormalizedAge : 0.0f;
}

bool FPCGSpeciesDistributorElement::ExecuteInternal(FPCGContext* Context) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FPCGSpeciesDistributorElement::Execute);

    const UPCGSpeciesDistributorSettings* Settings = Context->GetInputSettings<UPCGSpeciesDistributorSettings>();
    check(Settings);

    // Get input points
    const TArray<FPCGTaggedData> Inputs = Context->InputData.GetInputsByPin(PCGPinConstants::DefaultInputLabel);
    if (Inputs.IsEmpty())
    {
        return true;
    }

    const UPCGPointData* InputPointData = Cast<UPCGPointData>(Inputs[0].Data);
    if (!InputPointData)
    {
        return true;
    }

    const TArray<FPCGPoint>& InputPoints = InputPointData->GetPoints();
    const UPCGMetadata* InputMetadata = InputPointData->ConstMetadata();

    // Get biome attributes
    const FPCGMetadataAttributeBase* MainBiomeAttr = InputMetadata->GetConstAttribute(TEXT("MainBiome"));
    const FPCGMetadataAttributeBase* SubBiomeAttr = InputMetadata->GetConstAttribute(TEXT("SubBiome"));

    if (!MainBiomeAttr)
    {
        PCGE_LOG(Error, GraphAndLog, FText::FromString("No MainBiome attribute found. Run BiomeSelector first."));
        return true;
    }

    TArray<UBiomeData*> LoadedBiomes;
    TArray<FAssetData> BiomeAssets;

    // Grab the asset registry
    FAssetRegistryModule& AssetRegistryModule =
        FModuleManager::LoadModuleChecked<FAssetRegistryModule>("AssetRegistry");

    // Build a TopLevelAssetPath for UBiomeData
    FTopLevelAssetPath BiomeClassPath = UBiomeData::StaticClass()->GetClassPathName();

    // Now call the new overload:
    AssetRegistryModule.Get().GetAssetsByClass(
        BiomeClassPath,    // <-- FTopLevelAssetPath, not FName
        BiomeAssets,
        /* bSearchSubClasses = */ true
    );

    for (const FAssetData& AssetData : BiomeAssets)
    {
        if (UBiomeData* BiomeData = Cast<UBiomeData>(AssetData.GetAsset()))
        {
            LoadedBiomes.Add(BiomeData);
            UE_LOG(LogTemp, Warning, TEXT("Loaded Biome: %s"), *BiomeData->BiomeName);
        }
    }

    if (LoadedBiomes.Num() == 0)
    {
        PCGE_LOG(Error, GraphAndLog, FText::FromString("No biome data assets found!"));
        return true;
    }

    // Collect all species candidates
    TArray<FSpeciesCandidate> AllCandidates;

    for (int32 PointIndex = 0; PointIndex < InputPoints.Num(); ++PointIndex)
    {
        const FPCGPoint& Point = InputPoints[PointIndex];

        // Get biome name for this point
        FString BiomeName;
        if (MainBiomeAttr->GetTypeId() == PCG::Private::MetadataTypes<FString>::Id)
        {
            const FPCGMetadataAttribute<FString>* StringAttr = static_cast<const FPCGMetadataAttribute<FString>*>(MainBiomeAttr);
            BiomeName = StringAttr->GetValueFromItemKey(Point.MetadataEntry);
        }

        // Find matching biome data
        UBiomeData* MatchingBiome = nullptr;
        for (UBiomeData* BiomeData : LoadedBiomes)
        {
            if (BiomeData->BiomeName == BiomeName)
            {
                MatchingBiome = BiomeData;
                break;
            }
        }

        if (!MatchingBiome)
        {
            UE_LOG(LogTemp, Warning, TEXT("No matching biome found for: %s"), *BiomeName);
            continue;
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("Found biome %s with %d species"), *BiomeName, MatchingBiome->Species.Num());
        }

        // In ExecuteInternal, replace the species candidate creation loop:
        // Create candidates for each species in the biome
        for (const FBiomeSpecies& Species : MatchingBiome->Species)
        {
            // Skip species with no sizes defined
            if (Species.Sizes.Num() == 0)
            {
                UE_LOG(LogTemp, Warning, TEXT("Species %s has no sizes defined, skipping"), *Species.SpeciesName);
                continue;
            }

            float Viability = CalculateSpeciesViability(Point, InputMetadata, Species);
            UE_LOG(LogTemp, Warning, TEXT("Species %s viability: %f (min required: %f)"),
                *Species.SpeciesName, Viability, Settings->MinimumViability);

            if (Viability >= Settings->MinimumViability)
            {
                // Calculate size selector based on settings
                float SizeSelector = Viability;  // Default to viability only
                float Age = 0.0f;

                if (Settings->bUseAgeForSizing)
                {
                    // Calculate age (distance from edge)
                    Age = CalculateAgeFromViability(Point.Transform.GetLocation(), InputPoints, InputMetadata, Species, Settings);

                    // Combine viability and age for size selection
                    SizeSelector = FMath::Lerp(Viability, Age, Settings->AgeWeight);

                    UE_LOG(LogTemp, VeryVerbose, TEXT("Point at %s: Viability=%f, Age=%f, SizeSelector=%f"),
                        *Point.Transform.GetLocation().ToString(), Viability, Age, SizeSelector);
                }

                // Select size based on size selector
                int32 SizeIndex = 0;
                if (Species.Sizes.Num() > 1)
                {
                    // Invert so high values = larger trees (first in array)
                    float InvertedSelector = 1.0f - SizeSelector;
                    float SizeStep = 1.0f / Species.Sizes.Num();
                    SizeIndex = FMath::Clamp(
                        FMath::FloorToInt(InvertedSelector / SizeStep),
                        0,
                        Species.Sizes.Num() - 1
                    );
                }

                const FVegetationSize& SelectedSize = Species.Sizes[SizeIndex];

                // Skip if no variations defined for this size
                if (SelectedSize.Variations.Num() == 0)
                {
                    UE_LOG(LogTemp, Warning, TEXT("Species %s size %s has no variations, skipping"),
                        *Species.SpeciesName, *SelectedSize.SizeName);
                    continue;
                }

                // Select variation based on weights
                int32 VariationIndex = 0;
                if (SelectedSize.Variations.Num() > 1)
                {
                    // Calculate total weight
                    float TotalWeight = 0.0f;
                    for (const FVegetationVariation& Var : SelectedSize.Variations)
                    {
                        TotalWeight += Var.Weight;
                    }

                    // Random selection based on weights
                    float RandomValue = FMath::FRand() * TotalWeight;
                    float AccumulatedWeight = 0.0f;

                    for (int32 i = 0; i < SelectedSize.Variations.Num(); ++i)
                    {
                        AccumulatedWeight += SelectedSize.Variations[i].Weight;
                        if (RandomValue <= AccumulatedWeight)
                        {
                            VariationIndex = i;
                            break;
                        }
                    }
                }

                // Create candidates based on density
                FVector PointExtents = Point.GetScaledExtents();
                float AreaSquareUnits = FMath::Max(PointExtents.X * PointExtents.Y, 10000.0f);
                int32 NumCandidates = FMath::Max(1, FMath::RoundToInt(Species.DensityPerSquareMeter * AreaSquareUnits / 10000.0f));

                UE_LOG(LogTemp, Warning, TEXT("Species %s: Size %s (index %d), Density %f, Area %f, Candidates %d"),
                    *Species.SpeciesName, *SelectedSize.SizeName, SizeIndex, Species.DensityPerSquareMeter, AreaSquareUnits, NumCandidates);

                for (int32 i = 0; i < NumCandidates; ++i)
                {
                    FSpeciesCandidate Candidate;
                    Candidate.Location = Point.Transform.GetLocation();

                    // Add random offset within point bounds
                    FVector Offset = FVector(
                        FMath::RandRange(-Point.GetScaledExtents().X * 0.5f, Point.GetScaledExtents().X * 0.5f),
                        FMath::RandRange(-Point.GetScaledExtents().Y * 0.5f, Point.GetScaledExtents().Y * 0.5f),
                        0.0f
                    );
                    Candidate.Location += Offset;

                    Candidate.Species = Species;
                    Candidate.Viability = Viability;
                    Candidate.Priority = Species.Priority;
                    Candidate.OriginalPointIndex = PointIndex;
                    Candidate.SizeIndex = SizeIndex;
                    Candidate.VariationIndex = VariationIndex;
                    // Store age if you want to use it later
                    Candidate.Age = Age;  // You'll need to add float Age to FSpeciesCandidate struct

                    AllCandidates.Add(Candidate);
                }
            }
        }
    }

    // Run species competition
    RunSpeciesCompetition(AllCandidates, Settings);

    // Create output points for winning candidates
    UPCGPointData* OutputData = NewObject<UPCGPointData>();
    TArray<FPCGPoint>& OutputPoints = OutputData->GetMutablePoints();

    // Create metadata attributes
    UPCGMetadata* OutputMetadata = OutputData->MutableMetadata();
    FPCGMetadataAttribute<FString>* SpeciesNameAttr = OutputMetadata->CreateAttribute<FString>(TEXT("SpeciesName"), TEXT(""), false, false);
    FPCGMetadataAttribute<float>* SpeciesViabilityAttr = OutputMetadata->CreateAttribute<float>(TEXT("SpeciesViability"), 0.0f, true, false);
    FPCGMetadataAttribute<float>* SpeciesScaleAttr = OutputMetadata->CreateAttribute<float>(TEXT("SpeciesScale"), 1.0f, true, false);
    FPCGMetadataAttribute<FSoftObjectPath>* MeshAttr = OutputMetadata->CreateAttribute<FSoftObjectPath>(TEXT("Mesh"), FSoftObjectPath(), /*bAllowsInterpolation=*/false, /*bOverrideParent=*/false);
    // Add these metadata attributes after the existing ones:
    FPCGMetadataAttribute<FString>* SizeNameAttr = OutputMetadata->CreateAttribute<FString>(TEXT("SizeName"), TEXT(""), false, false);
    FPCGMetadataAttribute<bool>* AlignToTerrainAttr = OutputMetadata->CreateAttribute<bool>(TEXT("AlignToTerrain"), true, false, false);
    // Add these with other metadata attributes
    FPCGMetadataAttribute<bool>* ModifiesTerrainAttr = OutputMetadata->CreateAttribute<bool>(TEXT("ModifiesTerrain"), false, false, false);
    FPCGMetadataAttribute<float>* TerrainDeformHeightAttr = OutputMetadata->CreateAttribute<float>(TEXT("TerrainDeformHeight"), 0.0f, true, false);
    FPCGMetadataAttribute<float>* TerrainDeformRadiusAttr = OutputMetadata->CreateAttribute<float>(TEXT("TerrainDeformRadius"), 0.0f, true, false);
    FPCGMetadataAttribute<FSoftObjectPath>* TerrainLayerAttr = OutputMetadata->CreateAttribute<FSoftObjectPath>(TEXT("TerrainLayer"), FSoftObjectPath(), false, false);

    // In the loop where you create output points:
    for (const FSpeciesCandidate& Candidate : AllCandidates)
    {
        // Get the selected size and variation
        const FVegetationSize& SelectedSize = Candidate.Species.Sizes[Candidate.SizeIndex];
        const FVegetationVariation& SelectedVariation = SelectedSize.Variations[Candidate.VariationIndex];

        // Allocate metadata entry
        auto EntryKey = OutputMetadata->AddEntry();

        // Create the point
        FPCGPoint& NewPoint = OutputPoints.Emplace_GetRef();
        NewPoint.MetadataEntry = EntryKey;
        NewPoint.Transform.SetLocation(Candidate.Location);

        // Apply scale from size
        float Scale = SelectedSize.ScaleMultiplier;
        NewPoint.Transform.SetScale3D(FVector(Scale));

        // Random rotation if enabled for this variation
        if (SelectedVariation.bRandomRotation)
        {
            NewPoint.Transform.SetRotation(FQuat(FVector::UpVector, FMath::RandRange(0.0f, 360.0f) * PI / 180.0f));
        }

        // In the loop where you create output points, add this after setting other metadata:
        ModifiesTerrainAttr->SetValue(EntryKey, Candidate.Species.bModifiesTerrain);
        if (Candidate.Species.bModifiesTerrain)
        {
            TerrainDeformHeightAttr->SetValue(EntryKey, Candidate.Species.TerrainDeformationHeight);
            TerrainDeformRadiusAttr->SetValue(EntryKey, Candidate.Species.TerrainDeformationRadius);

            if (Candidate.Species.TerrainLayer)
            {
                // Build a softobject path from the UObjects full reference path
                FString LayerPath = Candidate.Species.TerrainLayer->GetPathName();
                FSoftObjectPath LayerSoftPath(LayerPath);

                // Now write it into metadata: remember SetValue needs (Key, Value)
                TerrainLayerAttr->SetValue(EntryKey, LayerSoftPath);
            }
        }

        // Set metadata
        SpeciesNameAttr->SetValue(EntryKey, Candidate.Species.SpeciesName);
        SpeciesViabilityAttr->SetValue(EntryKey, Candidate.Viability);
        SpeciesScaleAttr->SetValue(EntryKey, Scale);
        SizeNameAttr->SetValue(EntryKey, SelectedSize.SizeName);
        AlignToTerrainAttr->SetValue(EntryKey, SelectedVariation.bAlignToTerrain);

        // Set the mesh
        MeshAttr->SetValue(EntryKey, SelectedVariation.Mesh.ToSoftObjectPath());
    }

    // Add output
    FPCGTaggedData& Output = Context->OutputData.TaggedData.Emplace_GetRef();
    Output.Data = OutputData;

    return true;
}

float FPCGSpeciesDistributorElement::CalculateSpeciesViability(
    const FPCGPoint& Point,
    const UPCGMetadata* Metadata,
    const FBiomeSpecies& Species) const
{
    if (!Metadata || Species.ViabilityDataGroups.Num() == 0)
    {
        return 0.0f;
    }

    float TotalViability = 0.0f;

    // Process each data group
    for (const FViabilityDataGroup& DataGroup : Species.ViabilityDataGroups)
    {
        if (DataGroup.Attributes.Num() == 0)
        {
            continue;
        }

        float GroupViability = 1.0f; // Start at 1 for multiplication

        // Process each attribute in the group (multiply together)
        for (const FViabilityAttribute& ViabilityAttr : DataGroup.Attributes)
        {
            float AttributeValue = 0.0f;

            // Get attribute value
            FString AttributeName;
            switch (ViabilityAttr.Attribute)
            {
                case ETerrainAttribute::Altitude: AttributeName = TEXT("Altitude"); break;
                case ETerrainAttribute::Slope: AttributeName = TEXT("Slope"); break;
                case ETerrainAttribute::Aspect: AttributeName = TEXT("Aspect"); break;
                case ETerrainAttribute::Curvature: AttributeName = TEXT("Curvature"); break;
                case ETerrainAttribute::Occlusion: AttributeName = TEXT("Occlusion"); break;
                case ETerrainAttribute::FlowAccumulation: AttributeName = TEXT("FlowAccumulation"); break;
                case ETerrainAttribute::WindExposure: AttributeName = TEXT("WindExposure"); break;
                default: continue;
            }

            const FPCGMetadataAttributeBase* AttributeBase = Metadata->GetConstAttribute(FName(*AttributeName));
            if (AttributeBase && AttributeBase->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
            {
                const FPCGMetadataAttribute<float>* FloatAttribute = static_cast<const FPCGMetadataAttribute<float>*>(AttributeBase);
                AttributeValue = FloatAttribute->GetValueFromItemKey(Point.MetadataEntry);
            }

            // Apply power
            float ProcessedValue = AttributeValue;
            if (FMath::Abs(ViabilityAttr.Power) > SMALL_NUMBER)
            {
                if (ViabilityAttr.Power < 0)
                {
                    // Negative power inverts the value first
                    ProcessedValue = 1.0f - ProcessedValue;
                    ProcessedValue = FMath::Pow(ProcessedValue, FMath::Abs(ViabilityAttr.Power));
                }
                else
                {
                    ProcessedValue = FMath::Pow(ProcessedValue, ViabilityAttr.Power);
                }
            }

            // Apply ramp/curve if available
            if (ViabilityAttr.Ramp.GetRichCurveConst()->GetNumKeys() > 0)
            {
                ProcessedValue = ViabilityAttr.Ramp.GetRichCurveConst()->Eval(ProcessedValue);
            }

            // Multiply into group viability
            GroupViability *= ProcessedValue;
        }

        // Apply noise if enabled
        if (DataGroup.Noise.bEnabled)
        {
            // Simple 2D noise based on world position
            FVector2D NoisePos = FVector2D(Point.Transform.GetLocation().X, Point.Transform.GetLocation().Y);
            NoisePos = (NoisePos + DataGroup.Noise.Offset) / DataGroup.Noise.Scale;

            // Use Perlin noise (you can use FMath::PerlinNoise2D or a custom implementation)
            float NoiseValue = FMath::PerlinNoise2D(NoisePos);
            NoiseValue = (NoiseValue + 1.0f) * 0.5f; // Remap from [-1,1] to [0,1]
            NoiseValue *= DataGroup.Noise.Amplitude;

            // Multiply noise with group viability
            GroupViability *= NoiseValue;
        }

        // Apply group weight and add to total
        TotalViability += GroupViability * DataGroup.GroupWeight;
    }

    // Clamp final result
    return FMath::Clamp(TotalViability, 0.0f, 1.0f);
}

void FPCGSpeciesDistributorElement::RunSpeciesCompetition(TArray<FSpeciesCandidate>& Candidates, const UPCGSpeciesDistributorSettings* Settings) const
{
    if (!Settings->bUsePrioritySystem && !Settings->bUseViabilityRadius)
    {
        return; // No competition needed
    }

    // Sort candidates by priority first, then by viability
    Candidates.Sort([](const FSpeciesCandidate& A, const FSpeciesCandidate& B)
        {
            if (A.Priority != B.Priority)
            {
                return A.Priority > B.Priority;
            }
            return A.Viability > B.Viability;
        });

    // Spatial hash for efficient radius checking
    TMap<FIntVector, TArray<int32>> SpatialHash;
    const float CellSize = Settings->SpatialHashCellSize;

    TArray<FSpeciesCandidate> WinningCandidates;

    for (const FSpeciesCandidate& Candidate : Candidates)
    {
        bool bCanPlace = true;
        FIntVector Cell = FIntVector(
            FMath::FloorToInt(Candidate.Location.X / CellSize),
            FMath::FloorToInt(Candidate.Location.Y / CellSize),
            0
        );

        if (Settings->bUseViabilityRadius)
        {
            // Check neighboring cells
            int32 SearchRadius = FMath::CeilToInt(FMath::Max(Candidate.Species.PriorityRadius, Candidate.Species.ViabilityRadius) / CellSize);

            for (int32 dx = -SearchRadius; dx <= SearchRadius && bCanPlace; dx++)
            {
                for (int32 dy = -SearchRadius; dy <= SearchRadius && bCanPlace; dy++)
                {
                    FIntVector CheckCell = Cell + FIntVector(dx, dy, 0);
                    if (const TArray<int32>* Indices = SpatialHash.Find(CheckCell))
                    {
                        for (int32 Index : *Indices)
                        {
                            const FSpeciesCandidate& Existing = WinningCandidates[Index];
                            float Distance = FVector::Dist2D(Candidate.Location, Existing.Location);

                            // Check priority radius
                            if (Settings->bUsePrioritySystem && Distance < Existing.Species.PriorityRadius)
                            {
                                if (Existing.Priority >= Candidate.Priority)
                                {
                                    bCanPlace = false;
                                    break;
                                }
                            }

                            // Check viability radius for same priority
                            if (Candidate.Priority == Existing.Priority && Distance < Existing.Species.ViabilityRadius)
                            {
                                bCanPlace = false;
                                break;
                            }
                        }
                    }
                }
            }
        }

        if (bCanPlace)
        {
            int32 Index = WinningCandidates.Add(Candidate);
            SpatialHash.FindOrAdd(Cell).Add(Index);
        }
    }

    // Replace candidates with winners
    Candidates = MoveTemp(WinningCandidates);
}