#include "PCGTerrainAnalyzer.h"
#include "PCGSubsystem.h"
#include "PCGComponent.h"
#include "PCGContext.h"
#include "PCGParamData.h"
#include "Data/PCGSpatialData.h"
#include "Data/PCGPointData.h"
#include "Helpers/PCGAsync.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeComponent.h"
#include "LandscapeDataAccess.h"
#include "TerrainCache.h"
#include "Engine/World.h"
#include "Async/ParallelFor.h"
#include "EngineUtils.h"
#include "Metadata/PCGMetadata.h"
#include "Metadata/PCGMetadataAccessor.h"

UPCGTerrainAnalyzerSettings::UPCGTerrainAnalyzerSettings()
{
    bUseSeed = false;
}

FPCGElementPtr UPCGTerrainAnalyzerSettings::CreateElement() const
{
    return MakeShared<FPCGTerrainAnalyzerElement>();
}

TArray<FPCGPinProperties> UPCGTerrainAnalyzerSettings::InputPinProperties() const
{
    TArray<FPCGPinProperties> Properties;
    FPCGPinProperties& InputPin = Properties.Emplace_GetRef(PCGPinConstants::DefaultInputLabel, EPCGDataType::Spatial);
    InputPin.SetRequiredPin();
    return Properties;
}

TArray<FPCGPinProperties> UPCGTerrainAnalyzerSettings::OutputPinProperties() const
{
    TArray<FPCGPinProperties> Properties;
    Properties.Emplace(PCGPinConstants::DefaultOutputLabel, EPCGDataType::Point);
    return Properties;
}

bool FPCGTerrainAnalyzerElement::ExecuteInternal(FPCGContext* Context) const
{
    TRACE_CPUPROFILER_EVENT_SCOPE(FPCGTerrainAnalyzerElement::Execute);

    const UPCGTerrainAnalyzerSettings* Settings = Context->GetInputSettings<UPCGTerrainAnalyzerSettings>();
    check(Settings);

    // Get landscape
    UWorld* World = Context->SourceComponent.IsValid() ? Context->SourceComponent->GetWorld() : nullptr;
    if (!World)
    {
        PCGE_LOG(Error, GraphAndLog, FText::FromString("No valid world context"));
        return true;
    }

    ALandscapeProxy* Landscape = nullptr;
    for (TActorIterator<ALandscapeProxy> It(World); It; ++It)
    {
        Landscape = *It;
        break;
    }

    if (!Landscape)
    {
        PCGE_LOG(Error, GraphAndLog, FText::FromString("No landscape found in world"));
        return true;
    }

    // Get input spatial data
    const TArray<FPCGTaggedData> Inputs = Context->InputData.GetInputsByPin(PCGPinConstants::DefaultInputLabel);
    if (Inputs.IsEmpty())
    {
        return true;
    }

    const UPCGSpatialData* SpatialData = Cast<UPCGSpatialData>(Inputs[0].Data);
    if (!SpatialData)
    {
        return true;
    }

    // Get or create point data
    const UPCGPointData* PointData = SpatialData->ToPointData(Context);
    if (!PointData)
    {
        return true;
    }

    // Create output data
    UPCGPointData* OutputData = NewObject<UPCGPointData>();
    OutputData->InitializeFromData(PointData);
    TArray<FPCGPoint>& OutputPoints = OutputData->GetMutablePoints();

    // Copy input points
    const TArray<FPCGPoint>& InputPoints = PointData->GetPoints();
    OutputPoints = InputPoints;

    // Create metadata attributes
    UPCGMetadata* Metadata = OutputData->MutableMetadata();

    // Create all attribute entries based on settings
    bool bCalculateAll = (Settings->AnalysisMode == EPCGTerrainAnalysisMode::All);

    FPCGMetadataAttribute<float>* SlopeAttribute = nullptr;
    FPCGMetadataAttribute<float>* AspectAttribute = nullptr;
    FPCGMetadataAttribute<float>* OcclusionAttribute = nullptr;
    FPCGMetadataAttribute<float>* FlowAttribute = nullptr;
    FPCGMetadataAttribute<float>* CurvatureAttribute = nullptr;
    FPCGMetadataAttribute<float>* WindAttribute = nullptr;
    FPCGMetadataAttribute<float>* AltitudeAttribute = nullptr;

    if (bCalculateAll || Settings->AnalysisMode == EPCGTerrainAnalysisMode::Slope || Settings->bCalculateSlope)
    {
        SlopeAttribute = Metadata->CreateAttribute<float>(TEXT("Slope"), 0.0f, /*bAllowsInterpolation=*/true, /*bOverrideParent=*/false);
    }

    if (bCalculateAll || Settings->bCalculateAspect)
    {
        AspectAttribute = Metadata->CreateAttribute<float>(TEXT("Aspect"), 0.0f, true, false);
    }

    if (bCalculateAll || Settings->AnalysisMode == EPCGTerrainAnalysisMode::Occlusion || Settings->bCalculateOcclusion)
    {
        OcclusionAttribute = Metadata->CreateAttribute<float>(TEXT("Occlusion"), 0.0f, true, false);
    }

    if (bCalculateAll || Settings->AnalysisMode == EPCGTerrainAnalysisMode::FlowAccumulation || Settings->bCalculateFlowAccumulation)
    {
        FlowAttribute = Metadata->CreateAttribute<float>(TEXT("FlowAccumulation"), 0.0f, true, false);
    }

    if (bCalculateAll || Settings->AnalysisMode == EPCGTerrainAnalysisMode::Curvature || Settings->bCalculateCurvature)
    {
        CurvatureAttribute = Metadata->CreateAttribute<float>(TEXT("Curvature"), 0.0f, true, false);
    }

    if (bCalculateAll || Settings->bCalculateWindExposure)
    {
        WindAttribute = Metadata->CreateAttribute<float>(TEXT("WindExposure"), 0.0f, true, false);
    }

    // Always create altitude
    AltitudeAttribute = Metadata->CreateAttribute<float>(TEXT("Altitude"), 0.0f, true, false);

    // Process points
    if (Settings->bParallelExecution && InputPoints.Num() > Settings->ParallelBatchSize)
    {
        // Parallel execution
        FCriticalSection Mutex;
        const int32 NumBatches = FMath::DivideAndRoundUp(InputPoints.Num(), Settings->ParallelBatchSize);

        ParallelFor(NumBatches, [&](int32 BatchIndex)
            {
                const int32 StartIndex = BatchIndex * Settings->ParallelBatchSize;
                const int32 EndIndex = FMath::Min(StartIndex + Settings->ParallelBatchSize, InputPoints.Num());

                for (int32 i = StartIndex; i < EndIndex; ++i)
                {
                    FPCGPoint& Point = OutputPoints[i];
                    const FVector Location = Point.Transform.GetLocation();

                    // Calculate attributes
                    if (SlopeAttribute)
                    {
                        float Slope = CalculateSlope(Location, Landscape);
                        SlopeAttribute->SetValue(Point.MetadataEntry, Slope);
                    }

                    if (AspectAttribute)
                    {
                        float Aspect = CalculateAspect(Location, Landscape);
                        AspectAttribute->SetValue(Point.MetadataEntry, Aspect);
                    }

                    if (OcclusionAttribute)
                    {
                        float Occlusion = CalculateOcclusion(Location, Landscape, Settings->OcclusionRadius,
                            Settings->OcclusionSamples, Settings->OcclusionAngleBias);
                        OcclusionAttribute->SetValue(Point.MetadataEntry, Occlusion);
                    }

                    if (FlowAttribute)
                    {
                        float Flow = CalculateFlowAccumulation(Location, Landscape, Settings->FlowIterations, Settings->FlowThreshold);
                        FlowAttribute->SetValue(Point.MetadataEntry, Flow);
                    }

                    if (CurvatureAttribute)
                    {
                        float Curvature = CalculateCurvature(Location, Landscape);
                        CurvatureAttribute->SetValue(Point.MetadataEntry, Curvature);
                    }

                    if (WindAttribute)
                    {
                        float WindExposure = CalculateWindExposure(Location, Landscape, Settings->PrevailingWindDirection,
                            Settings->WindExposureRadius);
                        WindAttribute->SetValue(Point.MetadataEntry, WindExposure);
                    }

                    if (AltitudeAttribute)
                    {
                        float Height = GetHeightAtLocation(Location, Landscape);
                        float NormalizedAltitude = FMath::Clamp((Height + 1000.0f) / 4000.0f, 0.0f, 1.0f);
                        AltitudeAttribute->SetValue(Point.MetadataEntry, NormalizedAltitude);

                        // Update point height
                        Point.Transform.SetLocation(FVector(Location.X, Location.Y, Height));
                    }
                }
            });
    }
    else
    {
        // Sequential execution
        for (int32 i = 0; i < OutputPoints.Num(); ++i)
        {
            FPCGPoint& Point = OutputPoints[i];
            const FVector Location = Point.Transform.GetLocation();

            // Calculate attributes
            if (SlopeAttribute)
            {
                float Slope = CalculateSlope(Location, Landscape);
                SlopeAttribute->SetValue(Point.MetadataEntry, Slope);
            }

            if (AspectAttribute)
            {
                float Aspect = CalculateAspect(Location, Landscape);
                AspectAttribute->SetValue(Point.MetadataEntry, Aspect);
            }

            if (OcclusionAttribute)
            {
                float Occlusion = CalculateOcclusion(Location, Landscape, Settings->OcclusionRadius,
                    Settings->OcclusionSamples, Settings->OcclusionAngleBias);
                OcclusionAttribute->SetValue(Point.MetadataEntry, Occlusion);
            }

            if (FlowAttribute)
            {
                float Flow = CalculateFlowAccumulation(Location, Landscape, Settings->FlowIterations, Settings->FlowThreshold);
                FlowAttribute->SetValue(Point.MetadataEntry, Flow);
            }

            if (CurvatureAttribute)
            {
                float Curvature = CalculateCurvature(Location, Landscape);
                CurvatureAttribute->SetValue(Point.MetadataEntry, Curvature);
            }

            if (WindAttribute)
            {
                float WindExposure = CalculateWindExposure(Location, Landscape, Settings->PrevailingWindDirection,
                    Settings->WindExposureRadius);
                WindAttribute->SetValue(Point.MetadataEntry, WindExposure);
            }

            if (AltitudeAttribute)
            {
                float Height = GetHeightAtLocation(Location, Landscape);
                float NormalizedAltitude = FMath::Clamp((Height + 1000.0f) / 4000.0f, 0.0f, 1.0f);
                AltitudeAttribute->SetValue(Point.MetadataEntry, NormalizedAltitude);

                // Update point height
                Point.Transform.SetLocation(FVector(Location.X, Location.Y, Height));
            }
        }
    }

    // Add output
    FPCGTaggedData& Output = Context->OutputData.TaggedData.Emplace_GetRef();
    Output.Data = OutputData;

    return true;
}

float FPCGTerrainAnalyzerElement::CalculateSlope(const FVector& Location, ALandscapeProxy* Landscape) const
{
    FVector Normal = GetNormalAtLocation(Location, Landscape);

    // Ensure normal is valid
    if (Normal.IsNearlyZero())
    {
        return 0.0f;
    }

    Normal.Normalize();

    // Calculate angle between normal and up vector
    float CosAngle = FVector::DotProduct(Normal, FVector::UpVector);
    CosAngle = FMath::Clamp(CosAngle, -1.0f, 1.0f);

    float SlopeDegrees = FMath::RadiansToDegrees(FMath::Acos(CosAngle));
    return FMath::Clamp(SlopeDegrees / 90.0f, 0.0f, 1.0f);
}

float FPCGTerrainAnalyzerElement::CalculateAspect(const FVector& Location, ALandscapeProxy* Landscape) const
{
    FVector Normal = GetNormalAtLocation(Location, Landscape);

    // Project normal to horizontal plane
    FVector2D HorizontalNormal(Normal.X, Normal.Y);
    if (HorizontalNormal.IsNearlyZero())
    {
        return 0.0f; // Flat surface, no aspect
    }

    HorizontalNormal.Normalize();

    // Calculate angle from north (Y axis)
    float Angle = FMath::Atan2(HorizontalNormal.X, HorizontalNormal.Y);
    if (Angle < 0) Angle += 2.0f * PI;

    return Angle / (2.0f * PI); // Normalize to 0-1
}

float FPCGTerrainAnalyzerElement::CalculateOcclusion(const FVector& Location, ALandscapeProxy* Landscape,
    float Radius, int32 Samples, float AngleBias) const
{
    float TotalOcclusion = 0.0f;
    const float AngleStep = 2.0f * PI / Samples;
    const float BiasRadians = FMath::DegreesToRadians(AngleBias);

    FVector BaseLocation = Location;
    BaseLocation.Z = GetHeightAtLocation(Location, Landscape);

    for (int32 i = 0; i < Samples; ++i)
    {
        float Angle = i * AngleStep;
        FVector Direction(FMath::Cos(Angle), FMath::Sin(Angle), 0.0f);

        // Sample multiple distances
        float MaxElevation = -BiasRadians;
        for (float Distance = Radius * 0.1f; Distance <= Radius; Distance += Radius * 0.1f)
        {
            FVector SampleLocation = BaseLocation + Direction * Distance;
            float SampleHeight = GetHeightAtLocation(SampleLocation, Landscape);

            float ElevationAngle = FMath::Atan2(SampleHeight - BaseLocation.Z, Distance);
            MaxElevation = FMath::Max(MaxElevation, ElevationAngle);
        }

        // Convert to occlusion value
        TotalOcclusion += FMath::Clamp(MaxElevation / (PI * 0.5f) + 0.5f, 0.0f, 1.0f);
    }

    return 1.0f - (TotalOcclusion / Samples); // Invert so 1 = fully occluded
}

float FPCGTerrainAnalyzerElement::CalculateFlowAccumulation(const FVector& Location, ALandscapeProxy* Landscape,
    int32 Iterations, float Threshold) const
{
    const float CellSize = 100.0f;
    float AccumulatedFlow = 0.0f;

    const FVector2D Directions[8] = {
        FVector2D(0, 1), FVector2D(1, 1), FVector2D(1, 0), FVector2D(1, -1),
        FVector2D(0, -1), FVector2D(-1, -1), FVector2D(-1, 0), FVector2D(-1, 1)
    };

    const float DiagonalWeight = 1.0f / FMath::Sqrt(2.0f);
    const float CardinalWeight = 1.0f;

    float CurrentHeight = GetHeightAtLocation(Location, Landscape);

    for (int32 iter = 0; iter < Iterations; ++iter)
    {
        float IterationRadius = CellSize * FMath::Pow(2.0f, iter);

        for (int32 dir = 0; dir < 8; ++dir)
        {
            FVector SampleLocation = Location + FVector(Directions[dir].X, Directions[dir].Y, 0) * IterationRadius;
            float SampleHeight = GetHeightAtLocation(SampleLocation, Landscape);

            if (SampleHeight > CurrentHeight + Threshold)
            {
                float Weight = (dir % 2 == 0) ? CardinalWeight : DiagonalWeight;
                AccumulatedFlow += Weight * (SampleHeight - CurrentHeight) / IterationRadius;
            }
        }
    }

    return FMath::Clamp(AccumulatedFlow / Iterations, 0.0f, 1.0f);
}

float FPCGTerrainAnalyzerElement::CalculateCurvature(const FVector& Location, ALandscapeProxy* Landscape) const
{
    const float SampleDistance = 50.0f;

    // Get heights at cardinal points
    float HeightCenter = GetHeightAtLocation(Location, Landscape);
    float HeightNorth = GetHeightAtLocation(Location + FVector(0, SampleDistance, 0), Landscape);
    float HeightSouth = GetHeightAtLocation(Location + FVector(0, -SampleDistance, 0), Landscape);
    float HeightEast = GetHeightAtLocation(Location + FVector(SampleDistance, 0, 0), Landscape);
    float HeightWest = GetHeightAtLocation(Location + FVector(-SampleDistance, 0, 0), Landscape);

    // Calculate second derivatives
    float D2x = (HeightEast - 2 * HeightCenter + HeightWest) / (SampleDistance * SampleDistance);
    float D2y = (HeightNorth - 2 * HeightCenter + HeightSouth) / (SampleDistance * SampleDistance);

    // Mean curvature
    float Curvature = (D2x + D2y) * 0.5f;

    // Better normalization based on actual curvature values
    // Positive curvature = convex (hills), negative = concave (valleys)
    return FMath::Clamp(Curvature * 50.0f + 0.5f, 0.0f, 1.0f);
}

float FPCGTerrainAnalyzerElement::CalculateWindExposure(const FVector& Location, ALandscapeProxy* Landscape,
    const FVector& WindDir, float Radius) const
{
    FVector WindDirection = WindDir.GetSafeNormal();
    const int32 Samples = 10;
    float TotalExposure = 0.0f;

    FVector BaseLocation = Location;
    BaseLocation.Z = GetHeightAtLocation(Location, Landscape);

    for (int32 i = 1; i <= Samples; ++i)
    {
        float Distance = (Radius * i) / Samples;
        FVector SampleLocation = BaseLocation - WindDirection * Distance;
        float SampleHeight = GetHeightAtLocation(SampleLocation, Landscape);

        // Check if wind is blocked
        float HeightDifference = BaseLocation.Z - SampleHeight;
        float Angle = FMath::Atan2(HeightDifference, Distance);

        // More negative angle = more exposed
        float Exposure = FMath::Clamp(1.0f + (Angle / (PI * 0.25f)), 0.0f, 1.0f);
        TotalExposure += Exposure;
    }

    return TotalExposure / Samples;
}

float FPCGTerrainAnalyzerElement::GetHeightAtLocation(const FVector& Location, ALandscapeProxy* Landscape) const
{
    if (!Landscape) return Location.Z;

    // Use collision query which is more reliable
    FVector Start = Location + FVector(0, 0, 10000.0f);
    FVector End = Location - FVector(0, 0, 10000.0f);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnFaceIndex = false;
    QueryParams.bReturnPhysicalMaterial = false;

    // Use World line trace instead of Actor line trace for better results
    if (Landscape->GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECC_Visibility,  // Use Visibility channel for landscape
        QueryParams))
    {
        // Make sure we hit the landscape
        if (Cast<ALandscapeProxy>(HitResult.GetActor()))
        {
            return HitResult.Location.Z;
        }
    }

    return Location.Z;
}


FVector FPCGTerrainAnalyzerElement::GetNormalAtLocation(const FVector& Location, ALandscapeProxy* Landscape) const
{
    if (!Landscape) return FVector::UpVector;

    // Use line trace to get the normal directly
    FVector Start = Location + FVector(0, 0, 1000.0f);
    FVector End = Location - FVector(0, 0, 1000.0f);

    FHitResult HitResult;
    FCollisionQueryParams QueryParams;
    QueryParams.bTraceComplex = true;
    QueryParams.bReturnFaceIndex = true;

    if (Landscape->GetWorld()->LineTraceSingleByChannel(
        HitResult,
        Start,
        End,
        ECC_Visibility,
        QueryParams))
    {
        if (Cast<ALandscapeProxy>(HitResult.GetActor()))
        {
            return HitResult.Normal;
        }
    }

    // Fallback to calculating normal from height samples
    const float SampleOffset = 100.0f;

    float HeightCenter = GetHeightAtLocation(Location, Landscape);
    float HeightRight = GetHeightAtLocation(Location + FVector(SampleOffset, 0, 0), Landscape);
    float HeightForward = GetHeightAtLocation(Location + FVector(0, SampleOffset, 0), Landscape);

    FVector TangentX = FVector(SampleOffset, 0, HeightRight - HeightCenter);
    FVector TangentY = FVector(0, SampleOffset, HeightForward - HeightCenter);

    FVector Normal = FVector::CrossProduct(TangentY, TangentX).GetSafeNormal();
    return Normal;
}