#include "PCGTerrainModifier.h"
#include "PCGContext.h"
#include "PCGComponent.h"
#include "Data/PCGPointData.h"
#include "Metadata/PCGMetadata.h"
#include "Metadata/PCGMetadataAccessor.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "LandscapeComponent.h"
#include "LandscapeEdit.h"
#include "LandscapeEditorModule.h"
#include "LandscapeDataAccess.h"
#include "Engine/World.h"
#include "EngineUtils.h"
#include "DrawDebugHelpers.h"
#include "PCGPin.h" // Added for PCGPinConstants, assuming it was implicitly included or needed
#include "Logging/LogMacros.h" // For UE_LOG

#if WITH_EDITOR
#include "LandscapeEditorUtils.h"
#include "LandscapeInfo.h"
#include "LandscapeLayerInfoObject.h" // Added for ULandscapeLayerInfoObject, assuming it was implicitly included or needed
#endif

// Assuming FLandscapeTerrainModification is defined in PCGTerrainModifier.h or similar
// struct FLandscapeTerrainModification
// {
//     FVector Location;
//     float Height;
//     float Radius;
//     ULandscapeLayerInfoObject* LayerInfo;
// };


UPCGTerrainModifierSettings::UPCGTerrainModifierSettings()
{
    bUseSeed = false;
    // Assuming bDebugDraw, bApplyHeightChanges, bApplyLayerWeights, HeightBlendFalloff, LayerWeightStrength
    // are members of UPCGTerrainModifierSettings and initialized in its header or here.
    // If they are not, the original code would not compile where they are used.
    // For this minimal fix, I'm assuming they exist as per the original code's usage.
    // Example initializations if they were meant to be here:
    // bDebugDraw = false;
    // bApplyHeightChanges = true;
    // bApplyLayerWeights = true;
    // HeightBlendFalloff = 1.0f;
    // LayerWeightStrength = 1.0f;
}

FPCGElementPtr UPCGTerrainModifierSettings::CreateElement() const
{
    return MakeShared<FPCGTerrainModifierElement>();
}

TArray<FPCGPinProperties> UPCGTerrainModifierSettings::InputPinProperties() const
{
    TArray<FPCGPinProperties> Properties;
    FPCGPinProperties& InputPin = Properties.Emplace_GetRef(PCGPinConstants::DefaultInputLabel, EPCGDataType::Point);
    InputPin.SetRequiredPin();
    return Properties;
}

TArray<FPCGPinProperties> UPCGTerrainModifierSettings::OutputPinProperties() const
{
    TArray<FPCGPinProperties> Properties;
    Properties.Emplace(PCGPinConstants::DefaultOutputLabel, EPCGDataType::Point);
    return Properties;
}

bool FPCGTerrainModifierElement::ExecuteInternal(FPCGContext* Context) const
{
#if WITH_EDITOR
    TRACE_CPUPROFILER_EVENT_SCOPE(FPCGTerrainModifierElement::Execute);

    // 1) Grab settings
    const UPCGTerrainModifierSettings* Settings = Context->GetInputSettings<UPCGTerrainModifierSettings>();
    check(Settings);

    // 2) Grab point data or pass through
    const TArray<FPCGTaggedData> Inputs = Context->InputData.GetInputsByPin(PCGPinConstants::DefaultInputLabel);
    if (Inputs.IsEmpty() || !Inputs[0].Data)
    {
        return true;
    }
    const UPCGPointData* InputPointData = Cast<UPCGPointData>(Inputs[0].Data);
    if (!InputPointData)
    {
        FPCGTaggedData& Out = Context->OutputData.TaggedData.Emplace_GetRef();
        Out.Data = Inputs[0].Data;
        return true;
    }

    // 3) Find landscape in the world
    UWorld* World = Context->SourceComponent.IsValid()
        ? Context->SourceComponent->GetWorld()
        : nullptr;
    if (!World)
    {
        FPCGTaggedData& Out = Context->OutputData.TaggedData.Emplace_GetRef();
        Out.Data = const_cast<UPCGPointData*>(InputPointData);
        return true;
    }
    ALandscape* Landscape = nullptr;
    for (TActorIterator<ALandscape> It(World); It; ++It)
    {
        Landscape = *It;
        break;
    }
    if (!Landscape)
    {
        PCGE_LOG(Warning, GraphAndLog, FText::FromString("No landscape found"));
        FPCGTaggedData& Out = Context->OutputData.TaggedData.Emplace_GetRef();
        Out.Data = const_cast<UPCGPointData*>(InputPointData);
        return true;
    }

    // 4) Ensure edit-layers are enabled
    if (!Landscape->HasLayersContent())
    {
        PCGE_LOG(Error, GraphAndLog, FText::FromString("Landscape must have Edit Layers enabled"));
        FPCGTaggedData& Out = Context->OutputData.TaggedData.Emplace_GetRef();
        Out.Data = const_cast<UPCGPointData*>(InputPointData);
        return true;
    }

    // 5) Find or create our PCG layer
    const FName PCGLayerName = TEXT("PCG_TerrainModifications");
    int32 LayerIndex = Landscape->GetLayerIndex(PCGLayerName);
    if (LayerIndex == INDEX_NONE)
    {
        LayerIndex = Landscape->CreateLayer(PCGLayerName);
        if (LayerIndex == INDEX_NONE)
        {
            PCGE_LOG(Error, GraphAndLog, FText::FromString("Failed to create PCG_TerrainModifications layer"));
            FPCGTaggedData& Out = Context->OutputData.TaggedData.Emplace_GetRef();
            Out.Data = const_cast<UPCGPointData*>(InputPointData);
            return true;
        }
    }

    const FLandscapeLayer* Layer = Landscape->GetLayer(PCGLayerName);
    UE_LOG(LogTemp, Warning, TEXT("GetLayer returned %s"), Layer ? TEXT("VALID pointer") : TEXT("nullptr"));
    if (!Layer)
    {

        PCGE_LOG(Error, GraphAndLog,
            FText::Format(FText::FromString("Layer '{0}' not found"), FText::FromName(PCGLayerName)));
        FPCGTaggedData& Out = Context->OutputData.TaggedData.Emplace_GetRef();
        Out.Data = const_cast<UPCGPointData*>(InputPointData);
        return true;
    }

    // 6) Clear it exactly once at the start of this entire graph run
    static bool bLayerCleared = false;
    if (!bLayerCleared)
    {
        UE_LOG(LogTemp, Warning, TEXT("=== PCG: Clearing PCG_TerrainModifications layer ==="));
        Landscape->ClearLayer(LayerIndex);
        //Landscape->SetEditLayerVisible(PCGLayerName, true);
        bLayerCleared = true;
    }

    // 7) Gather all modifications from metadata
    TArray<FLandscapeTerrainModification> Mods;
    const UPCGMetadata* Meta = InputPointData->ConstMetadata();
    const auto* ModifiesAttr = Meta ? Meta->GetConstAttribute(TEXT("ModifiesTerrain")) : nullptr;
    const auto* HeightAttr = Meta ? Meta->GetConstAttribute(TEXT("TerrainDeformHeight")) : nullptr;
    const auto* RadiusAttr = Meta ? Meta->GetConstAttribute(TEXT("TerrainDeformRadius")) : nullptr;
    const auto* LayerPathAttr = Meta ? Meta->GetConstAttribute(TEXT("TerrainLayer")) : nullptr;

    for (const FPCGPoint& P : InputPointData->GetPoints())
    {
        bool bMod = false;
        if (ModifiesAttr && ModifiesAttr->GetTypeId() == PCG::Private::MetadataTypes<bool>::Id)
        {
            bMod = static_cast<const FPCGMetadataAttribute<bool>*>(ModifiesAttr)
                ->GetValueFromItemKey(P.MetadataEntry);
        }
        if (!bMod)
            continue;

        FLandscapeTerrainModification M;
        M.Location = P.Transform.GetLocation();
        M.Height = (HeightAttr && HeightAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
            ? static_cast<const FPCGMetadataAttribute<float>*>(HeightAttr)
            ->GetValueFromItemKey(P.MetadataEntry)
            : 0.0f;
        M.Radius = (RadiusAttr && RadiusAttr->GetTypeId() == PCG::Private::MetadataTypes<float>::Id)
            ? static_cast<const FPCGMetadataAttribute<float>*>(RadiusAttr)
            ->GetValueFromItemKey(P.MetadataEntry)
            : 100.0f;
        M.LayerInfo = nullptr;
        if (LayerPathAttr && LayerPathAttr->GetTypeId() == PCG::Private::MetadataTypes<FSoftObjectPath>::Id)
        {
            FSoftObjectPath Path = static_cast<const FPCGMetadataAttribute<FSoftObjectPath>*>(LayerPathAttr)
                ->GetValueFromItemKey(P.MetadataEntry);
            M.LayerInfo = Cast<ULandscapeLayerInfoObject>(Path.TryLoad());
        }

        Mods.Add(M);

        if (Settings->bDebugDraw)
        {
            DrawDebugSphere(World, M.Location, M.Radius, 16, FColor::Yellow, false, 5.0f);
            DrawDebugLine(World, M.Location, M.Location + FVector(0, 0, M.Height * 100.0f),
                FColor::Red, false, 5.0f, 0, 2.0f);
        }
    }

    // 8) Apply height + weight mods under a Scoped layer switch
    if (Mods.Num() > 0)
    {
        FScopedSetLandscapeEditingLayer Scope(
            Landscape,
            Layer->Guid,
            [Landscape]()
            {
                Landscape->RequestLayersContentUpdate(ELandscapeLayerUpdateMode::Update_All);
            });
        ApplyModificationsToLandscape(Landscape, Mods, Settings);
    }

    // 9) Finally, pass through the points
    FPCGTaggedData& Out = Context->OutputData.TaggedData.Emplace_GetRef();
    Out.Data = const_cast<UPCGPointData*>(InputPointData);
    return true;

#else
    // In non-editor builds, just pass the input through
    const TArray<FPCGTaggedData> InputsNE = Context->InputData.GetInputsByPin(PCGPinConstants::DefaultInputLabel);
    if (!InputsNE.IsEmpty())
    {
        FPCGTaggedData& Out = Context->OutputData.TaggedData.Emplace_GetRef();
        Out.Data = InputsNE[0].Data;
    }
    return true;
#endif
}





#if WITH_EDITOR
void FPCGTerrainModifierElement::ApplyModificationsToLandscape(
    ALandscape* Landscape,
    const TArray<FLandscapeTerrainModification>& Modifications,
    const UPCGTerrainModifierSettings* Settings) const
{
    if (!Landscape || Modifications.Num() == 0)
    {
        return;
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        return;
    }

    // Process height modifications
    // Assuming Settings->bApplyHeightChanges exists
    if (Settings->bApplyHeightChanges)
    {
        for (const FLandscapeTerrainModification& Mod : Modifications)
        {
            if (FMath::Abs(Mod.Height) < SMALL_NUMBER) // Original check
            {
                continue;
            }
            // Assuming Settings->HeightBlendFalloff exists
            ApplyHeightModification(Landscape, LandscapeInfo, Mod.Location, Mod.Height, Mod.Radius, Settings->HeightBlendFalloff);
        }
    }

    // Process layer weight modifications
    // Assuming Settings->bApplyLayerWeights exists
    if (Settings->bApplyLayerWeights)
    {
        for (const FLandscapeTerrainModification& Mod : Modifications)
        {
            if (!Mod.LayerInfo)
            {
                continue;
            }
            // Assuming Settings->LayerWeightStrength and Settings->HeightBlendFalloff exist
            ApplyLayerWeightModification(Landscape, LandscapeInfo, Mod.Location, Mod.Radius,
                Mod.LayerInfo, Settings->LayerWeightStrength, Settings->HeightBlendFalloff);
        }
    }
}

void FPCGTerrainModifierElement::ApplyHeightModification(
    ALandscape* Landscape,
    ULandscapeInfo* LandscapeInfo,
    const FVector& WorldLocation,
    float Height,
    float Radius,
    float Falloff) const
{
    if (!Landscape || !LandscapeInfo || Radius <= 0.0f)
    {
        return;
    }

    // 1) Actor origin & scale
    const FVector ActorOrigin = Landscape->GetActorLocation();
    const FVector ActorScale = Landscape->GetActorScale3D();

    // 2) Convert worldspace point into landscape grid coords
    FVector Local = (WorldLocation - ActorOrigin) / ActorScale;
    int32 CenterX = FMath::RoundToInt(Local.X);
    int32 CenterY = FMath::RoundToInt(Local.Y);
    int32 RadiusInGrid = FMath::CeilToInt(Radius / ActorScale.X);

    // 3) Clamp to true landscape extent
    FIntRect Extent;
    if (!LandscapeInfo->GetLandscapeExtent(Extent))
    {
        return;
    }

    int32 MinX = FMath::Clamp(CenterX - RadiusInGrid, Extent.Min.X, Extent.Max.X);
    int32 MinY = FMath::Clamp(CenterY - RadiusInGrid, Extent.Min.Y, Extent.Max.Y);
    int32 MaxX = FMath::Clamp(CenterX + RadiusInGrid, Extent.Min.X, Extent.Max.X);
    int32 MaxY = FMath::Clamp(CenterY + RadiusInGrid, Extent.Min.Y, Extent.Max.Y);

    const int32 NumCols = MaxX - MinX + 1;
    const int32 NumRows = MaxY - MinY + 1;
    if (NumCols <= 0 || NumRows <= 0)
    {
        return;
    }

    // 4) Read existing height data
    TArray<uint16> HeightData;
    HeightData.SetNum(NumCols * NumRows);

    FLandscapeEditDataInterface Edit(LandscapeInfo);
    Edit.GetHeightData(MinX, MinY, MaxX, MaxY, HeightData.GetData(), NumCols);

    // 5) Apply falloffbased delta in *local* cm so that `Height` is in meters:
    //
    //    meters  cm:  Height(m) * 100
    //    account for actor Zscale:   ActorScale.Z
    //    final delta = (Height * 100 / ActorScale.Z) * Alpha
    const float MetersToLocalCm = 100.0f / ActorScale.Z;

    for (int32 Row = 0; Row < NumRows; ++Row)
    {
        for (int32 Col = 0; Col < NumCols; ++Col)
        {
            int32 Index = Row * NumCols + Col;

            // worldpos of this texel
            FVector VertexWorld = ActorOrigin +
                FVector((MinX + Col) * ActorScale.X,
                    (MinY + Row) * ActorScale.Y,
                    0.0f);

            float Dist = FVector::Dist2D(VertexWorld, WorldLocation);
            if (Dist > Radius)
            {
                continue;
            }

            float Alpha = 1.0f - FMath::Pow(Dist / Radius, Falloff);

            // fetch current, compute new in localcm
            uint16 Current = HeightData[Index];
            float  CurrentCm = LandscapeDataAccess::GetLocalHeight(Current);
            float  DeltaCm = Height * MetersToLocalCm * Alpha;
            float  NewCm = CurrentCm + DeltaCm;

            HeightData[Index] = LandscapeDataAccess::GetTexHeight(NewCm);
        }
    }

    UE_LOG(LogTemp, Warning,
        TEXT("Wrote %dx%d height samples at (%d,%d)"),
        NumCols, NumRows, MinX, MinY);

    // 6) Write back & flush
    Edit.SetHeightData(
        MinX, MinY, MaxX, MaxY,
        HeightData.GetData(),
        NumCols,            // Stride
        true,               // bCalcNormals
        nullptr, nullptr, nullptr,
        true,               // bCreateComponents
        nullptr, nullptr,
        true,               // bUpdateBounds
        true,               // bUpdateCollision
        false               // bGenerateMips
    );

    Edit.Flush();
}


void FPCGTerrainModifierElement::ApplyLayerWeightModification(
    ALandscape* Landscape,
    ULandscapeInfo* LandscapeInfo,
    const FVector& Location,
    float Radius,
    ULandscapeLayerInfoObject* LayerInfo,
    float Strength,
    float Falloff) const
{
    if (!Landscape || !LandscapeInfo || !LayerInfo || Radius <= 0.0f)
    {
        return;
    }

    const FVector ActorOrigin = Landscape->GetActorLocation();
    const FVector ActorScale = Landscape->GetActorScale3D();
    const FVector Local = (Location - ActorOrigin) / ActorScale;

    int32 CenterX = FMath::RoundToInt(Local.X);
    int32 CenterY = FMath::RoundToInt(Local.Y);
    int32 RadiusGrid = FMath::CeilToInt(Radius / ActorScale.X);

    FIntRect Extent;
    if (!LandscapeInfo->GetLandscapeExtent(Extent))
    {
        return;
    }

    int32 MinX = FMath::Clamp(CenterX - RadiusGrid, Extent.Min.X, Extent.Max.X);
    int32 MinY = FMath::Clamp(CenterY - RadiusGrid, Extent.Min.Y, Extent.Max.Y);
    int32 MaxX = FMath::Clamp(CenterX + RadiusGrid, Extent.Min.X, Extent.Max.X);
    int32 MaxY = FMath::Clamp(CenterY + RadiusGrid, Extent.Min.Y, Extent.Max.Y);

    int32 Width = MaxX - MinX + 1;
    int32 Height = MaxY - MinY + 1;
    if (Width <= 0 || Height <= 0)
    {
        return;
    }

    FLandscapeEditDataInterface Edit(LandscapeInfo);

    // Get ALL layers
    const TArray<FLandscapeInfoLayerSettings>& LayerSettings = LandscapeInfo->Layers;

    // Process each layer
    for (const FLandscapeInfoLayerSettings& LayerSetting : LayerSettings)
    {
        if (!LayerSetting.LayerInfoObj)
            continue;

        TArray<uint8> WeightData;
        WeightData.SetNum(Width * Height);

        // Read current weights
        int32 X1 = MinX, Y1 = MinY, X2 = MaxX, Y2 = MaxY;
        Edit.GetWeightData(
            LayerSetting.LayerInfoObj,
            X1, Y1, X2, Y2,
            WeightData.GetData(),
            Width
        );

        // Modify weights
        bool bModified = false;
        for (int32 Row = 0; Row < Height; ++Row)
        {
            for (int32 Col = 0; Col < Width; ++Col)
            {
                int32 Index = Row * Width + Col;

                FVector TexWorld = ActorOrigin +
                    FVector((MinX + Col) * ActorScale.X,
                        (MinY + Row) * ActorScale.Y,
                        0.0f);

                float Dist = FVector::Dist2D(TexWorld, Location);
                if (Dist > Radius) continue;

                float Alpha = 1.0f - FMath::Pow(Dist / Radius, Falloff);

                if (LayerSetting.LayerInfoObj == LayerInfo)
                {
                    // This is our target layer - set to full weight
                    uint8 NewWeight = (uint8)FMath::Clamp(FMath::RoundToInt(Strength * Alpha * 255.0f), 0, 255);
                    WeightData[Index] = NewWeight;
                    bModified = true;
                }
                else
                {
                    // Other layers - set to zero where we're painting our target layer
                    if (Alpha > 0.01f && Strength > 0.01f)
                    {
                        // Reduce by the amount we're painting the target layer
                        float Reduction = 1.0f - (Strength * Alpha);
                        uint8 NewWeight = (uint8)FMath::Clamp(FMath::RoundToInt(WeightData[Index] * Reduction), 0, 255);
                        WeightData[Index] = NewWeight;
                        bModified = true;
                    }
                }
            }
        }

        if (bModified)
        {
            // Write back with explicit replace mode
            X1 = MinX; Y1 = MinY; X2 = MaxX; Y2 = MaxY;

            // Use raw data write to ensure no blending
            Edit.SetAlphaData(
                LayerSetting.LayerInfoObj,
                X1, Y1, X2, Y2,
                WeightData.GetData(),
                Width,
                ELandscapeLayerPaintingRestriction::None,
                false,  // bWeightAdjust = false
                false   // bTotalWeightAdjust = false
            );
        }
    }

    Edit.Flush();
}


#endif // WITH_EDITOR
