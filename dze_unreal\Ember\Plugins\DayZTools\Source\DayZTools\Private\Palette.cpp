#include "Palette.h"
#include "ColorHelper.h"
#include "BinaryReader.h" // Your implementation of BinaryReader
#include <cassert>
#include <stdexcept>
#include <array>

Palette::Palette(PAAType format)
    : MaxColor(0xffffffff), ChannelSwizzle(ARGBSwizzle::Default)
{
    switch (format)
    {
    case PAAType::RGBA_4444:
    case PAAType::RGBA_8888:
    case PAAType::AI88:
        AverageColor = PackedColor(0x80c02020);
        break;
    default:
        AverageColor = PackedColor(0xff802020);
        break;
    }
}

void Palette::Read(FBinaryReader& input, std::array<int, 16>& startOffsets)
{
    // Read Taggs
    while (input.ReadAscii(4) == "GGAT")
    {
        std::string taggName = input.ReadAscii(4);
        int32_t taggSize = input.ReadInt32();

        if (taggName == "CXAM") // MAXC
        {
            assert(taggSize == 4);
            MaxColor = PackedColor(input.ReadUInt32());
        }
        else if (taggName == "CGVA") // AVGC
        {
            assert(taggSize == 4);
            AverageColor = PackedColor(input.ReadUInt32());
        }
        else if (taggName == "GALF") // FLAG
        {
            assert(taggSize == 4);
            int32_t flags = input.ReadInt32();
            IsAlpha = (flags & PicFlagAlpha) != 0;
            IsTransparent = (flags & PicFlagTransparent) != 0;
        }
        else if (taggName == "SFFO") // OFFS
        {
            int nOffs = taggSize / sizeof(int32_t);
            for (int i = 0; i < nOffs; i++)
            {
                startOffsets[i] = input.ReadInt32();
            }
        }
        else if (taggName == "ZIWS") // SWIZ
        {
            assert(taggSize == 4);
            ARGBSwizzle newSwizzle;
            newSwizzle.SwizA = static_cast<TexSwizzle>(input.ReadByte());
            newSwizzle.SwizR = static_cast<TexSwizzle>(input.ReadByte());
            newSwizzle.SwizG = static_cast<TexSwizzle>(input.ReadByte());
            newSwizzle.SwizB = static_cast<TexSwizzle>(input.ReadByte());
            ChannelSwizzle = newSwizzle;
        }
        else
        {
            // Just skip the data
            throw std::runtime_error("Unknown PAA tagg: " + taggName);
            input.SetPosition(input.GetPosition() + taggSize);
        }
    }

    input.SetPosition(input.GetPosition() - 4);

    // Read palette colors
    uint16_t nPaletteColors = input.ReadUInt16();
    Colors.resize(nPaletteColors);

    for (int index = 0; index < nPaletteColors; ++index)
    {
        uint8_t b = input.ReadByte();
        uint8_t g = input.ReadByte();
        uint8_t r = input.ReadByte();
        Colors[index] = PackedColor(r, g, b);
    }
}

void Palette::Write(FBinaryWriter& output, std::array<int, 16>& startOffsets)
{
    // Write MAXC tag
    output.WriteAscii("GGAT");
    output.WriteAscii("CXAM"); // MAXC backwards
    output.WriteInt32(4); // Size of the tag data
    output.WriteUInt32(MaxColor.GetValue());

    // Write AVGC tag
    output.WriteAscii("GGAT");
    output.WriteAscii("CGVA"); // AVGC backwards
    output.WriteInt32(4); // Size of the tag data
    output.WriteUInt32(AverageColor.GetValue());

    // Write FLAG tag
    output.WriteAscii("GGAT");
    output.WriteAscii("GALF"); // FLAG backwards
    output.WriteInt32(4); // Size of the tag data
    int32_t flags = 0;
    if (IsAlpha) flags |= PicFlagAlpha;
    if (IsTransparent) flags |= PicFlagTransparent;
    output.WriteInt32(flags);

    // Write OFFS tag
    output.WriteAscii("GGAT");
    output.WriteAscii("SFFO"); // OFFS backwards
    int numOffsets = 0;
    for (int i = 0; i < startOffsets.size(); i++)
    {
        if (startOffsets[i] != 0)
            numOffsets = i + 1;
    }
    output.WriteInt32(numOffsets * sizeof(int32_t)); // Size of the tag data
    for (int i = 0; i < numOffsets; i++)
    {
        output.WriteInt32(startOffsets[i]);
    }

    // Write SWIZ tag
    output.WriteAscii("GGAT");
    output.WriteAscii("ZIWS"); // SWIZ backwards
    output.WriteInt32(4); // Size of the tag data
    output.WriteByte(static_cast<uint8_t>(ChannelSwizzle.SwizA));
    output.WriteByte(static_cast<uint8_t>(ChannelSwizzle.SwizR));
    output.WriteByte(static_cast<uint8_t>(ChannelSwizzle.SwizG));
    output.WriteByte(static_cast<uint8_t>(ChannelSwizzle.SwizB));

    // Write palette colors
    output.WriteUInt16(static_cast<uint16_t>(Colors.size()));
    for (const auto& color : Colors)
    {
        output.WriteByte(color.B8());
        output.WriteByte(color.G8());
        output.WriteByte(color.R8());
    }
}

void Palette::CalculateColors(const std::vector<uint8_t>& argb32Data)
{
    if (argb32Data.empty())
        return;

    // Calculate average and max colors
    uint32_t totalR = 0, totalG = 0, totalB = 0, totalA = 0;
    uint8_t maxR = 0, maxG = 0, maxB = 0, maxA = 0;
    bool hasAlpha = false;

    int numPixels = argb32Data.size() / 4;
    for (int i = 0; i < numPixels; i++)
    {
        uint8_t b = argb32Data[i * 4];
        uint8_t g = argb32Data[i * 4 + 1];
        uint8_t r = argb32Data[i * 4 + 2];
        uint8_t a = argb32Data[i * 4 + 3];

        totalR += r;
        totalG += g;
        totalB += b;
        totalA += a;

        maxR = std::max(maxR, r);
        maxG = std::max(maxG, g);
        maxB = std::max(maxB, b);
        maxA = std::max(maxA, a);

        if (a < 255)
            hasAlpha = true;
    }

    // Set average color
    uint8_t avgR = static_cast<uint8_t>(totalR / numPixels);
    uint8_t avgG = static_cast<uint8_t>(totalG / numPixels);
    uint8_t avgB = static_cast<uint8_t>(totalB / numPixels);
    uint8_t avgA = static_cast<uint8_t>(totalA / numPixels);
    AverageColor = PackedColor(avgA, avgR, avgG, avgB);

    // Set max color
    MaxColor = PackedColor(maxA, maxR, maxG, maxB);

    // Set alpha flag
    IsAlpha = hasAlpha;
}