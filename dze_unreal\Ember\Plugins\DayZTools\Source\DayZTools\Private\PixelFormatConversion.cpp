#include "PixelFormatConversion.h"
#include <stdexcept>
#include <algorithm>

void PixelFormatConversion::SetColor(std::vector<uint8_t>& img, int offset, uint8_t a, uint8_t r, uint8_t g, uint8_t b)
{
    img[offset] = b;
    img[offset + 1] = g;
    img[offset + 2] = r;
    img[offset + 3] = a;
}

void PixelFormatConversion::GetColor(const std::vector<uint8_t>& img, int offset, uint8_t& a, uint8_t& r, uint8_t& g, uint8_t& b)
{
    b = img[offset];
    g = img[offset + 1];
    r = img[offset + 2];
    a = img[offset + 3];
}

std::vector<uint8_t> PixelFormatConversion::ARGB16ToARGB32(const std::vector<uint8_t>& src)
{
    std::vector<uint8_t> dst(src.size() * 2);
    int nPixel = src.size() / 2;
    for (int index = 0; index < nPixel; ++index)
    {
        uint8_t hbyte = src[index * 2 + 1];
        uint8_t lbyte = src[index * 2];
        uint8_t lhbyte = hbyte & 0x0F;
        uint8_t hhbyte = (hbyte & 0xF0) >> 4;
        uint8_t llbyte = lbyte & 0x0F;
        uint8_t hlbyte = (lbyte & 0xF0) >> 4;
        uint8_t b = lhbyte * 255 / 15;
        uint8_t a = hhbyte * 255 / 15;
        uint8_t r = llbyte * 255 / 15;
        uint8_t g = hlbyte * 255 / 15;

        SetColor(dst, index * 4, a, r, g, b);
    }

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::ARGB1555ToARGB32(const std::vector<uint8_t>& src)
{
    std::vector<uint8_t> dst(src.size() * 2);
    int nPixel = src.size() / 2;
    for (int index = 0; index < nPixel; ++index)
    {
        uint16_t s = (src[index * 2 + 1] << 8) | src[index * 2];
        bool abit = ((s & 0x8000) >> 15) == 1;
        uint8_t b5bit = (s & 0x7C00) >> 10;
        uint8_t g5bit = (s & 0x03E0) >> 5;
        uint8_t r5bit = s & 0x001F;
        uint8_t b = b5bit * 255 / 31;
        uint8_t a = abit ? 255 : 0;
        uint8_t r = r5bit * 255 / 31;
        uint8_t g = g5bit * 255 / 31;

        SetColor(dst, index * 4, a, r, g, b);
    }

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::AI88ToARGB32(const std::vector<uint8_t>& src)
{
    std::vector<uint8_t> dst(src.size() * 2);
    int nPixel = src.size() / 2;
    for (int index = 0; index < nPixel; ++index)
    {
        uint8_t grey = src[index * 2];
        uint8_t alpha = src[index * 2 + 1];

        SetColor(dst, index * 4, alpha, grey, grey, grey);
    }

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::P8ToARGB32(const std::vector<uint8_t>& src, const Palette& palette)
{
    std::vector<uint8_t> dst(src.size() * 4);
    const auto& colors = palette.GetColors();
    int nPixel = src.size();
    for (int index = 0; index < nPixel; ++index)
    {
        const auto& color = colors[src[index]];
        SetColor(dst, index * 4, color.A8(), color.R8(), color.G8(), color.B8());
    }

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::DXTToARGB32(const std::vector<uint8_t>& imageData, int width, int height, int dxtType)
{
    std::vector<uint8_t> dst(width * height * 4);
    const uint8_t* imageReader = imageData.data();

    auto DecompressDXTBlock = [&](int x, int y, int blockCountX) {
        switch (dxtType)
        {
        case 1:
            DecompressDxt1Block(imageReader, x, y, blockCountX, width, height, dst);
            break;
        case 2:
        case 3:
            DecompressDxt3Block(imageReader, x, y, blockCountX, width, height, dst);
            break;
        case 4:
        case 5:
            DecompressDxt5Block(imageReader, x, y, blockCountX, width, height, dst);
            break;
        default:
            throw std::runtime_error("DXT type must be between 1 and 5");
        }
        };

    int blockCountX = (width + 3) / 4;
    int blockCountY = (height + 3) / 4;

    for (int y = 0; y < blockCountY; y++)
    {
        for (int x = 0; x < blockCountX; x++)
        {
            DecompressDXTBlock(x, y, blockCountX);
        }
    }

    return dst;
}

void PixelFormatConversion::DecompressDxt1Block(const uint8_t*& imageReader, int x, int y, int blockCountX, int width, int height, std::vector<uint8_t>& imageData)
{
    uint16_t c0 = *reinterpret_cast<const uint16_t*>(imageReader);
    imageReader += 2;
    uint16_t c1 = *reinterpret_cast<const uint16_t*>(imageReader);
    imageReader += 2;

    uint8_t r0, g0, b0;
    uint8_t r1, g1, b1;
    ConvertRgb565ToRgb888(c0, r0, g0, b0);
    ConvertRgb565ToRgb888(c1, r1, g1, b1);

    uint32_t lookupTable = *reinterpret_cast<const uint32_t*>(imageReader);
    imageReader += 4;

    for (int blockY = 0; blockY < 4; blockY++)
    {
        for (int blockX = 0; blockX < 4; blockX++)
        {
            uint8_t r = 0, g = 0, b = 0, a = 255;
            uint32_t index = (lookupTable >> 2 * (4 * blockY + blockX)) & 0x03;

            if (c0 > c1)
            {
                switch (index)
                {
                case 0:
                    r = r0; g = g0; b = b0;
                    break;
                case 1:
                    r = r1; g = g1; b = b1;
                    break;
                case 2:
                    r = (2 * r0 + r1) / 3;
                    g = (2 * g0 + g1) / 3;
                    b = (2 * b0 + b1) / 3;
                    break;
                case 3:
                    r = (r0 + 2 * r1) / 3;
                    g = (g0 + 2 * g1) / 3;
                    b = (b0 + 2 * b1) / 3;
                    break;
                }
            }
            else
            {
                switch (index)
                {
                case 0:
                    r = r0; g = g0; b = b0;
                    break;
                case 1:
                    r = r1; g = g1; b = b1;
                    break;
                case 2:
                    r = (r0 + r1) / 2;
                    g = (g0 + g1) / 2;
                    b = (b0 + b1) / 2;
                    break;
                case 3:
                    r = 0; g = 0; b = 0; a = 0;
                    break;
                }
            }

            int px = (x << 2) + blockX;
            int py = (y << 2) + blockY;
            if ((px < width) && (py < height))
            {
                int offset = ((py * width) + px) << 2;
                SetColor(imageData, offset, a, r, g, b);
            }
        }
    }
}

void PixelFormatConversion::DecompressDxt3Block(const uint8_t*& imageReader, int x, int y, int blockCountX, int width, int height, std::vector<uint8_t>& imageData)
{
    uint64_t alphaData = *reinterpret_cast<const uint64_t*>(imageReader);
    imageReader += 8;

    uint16_t c0 = *reinterpret_cast<const uint16_t*>(imageReader);
    imageReader += 2;
    uint16_t c1 = *reinterpret_cast<const uint16_t*>(imageReader);
    imageReader += 2;

    uint8_t r0, g0, b0;
    uint8_t r1, g1, b1;
    ConvertRgb565ToRgb888(c0, r0, g0, b0);
    ConvertRgb565ToRgb888(c1, r1, g1, b1);

    uint32_t lookupTable = *reinterpret_cast<const uint32_t*>(imageReader);
    imageReader += 4;

    for (int blockY = 0; blockY < 4; blockY++)
    {
        for (int blockX = 0; blockX < 4; blockX++)
        {
            uint8_t r = 0, g = 0, b = 0;
            uint32_t index = (lookupTable >> 2 * (4 * blockY + blockX)) & 0x03;

            uint8_t a = ((alphaData >> 4 * (4 * blockY + blockX)) & 0x0F) * 17;

            switch (index)
            {
            case 0:
                r = r0; g = g0; b = b0;
                break;
            case 1:
                r = r1; g = g1; b = b1;
                break;
            case 2:
                r = (2 * r0 + r1) / 3;
                g = (2 * g0 + g1) / 3;
                b = (2 * b0 + b1) / 3;
                break;
            case 3:
                r = (r0 + 2 * r1) / 3;
                g = (g0 + 2 * g1) / 3;
                b = (b0 + 2 * b1) / 3;
                break;
            }

            int px = (x << 2) + blockX;
            int py = (y << 2) + blockY;
            if ((px < width) && (py < height))
            {
                int offset = ((py * width) + px) << 2;
                SetColor(imageData, offset, a, r, g, b);
            }
        }
    }
}

void PixelFormatConversion::DecompressDxt5Block(const uint8_t*& imageReader, int x, int y, int blockCountX, int width, int height, std::vector<uint8_t>& imageData)
{
    uint8_t alpha0 = *imageReader++;
    uint8_t alpha1 = *imageReader++;

    uint64_t alphaMask = *reinterpret_cast<const uint64_t*>(imageReader) & 0xFFFFFFFFFFFF; // 6 bytes
    imageReader += 6;

    uint16_t c0 = *reinterpret_cast<const uint16_t*>(imageReader);
    imageReader += 2;
    uint16_t c1 = *reinterpret_cast<const uint16_t*>(imageReader);
    imageReader += 2;

    uint8_t r0, g0, b0;
    uint8_t r1, g1, b1;
    ConvertRgb565ToRgb888(c0, r0, g0, b0);
    ConvertRgb565ToRgb888(c1, r1, g1, b1);

    uint32_t lookupTable = *reinterpret_cast<const uint32_t*>(imageReader);
    imageReader += 4;

    for (int blockY = 0; blockY < 4; blockY++)
    {
        for (int blockX = 0; blockX < 4; blockX++)
        {
            uint8_t r = 0, g = 0, b = 0, a = 255;
            uint32_t index = (lookupTable >> 2 * (4 * blockY + blockX)) & 0x03;

            uint32_t alphaIndex = (alphaMask >> 3 * (4 * blockY + blockX)) & 0x07;
            if (alphaIndex == 0)
            {
                a = alpha0;
            }
            else if (alphaIndex == 1)
            {
                a = alpha1;
            }
            else if (alpha0 > alpha1)
            {
                a = ((8 - alphaIndex) * alpha0 + (alphaIndex - 1) * alpha1) / 7;
            }
            else if (alphaIndex == 6)
            {
                a = 0;
            }
            else if (alphaIndex == 7)
            {
                a = 255;
            }
            else
            {
                a = ((6 - alphaIndex) * alpha0 + (alphaIndex - 1) * alpha1) / 5;
            }

            switch (index)
            {
            case 0:
                r = r0; g = g0; b = b0;
                break;
            case 1:
                r = r1; g = g1; b = b1;
                break;
            case 2:
                r = (2 * r0 + r1) / 3;
                g = (2 * g0 + g1) / 3;
                b = (2 * b0 + b1) / 3;
                break;
            case 3:
                r = (r0 + 2 * r1) / 3;
                g = (g0 + 2 * g1) / 3;
                b = (b0 + 2 * b1) / 3;
                break;
            }

            int px = (x << 2) + blockX;
            int py = (y << 2) + blockY;
            if ((px < width) && (py < height))
            {
                int offset = ((py * width) + px) << 2;
                SetColor(imageData, offset, a, r, g, b);
            }
        }
    }
}

void PixelFormatConversion::ConvertRgb565ToRgb888(uint16_t color, uint8_t& r, uint8_t& g, uint8_t& b)
{
    int temp;

    temp = (color >> 11) * 255 + 16;
    r = (temp / 32 + temp) / 32;
    temp = ((color & 0x07E0) >> 5) * 255 + 32;
    g = (temp / 64 + temp) / 64;
    temp = (color & 0x001F) * 255 + 16;
    b = (temp / 32 + temp) / 32;
}

uint16_t PixelFormatConversion::ConvertRgb888ToRgb565(uint8_t r, uint8_t g, uint8_t b)
{
    // Convert 8-bit RGB components to 5-6-5 bit format
    uint16_t r5 = (r >> 3) & 0x1F;  // 5 bits for red
    uint16_t g6 = (g >> 2) & 0x3F;  // 6 bits for green
    uint16_t b5 = (b >> 3) & 0x1F;  // 5 bits for blue

    // Pack into 16-bit RGB565 format
    return (r5 << 11) | (g6 << 5) | b5;
}

// Conversion methods from ARGB32 to various PAA formats
std::vector<uint8_t> PixelFormatConversion::ARGB32ToARGB16(const std::vector<uint8_t>& src)
{
    int nPixels = src.size() / 4;
    std::vector<uint8_t> dst(nPixels * 2);

    for (int i = 0; i < nPixels; i++)
    {
        uint8_t a, r, g, b;
        GetColor(src, i * 4, a, r, g, b);

        // Convert 8-bit components to 4-bit
        uint8_t a4 = a >> 4;
        uint8_t r4 = r >> 4;
        uint8_t g4 = g >> 4;
        uint8_t b4 = b >> 4;

        // Pack into 16-bit ARGB4444 format
        uint8_t lbyte = (r4 << 4) | b4;
        uint8_t hbyte = (a4 << 4) | g4;

        dst[i * 2] = lbyte;
        dst[i * 2 + 1] = hbyte;
    }

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::ARGB32ToARGB1555(const std::vector<uint8_t>& src)
{
    int nPixels = src.size() / 4;
    std::vector<uint8_t> dst(nPixels * 2);

    for (int i = 0; i < nPixels; i++)
    {
        uint8_t a, r, g, b;
        GetColor(src, i * 4, a, r, g, b);

        // Convert to 1-bit alpha, 5-bit RGB
        uint8_t a1 = a >= 128 ? 1 : 0;
        uint8_t r5 = r >> 3;
        uint8_t g5 = g >> 3;
        uint8_t b5 = b >> 3;

        // Pack into 16-bit ARGB1555 format
        uint16_t pixel = (a1 << 15) | (b5 << 10) | (g5 << 5) | r5;

        dst[i * 2] = pixel & 0xFF;
        dst[i * 2 + 1] = (pixel >> 8) & 0xFF;
    }

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::ARGB32ToAI88(const std::vector<uint8_t>& src)
{
    int nPixels = src.size() / 4;
    std::vector<uint8_t> dst(nPixels * 2);

    for (int i = 0; i < nPixels; i++)
    {
        uint8_t a, r, g, b;
        GetColor(src, i * 4, a, r, g, b);

        // Convert RGB to grayscale using standard luminance formula
        uint8_t grey = static_cast<uint8_t>(0.299f * r + 0.587f * g + 0.114f * b);

        dst[i * 2] = grey;
        dst[i * 2 + 1] = a;
    }

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::ARGB32ToP8(const std::vector<uint8_t>& src, const std::vector<PackedColor>& palette)
{
    int nPixels = src.size() / 4;
    std::vector<uint8_t> dst(nPixels);

    for (int i = 0; i < nPixels; i++)
    {
        uint8_t a, r, g, b;
        GetColor(src, i * 4, a, r, g, b);

        // Find the closest color in the palette
        int bestIndex = 0;
        int bestDistance = INT_MAX;

        for (size_t j = 0; j < palette.size(); j++)
        {
            const PackedColor& color = palette[j];
            int dr = r - color.R8();
            int dg = g - color.G8();
            int db = b - color.B8();
            int da = a - color.A8();

            // Use weighted Euclidean distance for better perceptual matching
            int distance = dr*dr + dg*dg + db*db + da*da;

            if (distance < bestDistance)
            {
                bestDistance = distance;
                bestIndex = static_cast<int>(j);
            }
        }

        dst[i] = static_cast<uint8_t>(bestIndex);
    }

    return dst;
}

// DXT compression is complex and would require a full implementation
// This is a placeholder that would need to be replaced with a proper DXT compressor
std::vector<uint8_t> PixelFormatConversion::ARGB32ToDXT1(const std::vector<uint8_t>& src, int width, int height)
{
    // Calculate the size of the DXT1 compressed data
    int blockCountX = (width + 3) / 4;
    int blockCountY = (height + 3) / 4;
    int dxtDataSize = blockCountX * blockCountY * 8; // 8 bytes per 4x4 block for DXT1

    std::vector<uint8_t> dst(dxtDataSize);

    // This is where you would implement DXT1 compression
    // For now, we'll just create a placeholder with empty blocks

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::ARGB32ToDXT3(const std::vector<uint8_t>& src, int width, int height)
{
    // Calculate the size of the DXT3 compressed data
    int blockCountX = (width + 3) / 4;
    int blockCountY = (height + 3) / 4;
    int dxtDataSize = blockCountX * blockCountY * 16; // 16 bytes per 4x4 block for DXT3

    std::vector<uint8_t> dst(dxtDataSize);

    // This is where you would implement DXT3 compression
    // For now, we'll just create a placeholder with empty blocks

    return dst;
}

std::vector<uint8_t> PixelFormatConversion::ARGB32ToDXT5(const std::vector<uint8_t>& src, int width, int height)
{
    // Calculate the size of the DXT5 compressed data
    int blockCountX = (width + 3) / 4;
    int blockCountY = (height + 3) / 4;
    int dxtDataSize = blockCountX * blockCountY * 16; // 16 bytes per 4x4 block for DXT5

    std::vector<uint8_t> dst(dxtDataSize, 0); // Initialize with zeros
    uint8_t* dstPtr = dst.data();

    // Process each 4x4 block
    for (int y = 0; y < blockCountY; y++)
    {
        for (int x = 0; x < blockCountX; x++)
        {
            // Extract the 4x4 block of pixels
            uint8_t blockPixels[4 * 4 * 4]; // 4x4 block, 4 bytes per pixel (BGRA)
            uint8_t alphaValues[16]; // Alpha values for the block

            // Extract pixels and alpha values from the source
            for (int by = 0; by < 4; by++)
            {
                for (int bx = 0; bx < 4; bx++)
                {
                    int sx = x * 4 + bx;
                    int sy = y * 4 + by;

                    // Handle edge cases (clamp to edge)
                    sx = std::min(sx, width - 1);
                    sy = std::min(sy, height - 1);

                    int srcIdx = (sy * width + sx) * 4;
                    int blockIdx = by * 4 + bx;

                    // Make sure we don't read past the end of the source buffer
                    if (srcIdx + 3 < src.size())
                    {
                        // Copy BGRA values
                        blockPixels[blockIdx * 4] = src[srcIdx]; // B
                        blockPixels[blockIdx * 4 + 1] = src[srcIdx + 1]; // G
                        blockPixels[blockIdx * 4 + 2] = src[srcIdx + 2]; // R
                        blockPixels[blockIdx * 4 + 3] = src[srcIdx + 3]; // A

                        // Store alpha separately
                        alphaValues[blockIdx] = src[srcIdx + 3];
                    }
                    else
                    {
                        // Default values if we're out of bounds
                        blockPixels[blockIdx * 4] = 0;
                        blockPixels[blockIdx * 4 + 1] = 0;
                        blockPixels[blockIdx * 4 + 2] = 0;
                        blockPixels[blockIdx * 4 + 3] = 255;
                        alphaValues[blockIdx] = 255;
                    }
                }
            }

            // Compress alpha channel (first 8 bytes of DXT5 block)
            uint8_t alpha0 = 0, alpha1 = 255;

            // Find min and max alpha values
            for (int i = 0; i < 16; i++)
            {
                if (alphaValues[i] > alpha0) alpha0 = alphaValues[i];
                if (alphaValues[i] < alpha1) alpha1 = alphaValues[i];
            }

            // If all alpha values are the same, use a simpler encoding
            if (alpha0 == alpha1)
            {
                *dstPtr++ = alpha0;
                *dstPtr++ = alpha1;

                // Write 6 bytes of alpha indices (all zeros)
                for (int i = 0; i < 6; i++)
                {
                    *dstPtr++ = 0;
                }
            }
            else
            {
                // Write alpha endpoints
                *dstPtr++ = alpha0;
                *dstPtr++ = alpha1;

                // Compute alpha indices and write them
                uint64_t alphaMask = 0;

                for (int i = 0; i < 16; i++)
                {
                    uint8_t alphaValue = alphaValues[i];
                    uint8_t alphaIndex;

                    if (alphaValue == alpha0)
                        alphaIndex = 0;
                    else if (alphaValue == alpha1)
                        alphaIndex = 1;
                    else if (alpha0 > alpha1)
                    {
                        // 8-alpha interpolation
                        int diff = alpha0 - alpha1;
                        if (diff == 0) diff = 1; // Avoid division by zero
                        alphaIndex = 1 + (((alphaValue - alpha1) * 7) / diff);
                        alphaIndex = std::min(alphaIndex, (uint8_t)7);
                    }
                    else
                    {
                        // 6-alpha interpolation
                        if (alphaValue == 0)
                            alphaIndex = 6;
                        else if (alphaValue == 255)
                            alphaIndex = 7;
                        else
                        {
                            int diff = alpha1 - alpha0;
                            if (diff == 0) diff = 1; // Avoid division by zero
                            alphaIndex = 1 + (((alphaValue - alpha0) * 5) / diff);
                            alphaIndex = std::min(alphaIndex, (uint8_t)7);
                        }
                    }

                    // Pack 3-bit index into the 64-bit mask (3 bits per index)
                    alphaMask |= ((uint64_t)alphaIndex << (3 * i));
                }

                // Write 6 bytes of alpha indices
                for (int i = 0; i < 6; i++)
                {
                    *dstPtr++ = (alphaMask >> (8 * i)) & 0xFF;
                }
            }

            // Now compress the color data (remaining 8 bytes of DXT5 block)
            // Find the two most distinct colors in the block
            uint8_t r0 = 0, g0 = 0, b0 = 0;
            uint8_t r1 = 0, g1 = 0, b1 = 0;

            // Simple approach: use the colors with the most extreme luminance
            float minLum = 1000.0f, maxLum = -1000.0f;
            int minIdx = 0, maxIdx = 0;

            for (int i = 0; i < 16; i++)
            {
                uint8_t r = blockPixels[i * 4 + 2];
                uint8_t g = blockPixels[i * 4 + 1];
                uint8_t b = blockPixels[i * 4];

                // Calculate luminance
                float lum = 0.299f * r + 0.587f * g + 0.114f * b;

                if (lum < minLum)
                {
                    minLum = lum;
                    minIdx = i;
                }

                if (lum > maxLum)
                {
                    maxLum = lum;
                    maxIdx = i;
                }
            }

            // Get the two extreme colors
            r0 = blockPixels[maxIdx * 4 + 2];
            g0 = blockPixels[maxIdx * 4 + 1];
            b0 = blockPixels[maxIdx * 4];

            r1 = blockPixels[minIdx * 4 + 2];
            g1 = blockPixels[minIdx * 4 + 1];
            b1 = blockPixels[minIdx * 4];

            // If the colors are identical, make a small difference
            if (r0 == r1 && g0 == g1 && b0 == b1)
            {
                if (r0 < 255) r0++;
                else if (g0 < 255) g0++;
                else if (b0 < 255) b0++;
                else if (r1 > 0) r1--;
                else if (g1 > 0) g1--;
                else if (b1 > 0) b1--;
            }

            // Convert to RGB565 format
            uint16_t color0 = ConvertRgb888ToRgb565(r0, g0, b0);
            uint16_t color1 = ConvertRgb888ToRgb565(r1, g1, b1);

            // Ensure color0 > color1 for DXT5 (we want 4 interpolated colors)
            if (color0 < color1)
            {
                std::swap(color0, color1);
                std::swap(r0, r1);
                std::swap(g0, g1);
                std::swap(b0, b1);
            }

            // Write the color endpoints
            *dstPtr++ = color0 & 0xFF;
            *dstPtr++ = (color0 >> 8) & 0xFF;
            *dstPtr++ = color1 & 0xFF;
            *dstPtr++ = (color1 >> 8) & 0xFF;

            // Compute color indices
            uint32_t colorIndices = 0;

            for (int i = 0; i < 16; i++)
            {
                uint8_t r = blockPixels[i * 4 + 2];
                uint8_t g = blockPixels[i * 4 + 1];
                uint8_t b = blockPixels[i * 4];

                // Find the closest color
                int d0 = (r - r0) * (r - r0) + (g - g0) * (g - g0) + (b - b0) * (b - b0);
                int d1 = (r - r1) * (r - r1) + (g - g1) * (g - g1) + (b - b1) * (b - b1);

                // Calculate interpolated colors
                uint8_t r2 = (2 * r0 + r1) / 3;
                uint8_t g2 = (2 * g0 + g1) / 3;
                uint8_t b2 = (2 * b0 + b1) / 3;

                uint8_t r3 = (r0 + 2 * r1) / 3;
                uint8_t g3 = (g0 + 2 * g1) / 3;
                uint8_t b3 = (b0 + 2 * b1) / 3;

                int d2 = (r - r2) * (r - r2) + (g - g2) * (g - g2) + (b - b2) * (b - b2);
                int d3 = (r - r3) * (r - r3) + (g - g3) * (g - g3) + (b - b3) * (b - b3);

                // Find the closest color
                int minDist = d0;
                uint8_t colorIndex = 0;

                if (d1 < minDist) { minDist = d1; colorIndex = 1; }
                if (d2 < minDist) { minDist = d2; colorIndex = 2; }
                if (d3 < minDist) { minDist = d3; colorIndex = 3; }

                // Pack 2-bit index into the 32-bit mask (2 bits per index)
                colorIndices |= (colorIndex << (2 * i));
            }

            // Write 4 bytes of color indices
            *dstPtr++ = colorIndices & 0xFF;
            *dstPtr++ = (colorIndices >> 8) & 0xFF;
            *dstPtr++ = (colorIndices >> 16) & 0xFF;
            *dstPtr++ = (colorIndices >> 24) & 0xFF;
        }
    }

    return dst;
}