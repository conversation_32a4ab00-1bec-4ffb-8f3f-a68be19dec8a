// RvmatAsset.cpp
#include "RvmatAsset.h"
#include "Materials/Material.h"
#include "LevelEditor.h"
#include "Materials/MaterialInstanceConstant.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialExpressionParameter.h"

URvmatAsset::URvmatAsset(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
}

void URvmatAsset::PostLoad()
{
    Super::PostLoad();

#if WITH_EDITOR
    // Ensure that the asset is set up properly
    UpdateRvmatParameters();
#endif
}

int32 URvmatAsset::GetSortPriority(const FMaterialParameterInfo& ParameterInfo) const
{
    int32 Priority = 0;  // Default priority

    if (Parent)
    {
        const UMaterialInstance* MaterialInstance = Cast<UMaterialInstance>(Parent);
        if (MaterialInstance)
        {
            for (const FScalarParameterValue& ScalarParam : MaterialInstance->ScalarParameterValues)
            {
                if (ScalarParam.ParameterInfo == ParameterInfo)
                {
                    Priority = 1;
                    break;
                }
            }

            for (const FVectorParameterValue& VectorParam : MaterialInstance->VectorParameterValues)
            {
                if (VectorParam.ParameterInfo == ParameterInfo)
                {
                    Priority = 2;
                    break;
                }
            }

            for (const FTextureParameterValue& TextureParam : MaterialInstance->TextureParameterValues)
            {
                if (TextureParam.ParameterInfo == ParameterInfo)
                {
                    Priority = 3;
                    break;
                }
            }
        }
    }

    // Log the priority for debugging purposes
    UE_LOG(LogTemp, Warning, TEXT("Parameter: %s, Priority: %d"), *ParameterInfo.Name.ToString(), Priority);

    return Priority;
}



void URvmatAsset::PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent)
{
    Super::PostEditChangeProperty(PropertyChangedEvent);

#if WITH_EDITOR
    if (PropertyChangedEvent.Property)
    {
        FName PropertyName = PropertyChangedEvent.Property->GetFName();

        if (PropertyName == GET_MEMBER_NAME_CHECKED(URvmatAsset, Parent))
        {
            // Update parameters
            UpdateRvmatParameters();

            // Broadcast the change to refresh the details panel
            OnRvmatAssetChanged.Broadcast();
        }
    }
#endif
}

void URvmatAsset::SetParentEditorOnly(UMaterialInterface* NewParent)
{
    if (NewParent != Parent)
    {
        Parent = NewParent;
        OnRvmatAssetChanged.Broadcast();
        MarkPackageDirty();
    }
}

void URvmatAsset::UpdateRvmatParameters()
{
    if (Parent)
    {
        UE_LOG(LogTemp, Warning, TEXT("RvmatAsset: Updating parameters for %s"), *GetName());

        // Get scalar parameters
        TArray<FMaterialParameterInfo> ScalarParameterInfos;
        TArray<FGuid> ScalarParameterIds;
        GetAllScalarParameterInfo(ScalarParameterInfos, ScalarParameterIds);

        for (const FMaterialParameterInfo& Info : ScalarParameterInfos)
        {
            float Value;
            if (GetScalarParameterValue(Info, Value))
            {
                UE_LOG(LogTemp, Warning, TEXT("Scalar parameter: %s = %f"), *Info.Name.ToString(), Value);
            }
        }

        // Get vector parameters
        TArray<FMaterialParameterInfo> VectorParameterInfos;
        TArray<FGuid> VectorParameterIds;
        GetAllVectorParameterInfo(VectorParameterInfos, VectorParameterIds);

        for (const FMaterialParameterInfo& Info : VectorParameterInfos)
        {
            FLinearColor Value;
            if (GetVectorParameterValue(Info, Value))
            {
                UE_LOG(LogTemp, Warning, TEXT("Vector parameter: %s = (%f, %f, %f, %f)"),
                    *Info.Name.ToString(), Value.R, Value.G, Value.B, Value.A);
            }
        }

        // Get texture parameters
        TArray<FMaterialParameterInfo> TextureParameterInfos;
        TArray<FGuid> TextureParameterIds;
        GetAllTextureParameterInfo(TextureParameterInfos, TextureParameterIds);

        for (const FMaterialParameterInfo& Info : TextureParameterInfos)
        {
            UTexture* Value;
            if (GetTextureParameterValue(Info, Value))
            {
                UE_LOG(LogTemp, Warning, TEXT("Texture parameter: %s = %s"),
                    *Info.Name.ToString(), Value ? *Value->GetName() : TEXT("None"));
            }
        }

        // Mark the material as changed
        // Force a refresh of the editor
        FLevelEditorModule& LevelEditor = FModuleManager::LoadModuleChecked<FLevelEditorModule>("LevelEditor");
        LevelEditor.BroadcastComponentsEdited();
        LevelEditor.BroadcastRedrawViewports(false);

        // Mark the package as dirty
        MarkPackageDirty();
    }
}