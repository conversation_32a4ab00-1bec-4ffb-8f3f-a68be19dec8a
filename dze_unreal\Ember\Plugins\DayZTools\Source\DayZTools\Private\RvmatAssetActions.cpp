// RvmatAssetActions.cpp
#include "RvmatAssetActions.h"
#include "RvmatAsset.h"
#include "RvmatAssetEditor.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"

FRvmatAssetActions::FRvmatAssetActions(EAssetTypeCategories::Type category)
{
    _assetCategory = category;
}

FText FRvmatAssetActions::GetName() const
{
    return FText::FromString(TEXT("RVMAT"));
}

FColor FRvmatAssetActions::GetTypeColor() const
{
    return FColor::Red;
}

UClass* FRvmatAssetActions::GetSupportedClass() const
{
    return URvmatAsset::StaticClass();
}

uint32 FRvmatAssetActions::GetCategories()
{
    return _assetCategory;
}

void FRvmatAssetActions::OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor)
{
    const EToolkitMode::Type Mode = EditWithinLevelEditor.IsValid() ? EToolkitMode::WorldCentric : EToolkitMode::Standalone;

    for (UObject* Object : InObjects)
    {
        URvmatAsset* RvmatAsset = Cast<URvmatAsset>(Object);
        if (RvmatAsset)
        {
            TSharedRef<FRvmatAssetEditor> NewEditor = MakeShareable(new FRvmatAssetEditor());
            NewEditor->InitRvmatAssetEditor(Mode, EditWithinLevelEditor, RvmatAsset);
        }
    }
}
