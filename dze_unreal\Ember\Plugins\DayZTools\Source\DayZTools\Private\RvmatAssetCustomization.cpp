// RvmatAssetCustomization.cpp
#include "RvmatAssetCustomization.h"
#include "RvmatAsset.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "DetailWidgetRow.h"
#include "Widgets/Input/SNumericEntryBox.h"
#include "Widgets/Text/STextBlock.h"
#include "Widgets/Input/SButton.h"
#include "PropertyCustomizationHelpers.h"  // For SObjectPropertyEntryBox
#include "Widgets/Colors/SColorBlock.h"
#include "Widgets/Colors/SColorPicker.h"
#include "Widgets/Input/SCheckBox.h"
#include "Widgets/Input/SSpinBox.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/SBoxPanel.h"
#include "Widgets/SNullWidget.h"
#include "IDetailGroup.h"

#include "Widgets/SWidget.h"
#include "PropertyEditorModule.h"
#include "AssetRegistry/AssetData.h"  // If the header file path has changed

#include "TimerManager.h" // Include TimerManager for handling the debounce
#include "Engine/World.h" // Include for getting the world context

#include "Containers/Ticker.h"


#define LOCTEXT_NAMESPACE "RvmatAssetCustomization"

TSharedRef<IDetailCustomization> FRvmatAssetCustomization::MakeInstance()
{
    return MakeShareable(new FRvmatAssetCustomization());
}

FRvmatAssetCustomization::FRvmatAssetCustomization()
    : RvmatAsset(nullptr)
    , DetailBuilderPtr(nullptr)
{

}


void FRvmatAssetCustomization::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    UE_LOG(LogTemp, Log, TEXT("FRvmatAssetCustomization::CustomizeDetails called"));

    

    DetailBuilderPtr = &DetailBuilder;

    // Create the Material Parameters category first, with highest priority
    IDetailCategoryBuilder& ParametersCategory = DetailBuilder.EditCategory("Material Parameters", FText::GetEmpty(), ECategoryPriority::Variable);

    // Reset the RvmatAsset pointer
    RvmatAsset.Reset();

    TArray<TWeakObjectPtr<UObject>> ObjectsBeingCustomized;
    DetailBuilder.GetObjectsBeingCustomized(ObjectsBeingCustomized);

    if (ObjectsBeingCustomized.Num() != 1)
    {
        UE_LOG(LogTemp, Error, TEXT("FRvmatAssetCustomization: Unexpected number of objects being customized: %d"), ObjectsBeingCustomized.Num());
        return;
    }

    RvmatAsset = Cast<URvmatAsset>(ObjectsBeingCustomized[0].Get());
    if (!RvmatAsset.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("FRvmatAssetCustomization: Invalid RvmatAsset"));
        return;
    }


    UE_LOG(LogTemp, Log, TEXT("FRvmatAssetCustomization: Customizing details for %s"), *RvmatAsset->GetName());

    // Initialize PixelShaderOptions here if they haven't been already
    if (PixelShaderOptions.Num() == 0)
    {
        PixelShaderOptions.Add(MakeShareable(new FPixelShaderOption("Super", "MM_Super")));
        PixelShaderOptions.Add(MakeShareable(new FPixelShaderOption("Multi", "MM_Multi")));
    }

    PixelShaderOptionsList.Empty();
    TSharedPtr<FString> InitialSelection = nullptr;

    // Get the current parent material name
    FString CurrentParentName = GetParentMaterialName();
    UE_LOG(LogTemp, Log, TEXT("Current Parent Material Name: %s"), *CurrentParentName);

    for (const auto& Option : PixelShaderOptions)
    {
        TSharedPtr<FString> OptionName = MakeShareable(new FString(Option->DisplayName));
        PixelShaderOptionsList.Add(OptionName);

        // Set the initial selection based on the parent material name
        if (CurrentParentName.Equals(Option->MaterialName, ESearchCase::IgnoreCase))
        {
            InitialSelection = OptionName;
            UE_LOG(LogTemp, Log, TEXT("Setting initial selection: %s"), **OptionName);
        }
    }

    ParametersCategory.AddCustomRow(FText::FromString("Pixel Shader"))
        .NameContent()
        [
            SNew(STextBlock)
                .Text(FText::FromString("Pixel Shader"))
                .Font(IDetailLayoutBuilder::GetDetailFont())
        ]
        .ValueContent()
        [
            SAssignNew(PixelShaderComboBox, SComboBox<TSharedPtr<FString>>)
                .ContentPadding(FMargin(4.0f, 2.0f))
                .OptionsSource(&PixelShaderOptionsList)
                .OnGenerateWidget_Lambda([](TSharedPtr<FString> Item)
                    {
                        return SNew(STextBlock)
                            .Text(FText::FromString(*Item));
                    })
                .OnSelectionChanged(this, &FRvmatAssetCustomization::OnPixelShaderChanged)
                .InitiallySelectedItem(InitialSelection.IsValid() ? InitialSelection : PixelShaderOptionsList[0])
                [
                    SNew(STextBlock)
                        .Text(this, &FRvmatAssetCustomization::GetCurrentPixelShaderText)
                ]
        ];

    // Call UpdateComboBoxSelection after setting up the combo box
    UpdateComboBoxSelection();


    // Create groups
    IDetailGroup& SurfaceParametersGroup = ParametersCategory.AddGroup("SurfaceParameters", LOCTEXT("SurfaceParametersGroup", "Surface Parameters"), false, true);
    IDetailGroup& StagesGroup = ParametersCategory.AddGroup("Stages", LOCTEXT("StagesGroup", "Stages"), false, true);
    ParametersCategory.InitiallyCollapsed(false);

    FString ParentMaterialName = GetParentMaterialName();

    // Example mapping for parent material names to display names
    TMap<FString, TMap<FString, TMap<FString, FString>>> MaterialDisplayNameMap = {
        { "super", {
            { "surface_parameters", {
                { "ambient", "Ambient" },
                { "diffuse", "Diffuse" },
                { "forcedDiffuse", "Forced Diffuse" },
                { "emmisive", "Emissive" },
                { "specular", "Specular" },
                { "specularPower", "Specular Power" },
            }},
            { "stages", {
                { "stage0", "Diffuse Map (Stage 0)" },
                { "stage1", "Normal Map (Stage 1)" },
                { "stage2", "Detail Map (Stage 2)" },
                { "stage3", "Macro Map (Stage 3)" },
                { "stage4", "AmbientShadow Map (Stage 4)" },
                { "stage5", "Specular Map (Stage 5)" },
            }},
        }},
        { "multi", {
            { "surface_parameters", {
                { "ambient", "Ambient" },
                { "diffuse", "Diffuse" },
                { "forcedDiffuse", "Forced Diffuse" },
                { "emmisive", "Emissive" },
                { "specular", "Specular" },
                { "specularPower", "Specular Power" },
            }},
            { "stages", {

                { "stage4", "Layer Mask" },
                { "stage9", "Macro Map" },
                { "stage10", "AmbientShadow Map" },

                { "stage0", "Diffuse Map (Layer 1)" },
                { "stage11", "Normal Map (Layer 1)" },
                { "stage5", "Specular Map (Layer 1)" },

                { "stage1", "Diffuse Map (Layer 2)" },
                { "stage12", "Normal Map (Layer 2)" },
                { "stage6", "Specular Map (Layer 2)" },

                { "stage2", "Diffuse Map (Layer 3)" },
                { "stage13", "Normal Map (Layer 3)" },
                { "stage7", "Specular Map (Layer 3)" },

                { "stage3", "Diffuse Map (Layer 4)" },
                { "stage14", "Normal Map (Layer 4)" },
                { "stage8", "Specular Map (Layer 4)" }, 
                
            }},
        }},
    };

    if (MaterialDisplayNameMap.Contains(ParentMaterialName))
    {
        for (const auto& CategoryPair : MaterialDisplayNameMap[ParentMaterialName])
        {
            IDetailGroup& TargetGroup = CategoryPair.Key == "stages" ? StagesGroup : SurfaceParametersGroup;

            for (const auto& ParameterPair : CategoryPair.Value)
            {
                FString ParameterName = ParameterPair.Key;
                FString DisplayName = ParameterPair.Value;

                // Add scalar parameters
                TArray<FMaterialParameterInfo> ScalarParameterInfos;
                TArray<FGuid> ScalarParameterIds;
                RvmatAsset->GetAllScalarParameterInfo(ScalarParameterInfos, ScalarParameterIds);

                for (const FMaterialParameterInfo& Info : ScalarParameterInfos)
                {
                    if (Info.Name.ToString().Equals(ParameterName, ESearchCase::IgnoreCase))
                    {
                        TargetGroup.AddWidgetRow()
                            .NameContent()
                            [
                                SNew(STextBlock)
                                    .Text(FText::FromString(DisplayName))
                                    .Font(IDetailLayoutBuilder::GetDetailFont())
                            ]
                            .ValueContent()
                            [
                                SNew(SNumericEntryBox<float>)
                                    .Value_Lambda([this, Info]() {
                                    float Value = 0.0f;
                                    if (RvmatAsset.IsValid())
                                    {
                                        RvmatAsset->GetScalarParameterValue(Info, Value);
                                    }
                                    return Value;
                                        })
                                    .OnValueCommitted_Lambda([this, Info](float NewValue, ETextCommit::Type CommitType) {
                                    if (RvmatAsset.IsValid())
                                    {
                                        RvmatAsset->SetScalarParameterValueEditorOnly(Info.Name, NewValue);
                                    }
                                        })
                            ];
                    }
                }

                // Add vector parameters
                TArray<FMaterialParameterInfo> VectorParameterInfos;
                TArray<FGuid> VectorParameterIds;
                RvmatAsset->GetAllVectorParameterInfo(VectorParameterInfos, VectorParameterIds);

                for (const FMaterialParameterInfo& Info : VectorParameterInfos)
                {
                    if (Info.Name.ToString().Equals(ParameterName, ESearchCase::IgnoreCase))
                    {
                        TargetGroup.AddWidgetRow()
                            .NameContent()
                            [
                                SNew(STextBlock)
                                    .Text(FText::FromString(DisplayName))
                                    .Font(IDetailLayoutBuilder::GetDetailFont())
                            ]
                            .ValueContent()
                            [
                                SNew(SColorBlock)
                                    .Color_Lambda([this, Info]() {
                                    FLinearColor Value = FLinearColor::White;
                                    if (RvmatAsset.IsValid())
                                    {
                                        RvmatAsset->GetVectorParameterValue(Info, Value);
                                    }
                                    return Value;
                                        })
                                    .OnMouseButtonDown_Lambda([this, Info](const FGeometry&, const FPointerEvent&) {
                                    if (RvmatAsset.IsValid())
                                    {
                                        FLinearColor Value;
                                        RvmatAsset->GetVectorParameterValue(Info, Value);

                                        FColorPickerArgs PickerArgs;
                                        PickerArgs.InitialColor = Value;
                                        PickerArgs.bOnlyRefreshOnOk = false;
                                        PickerArgs.bUseAlpha = true;
                                        PickerArgs.OnColorCommitted = FOnLinearColorValueChanged::CreateLambda([this, Info](FLinearColor NewColor) {
                                            if (RvmatAsset.IsValid())
                                            {
                                                RvmatAsset->SetVectorParameterValueEditorOnly(Info.Name, NewColor);
                                            }
                                            });

                                        PickerArgs.ParentWidget = FSlateApplication::Get().GetActiveTopLevelWindow();
                                        OpenColorPicker(PickerArgs);
                                    }
                                    return FReply::Handled();
                                        })
                            ];
                    }
                }

                // Add texture parameters
                TArray<FMaterialParameterInfo> TextureParameterInfos;
                TArray<FGuid> TextureParameterIds;
                RvmatAsset->GetAllTextureParameterInfo(TextureParameterInfos, TextureParameterIds);

                for (const FMaterialParameterInfo& Info : TextureParameterInfos)
                {
                    if (Info.Name.ToString().Equals(ParameterName, ESearchCase::IgnoreCase))
                    {
                        TargetGroup.AddWidgetRow()
                            .NameContent()
                            [
                                SNew(STextBlock)
                                    .Text(FText::FromString(DisplayName))
                                    .Font(IDetailLayoutBuilder::GetDetailFont())
                            ]
                            .ValueContent()
                            [
                                SNew(SObjectPropertyEntryBox)
                                    .AllowedClass(UTexture::StaticClass())
                                    .ObjectPath_Lambda([this, Info]() -> FString {
                                    UTexture* Value = nullptr;
                                    if (RvmatAsset.IsValid())
                                    {
                                        RvmatAsset->GetTextureParameterValue(Info, Value);
                                    }
                                    return Value ? Value->GetPathName() : FString();
                                        })
                                    .OnObjectChanged_Lambda([this, Info](const FAssetData& AssetData) {
                                    if (RvmatAsset.IsValid())
                                    {
                                        RvmatAsset->SetTextureParameterValueEditorOnly(Info.Name, Cast<UTexture>(AssetData.GetAsset()));
                                    }
                                        })
                                    .ThumbnailPool(DetailBuilder.GetThumbnailPool())
                                    .DisplayThumbnail(true)
                                    .ThumbnailSizeOverride(FIntPoint(64, 64))
                            ];
                    }
                }
            }
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("FRvmatAssetCustomization: Unknown parent material: %s"), *ParentMaterialName);
    }

    // Hide other categories
    DetailBuilder.HideCategory("AssetUserData");
    DetailBuilder.HideCategory("Thumbnail");
    DetailBuilder.HideCategory("ImportSettings");
    DetailBuilder.HideCategory("Previewing");
    DetailBuilder.HideCategory("Lightmass");
    DetailBuilder.HideCategory("PostProcessMaterial");
    DetailBuilder.HideCategory("PhysicalMaterialMask");
    DetailBuilder.HideCategory("Material");
    DetailBuilder.HideCategory("MaterialInstance");

    if (RvmatAsset.IsValid())
    {
        RvmatAsset->OnRvmatAssetChanged.AddSP(this, &FRvmatAssetCustomization::RefreshDetailsView);
    }
}

FText FRvmatAssetCustomization::GetCurrentPixelShaderText() const
{
    if (PixelShaderComboBox.IsValid())
    {
        TSharedPtr<FString> SelectedItem = PixelShaderComboBox->GetSelectedItem();
        if (SelectedItem.IsValid())
        {
            return FText::FromString(*SelectedItem);
        }
    }
    return FText::FromString("Select Shader");
}

void FRvmatAssetCustomization::UpdateComboBoxSelection()
{
    UE_LOG(LogTemp, Log, TEXT("UpdateComboBoxSelection - Start"));

    if (!RvmatAsset.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateComboBoxSelection - RvmatAsset is invalid"));
        return;
    }

    if (!PixelShaderComboBox.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("UpdateComboBoxSelection - PixelShaderComboBox is invalid"));
        return;
    }

    FString CurrentParentName = GetParentMaterialName();
    UE_LOG(LogTemp, Log, TEXT("UpdateComboBoxSelection - Current Parent Name: %s"), *CurrentParentName);

    TSharedPtr<FString> SelectedOption;
    for (const auto& Option : PixelShaderOptionsList)
    {
        if (Option.IsValid() && Option->Contains(CurrentParentName, ESearchCase::IgnoreCase))
        {
            SelectedOption = Option;
            break;
        }
    }

    if (SelectedOption.IsValid())
    {
        UE_LOG(LogTemp, Log, TEXT("UpdateComboBoxSelection - Setting selection to: %s"), **SelectedOption);
        PixelShaderComboBox->SetSelectedItem(SelectedOption);
        PixelShaderComboBox->RefreshOptions();
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("UpdateComboBoxSelection - No matching option found for: %s"), *CurrentParentName);
    }

    UE_LOG(LogTemp, Log, TEXT("UpdateComboBoxSelection - End"));
}

void FRvmatAssetCustomization::OnPixelShaderChanged(TSharedPtr<FString> NewValue, ESelectInfo::Type SelectType)
{
    UE_LOG(LogTemp, Log, TEXT("OnPixelShaderChanged - Start"));

    if (!NewValue.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("OnPixelShaderChanged - NewValue is invalid"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("OnPixelShaderChanged - New Value: %s"), **NewValue);

    if (!RvmatAsset.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("OnPixelShaderChanged - RvmatAsset is invalid"));
        return;
    }

    UE_LOG(LogTemp, Warning, TEXT("Selected Pixel Shader: %s"), **NewValue);

    if (PixelShaderComboBox.IsValid())
    {
        UE_LOG(LogTemp, Log, TEXT("OnPixelShaderChanged - ComboBox Selection After Change: %s"),
            PixelShaderComboBox->GetSelectedItem().IsValid() ? **(PixelShaderComboBox->GetSelectedItem()) : TEXT("Invalid"));
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("OnPixelShaderChanged - PixelShaderComboBox is invalid"));
    }

    TSharedPtr<FPixelShaderOption> SelectedOption = nullptr;
    for (const auto& Option : PixelShaderOptions)
    {
        if (Option->DisplayName == *NewValue)
        {
            SelectedOption = Option;
            break;
        }
    }

    if (!SelectedOption.IsValid())
    {
        UE_LOG(LogTemp, Error, TEXT("OnPixelShaderChanged - No valid PixelShaderOption found for %s"), **NewValue);
        return;
    }

    FString MaterialPath = FString::Printf(TEXT("/Game/Shaders/%s.%s"), *SelectedOption->MaterialName, *SelectedOption->MaterialName);
    UMaterial* NewParentMaterial = LoadObject<UMaterial>(nullptr, *MaterialPath);
    if (!NewParentMaterial)
    {
        UE_LOG(LogTemp, Error, TEXT("OnPixelShaderChanged - Failed to load material: %s"), *SelectedOption->MaterialName);
        return;
    }

    RvmatAsset->SetParentEditorOnly(NewParentMaterial);
    RvmatAsset->Parent = NewParentMaterial;
    RvmatAsset->Modify();

    UE_LOG(LogTemp, Warning, TEXT("Changed Parent material to: %s"), *SelectedOption->MaterialName);

    UpdateComboBoxSelection();

    if (DetailBuilderPtr)
    {
        DetailBuilderPtr->ForceRefreshDetails();
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("OnPixelShaderChanged - DetailBuilderPtr is invalid"));
    }

    UE_LOG(LogTemp, Log, TEXT("OnPixelShaderChanged - End"));
}


void FRvmatAssetCustomization::RefreshDetailsView()
{
    if (DetailBuilderPtr)
    {
        DetailBuilderPtr->ForceRefreshDetails();
    }

    if (PixelShaderComboBox.IsValid())
    {
        PixelShaderComboBox->RefreshOptions();
        // Ensure the correct item is selected
        PixelShaderComboBox->SetSelectedItem(PixelShaderComboBox->GetSelectedItem());
    }
}


FString FRvmatAssetCustomization::GetParentMaterialName() const
{
    if (RvmatAsset.IsValid() && RvmatAsset->Parent)
    {
        FString ParentName = RvmatAsset->Parent->GetName();
        if (ParentName.StartsWith("MM_"))
        {
            ParentName.RemoveFromStart("MM_");
        }
        return ParentName.ToLower();
    }
    UE_LOG(LogTemp, Warning, TEXT("GetParentMaterialName - RvmatAsset or Parent is invalid"));
    return TEXT("");
}


#undef LOCTEXT_NAMESPACE
