// RvmatAssetDetails.cpp
#include "RvmatAssetDetails.h"
#include "DetailLayoutBuilder.h"
#include "DetailCategoryBuilder.h"
#include "DetailWidgetRow.h"
#include "Widgets/Input/SButton.h"
#include "RvmatAsset.h"

TSharedRef<IDetailCustomization> FRvmatAssetDetails::MakeInstance()
{
    return MakeShareable(new FRvmatAssetDetails);
}

void FRvmatAssetDetails::CustomizeDetails(IDetailLayoutBuilder& DetailBuilder)
{
    IDetailCategoryBuilder& RvmatCategory = DetailBuilder.EditCategory("Rvmat");

    TArray<TWeakObjectPtr<UObject>> ObjectsBeingCustomized;
    DetailBuilder.GetObjectsBeingCustomized(ObjectsBeingCustomized);

    for (TWeakObjectPtr<UObject> Object : ObjectsBeingCustomized)
    {
        URvmatAsset* RvmatAsset = Cast<URvmatAsset>(Object.Get());
        if (RvmatAsset)
        {
            // Add custom rows or modify existing ones here
            RvmatCategory.AddCustomRow(FText::FromString("Rvmat Actions"))
                .NameContent()
                [
                    SNew(STextBlock)
                        .Text(FText::FromString("Rvmat Special Action"))
                ]
                .ValueContent()
                [
                    SNew(SButton)
                        .Text(FText::FromString("Execute"))
                        .OnClicked(FOnClicked::CreateLambda([RvmatAsset]()
                            {
                                // Perform some action specific to Rvmat assets
                                UE_LOG(LogTemp, Warning, TEXT("Rvmat special action executed!"));
                                return FReply::Handled();
                            }))
                ];
        }
    }
}