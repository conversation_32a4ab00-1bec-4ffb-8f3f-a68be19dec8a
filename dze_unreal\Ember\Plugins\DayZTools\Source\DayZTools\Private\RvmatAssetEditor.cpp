#include "RvmatAssetEditor.h"
#include "RvmatAsset.h"
#include "RvmatEditorViewport.h"
#include "Widgets/Docking/SDockTab.h"
#include "IDetailsView.h"
#include "PropertyEditorModule.h"
#include "Widgets/Input/SMultiLineEditableTextBox.h"
#include "Modules/ModuleManager.h"

#define LOCTEXT_NAMESPACE "FRvmatAssetEditor"

// Define the tab identifiers
const FName FRvmatAssetEditor::PreviewTabID(TEXT("Preview"));
const FName FRvmatAssetEditor::DetailsTabID(TEXT("Details"));
const FName FRvmatAssetEditor::OutputTabID(TEXT("RvmatOutput"));

void FRvmatAssetEditor::InitRvmatAssetEditor(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, URvmatAsset* InRvmatAsset)
{
    RvmatAsset = InRvmatAsset;

    // Initialize the editor layout
    TSharedRef<FTabManager::FLayout> Layout = CreateEditorLayout();

    // Initialize the asset editor toolkit
    InitAssetEditor(Mode, InitToolkitHost, FName("RvmatAssetEditor"), Layout, true, true, RvmatAsset);
}

TSharedRef<FTabManager::FLayout> FRvmatAssetEditor::CreateEditorLayout()
{
    return FTabManager::NewLayout("Standalone_RvmatAssetEditor_Layout")
        ->AddArea
        (
            FTabManager::NewPrimaryArea()
            ->SetOrientation(Orient_Horizontal)
            ->Split
            (
                FTabManager::NewSplitter()
                ->SetOrientation(Orient_Vertical)
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.7f)
                    ->AddTab(PreviewTabID, ETabState::OpenedTab)  // Preview tab
                )
                ->Split
                (
                    FTabManager::NewStack()
                    ->SetSizeCoefficient(0.25f)  // Small height for output log
                    ->AddTab(OutputTabID, ETabState::OpenedTab)  // Output tab
                )
            )
            ->Split
            (
                FTabManager::NewStack()
                ->SetSizeCoefficient(0.3f)
                ->AddTab(DetailsTabID, ETabState::OpenedTab)  // Details tab
            )
        );

}



void FRvmatAssetEditor::RegisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::RegisterTabSpawners(InTabManager);

    InTabManager->RegisterTabSpawner(PreviewTabID, FOnSpawnTab::CreateSP(this, &FRvmatAssetEditor::SpawnPreviewTab))
        .SetDisplayName(LOCTEXT("PreviewTab", "Preview"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef());

    InTabManager->RegisterTabSpawner(DetailsTabID, FOnSpawnTab::CreateSP(this, &FRvmatAssetEditor::SpawnDetailsTab))
        .SetDisplayName(LOCTEXT("DetailsTab", "Details"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef());

    InTabManager->RegisterTabSpawner(OutputTabID, FOnSpawnTab::CreateSP(this, &FRvmatAssetEditor::SpawnOutputTab))
        .SetDisplayName(LOCTEXT("OutputTab", "RVMAT Output"))
        .SetGroup(WorkspaceMenuCategory.ToSharedRef());
}


void FRvmatAssetEditor::UnregisterTabSpawners(const TSharedRef<FTabManager>& InTabManager)
{
    FAssetEditorToolkit::UnregisterTabSpawners(InTabManager);

    InTabManager->UnregisterTabSpawner(PreviewTabID);
    InTabManager->UnregisterTabSpawner(DetailsTabID);
}

TSharedRef<SDockTab> FRvmatAssetEditor::SpawnPreviewTab(const FSpawnTabArgs& Args)
{
    TSharedPtr<FRvmatPreviewScene> PreviewScene = MakeShareable(new FRvmatPreviewScene(FPreviewScene::ConstructionValues()));
    PreviewScene->SetPreviewMaterial(RvmatAsset);  // Set the material from your asset

    return SNew(SDockTab)
        .Label(LOCTEXT("PreviewTabLabel", "Preview"))
        [
            SNew(SRvmatEditorViewport)
                .PreviewScene(PreviewScene)  // Pass the preview scene to the viewport
        ];
}

TSharedRef<SDockTab> FRvmatAssetEditor::SpawnOutputTab(const FSpawnTabArgs& Args)
{
    return SNew(SDockTab)
        .Label(LOCTEXT("OutputTabLabel", "RVMAT Output"))
        [
            SNew(SMultiLineEditableTextBox)
                .IsReadOnly(true)  // Allow users to add output
                .AutoWrapText(true)
                .HintText(LOCTEXT("OutputPlaceholder", "RVMAT Output will be displayed here..."))  // Set placeholder text
        ];
}

TSharedRef<SDockTab> FRvmatAssetEditor::SpawnDetailsTab(const FSpawnTabArgs& Args)
{
    FPropertyEditorModule& PropertyEditorModule = FModuleManager::LoadModuleChecked<FPropertyEditorModule>("PropertyEditor");

    FDetailsViewArgs DetailsViewArgs;
    DetailsViewArgs.bUpdatesFromSelection = false;
    DetailsViewArgs.NotifyHook = nullptr;
    DetailsViewArgs.bLockable = false;
    DetailsViewArgs.NameAreaSettings = FDetailsViewArgs::HideNameArea;
    DetailsViewArgs.ViewIdentifier = "RvmatAssetEditorDetailsView";

    TSharedRef<IDetailsView> DetailsView = PropertyEditorModule.CreateDetailView(DetailsViewArgs);
    DetailsView->SetObject(RvmatAsset);

    return SNew(SDockTab)
        .Label(LOCTEXT("DetailsTabLabel", "Details"))
        [
            DetailsView
        ];
}

FName FRvmatAssetEditor::GetToolkitFName() const
{
    return FName("RvmatAssetEditor");
}

FText FRvmatAssetEditor::GetBaseToolkitName() const
{
    return LOCTEXT("AppLabel", "Rvmat Asset Editor");
}

FString FRvmatAssetEditor::GetWorldCentricTabPrefix() const
{
    return TEXT("RvmatAssetEditor");
}

FLinearColor FRvmatAssetEditor::GetWorldCentricTabColorScale() const
{
    return FLinearColor::White;
}

#undef LOCTEXT_NAMESPACE
