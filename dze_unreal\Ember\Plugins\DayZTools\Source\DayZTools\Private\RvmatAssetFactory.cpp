// RvmatAssetFactory.cpp
#include "RvmatAssetFactory.h"
#include "EditorAssetLibrary.h"
#include "RvmatAsset.h"
#include "RvmatUtils.h"
#include "DayZToolsSettings.h"
#include "AssetRegistry/AssetData.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "AssetToolsModule.h"
#include "IAssetTools.h"

URvmatAssetFactory::URvmatAssetFactory(const FObjectInitializer& ObjectInitializer)
    : Super(ObjectInitializer)
{
    SupportedClass = URvmatAsset::StaticClass();
    bCreateNew = false;  // Change this to false
    bEditAfterNew = true;
    bEditorImport = true;
    Formats.Add(TEXT("rvmat;Rvmat Asset"));
}

UObject* URvmatAssetFactory::FactoryCreateNew(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn)
{
    // This should not be called for imports
    return nullptr;
}

UObject* URvmatAssetFactory::FactoryCreateFile(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, const FString& Filename, const TCHAR* Parms, FFeedbackContext* Warn, bool& bOutOperationCanceled)
{
    UE_LOG(LogTemp, Display, TEXT("FactoryCreateFile called for: %s"), *Filename);

    FString FileContent;
    if (FFileHelper::LoadFileToString(FileContent, *Filename))
    {
        // Parse the RVMAT file
        TMap<FString, FRvmatUtils::VariantType> ParsedData = FRvmatUtils::ParseRVMat(FileContent);

        // Print parsed results for debugging
        UE_LOG(LogTemp, Display, TEXT("Parsed RVMAT Content:"));
        //PrintParsedData(ParsedData);

        // Create the asset
        URvmatAsset* NewAsset = NewObject<URvmatAsset>(InParent, InClass, InName, Flags);
        if (NewAsset)
        {
            // Set the parent material based on PixelShaderID
            SetParentMaterial(NewAsset, ParsedData);

            // Set surface properties
            SetSurfaceProperties(NewAsset, ParsedData);

            UE_LOG(LogTemp, Display, TEXT("Created new RvmatAsset: %s"), *NewAsset->GetName());
        }

        return NewAsset;
    }

    UE_LOG(LogTemp, Error, TEXT("Failed to load file: %s"), *Filename);
    return nullptr;
}

void URvmatAssetFactory::SetParentMaterial(URvmatAsset* Asset, const TMap<FString, FRvmatUtils::VariantType>& ParsedData)
{
    if (ParsedData.Contains("PixelShaderID"))
    {
        FString PixelShaderID = ParsedData["PixelShaderID"].Get<FString>();
        UE_LOG(LogTemp, Display, TEXT("PixelShaderID found: %s"), *PixelShaderID);

        FString MaterialName = GetMaterialNameFromPixelShaderID(PixelShaderID);
        FString MaterialPath = FString::Printf(TEXT("/Game/Shaders/%s.%s"), *MaterialName, *MaterialName);

        UMaterial* ParentMaterial = LoadObject<UMaterial>(nullptr, *MaterialPath);
        if (ParentMaterial)
        {
            Asset->SetParentEditorOnly(ParentMaterial);
            UE_LOG(LogTemp, Display, TEXT("Set parent material to: %s"), *ParentMaterial->GetName());
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("Failed to load parent material: %s"), *MaterialPath);
        }
    }
    else
    {
        UE_LOG(LogTemp, Warning, TEXT("PixelShaderID not found in parsed data"));
    }
}

void URvmatAssetFactory::SetSurfaceProperties(URvmatAsset* Asset, const TMap<FString, FRvmatUtils::VariantType>& ParsedData)
{
    const UDayZToolsSettings* Settings = GetDefault<UDayZToolsSettings>();
    FString TrunkPath = Settings->ProjectTrunkPath;

    for (const auto& Pair : ParsedData)
    {
        const FString& ParamName = Pair.Key;
        const FRvmatUtils::VariantType& Value = Pair.Value;

        switch (Value.Type)
        {
        case FRvmatUtils::EVariantType::Float:
            Asset->SetScalarParameterValueEditorOnly(FName(*ParamName), Value.Get<float>());
            UE_LOG(LogTemp, Display, TEXT("Set scalar parameter %s to %f"), *ParamName, Value.Get<float>());
            break;

        case FRvmatUtils::EVariantType::FloatArray:
        {
            TArray<float> FloatArray = Value.Get<TArray<float>>();
            if (FloatArray.Num() >= 3)
            {
                FLinearColor Color(FloatArray[0], FloatArray[1], FloatArray[2]);
                if (FloatArray.Num() >= 4)
                {
                    Color.A = FloatArray[3];
                }
                Asset->SetVectorParameterValueEditorOnly(FName(*ParamName), Color);
                UE_LOG(LogTemp, Display, TEXT("Set vector parameter %s to (%f, %f, %f, %f)"),
                    *ParamName, Color.R, Color.G, Color.B, Color.A);
            }
            break;
        }

        case FRvmatUtils::EVariantType::Map:
            if (ParamName.StartsWith("Stage"))
            {
                HandleStage(Asset, ParamName, Value.Get<TMap<FString, FRvmatUtils::VariantType>>(), TrunkPath);
            }
            break;

        default:
            UE_LOG(LogTemp, Warning, TEXT("Unknown parameter type for %s"), *ParamName);
            break;
        }
    }
}

void URvmatAssetFactory::HandleStage(URvmatAsset* Asset, const FString& StageName, const TMap<FString, FRvmatUtils::VariantType>& StageData, const FString& TrunkPath)
{
    if (StageData.Contains("texture"))
    {
        FString RelativeTexturePath = StageData["texture"].Get<FString>();

        // Check if the texture path starts with '#'
        if (RelativeTexturePath.StartsWith("#"))
        {
            UE_LOG(LogTemp, Warning, TEXT("Skipping texture import for %s: Path starts with '#' (%s)"), *StageName, *RelativeTexturePath);
            return;
        }

        if (RelativeTexturePath.IsEmpty())
        {
            UE_LOG(LogTemp, Warning, TEXT("The texture path is empty."));
            return;
        }

        FString FullTexturePath = FPaths::Combine(TrunkPath, RelativeTexturePath);

        // Import the texture
        UTexture* ImportedTexture = ImportTexture(FullTexturePath, TrunkPath);

        if (ImportedTexture)
        {
            // Set the texture parameter
            Asset->SetTextureParameterValueEditorOnly(FName(*StageName), ImportedTexture);
            UE_LOG(LogTemp, Display, TEXT("Set texture parameter %s to %s"), *StageName, *ImportedTexture->GetName());
        }
    }
}

UTexture* URvmatAssetFactory::ImportTexture(const FString& FullTexturePath, const FString& TrunkPath)
{
    // Normalize paths to use forward slashes
    FString FullPath = FullTexturePath.Replace(TEXT("\\"), TEXT("/"));
    FString NormalizedTrunkPath = TrunkPath.Replace(TEXT("\\"), TEXT("/"));

    // Get the relative path by removing the TrunkPath from the FullTexturePath
    FString RelativePath = FullPath;
    RelativePath.RemoveFromStart(NormalizedTrunkPath);
    RelativePath.RemoveFromStart(TEXT("/")); // Remove any leading slash

    FString Filename = FPaths::GetCleanFilename(FullPath);
    FString DestinationPath = FString::Printf(TEXT("/Game/%s"), *FPaths::GetPath(RelativePath));

    // Ensure the destination folder exists
    FString DestinationFolder = FPaths::GetPath(DestinationPath);
    if (!UEditorAssetLibrary::DoesDirectoryExist(DestinationFolder))
    {
        UEditorAssetLibrary::MakeDirectory(DestinationFolder);
    }

    // Import the texture using AssetTools
    FAssetToolsModule& AssetToolsModule = FModuleManager::GetModuleChecked<FAssetToolsModule>("AssetTools");
    TArray<FString> FileNames;
    FileNames.Add(FullPath);

    TArray<UObject*> ImportedAssets = AssetToolsModule.Get().ImportAssets(FileNames, DestinationFolder);
    if (ImportedAssets.Num() > 0)
    {
        UTexture* ImportedTexture = Cast<UTexture>(ImportedAssets[0]);
        if (ImportedTexture)
        {
            UE_LOG(LogTemp, Log, TEXT("Successfully imported texture: %s"), *FullPath);
            return ImportedTexture;
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to cast imported asset to UTexture: %s"), *FullPath);
        }
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to import texture: %s"), *FullPath);
    }

    return nullptr;
}


FString URvmatAssetFactory::GetMaterialNameFromPixelShaderID(const FString& PixelShaderID)
{
    FString MaterialName = PixelShaderID;

    // Convert to title case
    MaterialName = MaterialName.Left(1).ToUpper() + MaterialName.Mid(1).ToLower();

    // Add "MM_" prefix
    MaterialName = FString::Printf(TEXT("MM_%s"), *MaterialName);

    return MaterialName;
}

void URvmatAssetFactory::PrintParsedData(const TMap<FString, FRvmatUtils::VariantType>& Data, int32 Indent)
{
    for (const auto& Pair : Data)
    {
        FString IndentStr = FString::ChrN(Indent, TEXT(' '));

        switch (Pair.Value.Type)
        {
        case FRvmatUtils::EVariantType::Float:
            UE_LOG(LogTemp, Display, TEXT("%s%s: %f"), *IndentStr, *Pair.Key, Pair.Value.Get<float>());
            break;
        case FRvmatUtils::EVariantType::String:
            UE_LOG(LogTemp, Display, TEXT("%s%s: \"%s\""), *IndentStr, *Pair.Key, *Pair.Value.Get<FString>());
            break;
        case FRvmatUtils::EVariantType::FloatArray:
        {
            FString ArrayStr;
            for (float Value : Pair.Value.Get<TArray<float>>())
            {
                ArrayStr += FString::Printf(TEXT("%f, "), Value);
            }
            UE_LOG(LogTemp, Display, TEXT("%s%s: [%s]"), *IndentStr, *Pair.Key, *ArrayStr.LeftChop(2));
            break;
        }
        case FRvmatUtils::EVariantType::Map:
            UE_LOG(LogTemp, Display, TEXT("%s%s:"), *IndentStr, *Pair.Key);
            PrintParsedData(Pair.Value.Get<TMap<FString, FRvmatUtils::VariantType>>(), Indent + 2);
            break;
        }
    }
}

bool URvmatAssetFactory::CanCreateNew() const {
    return false;  // Change this to false
}

bool URvmatAssetFactory::ShouldShowInNewMenu() const
{
    return false;  // Change this to false
}