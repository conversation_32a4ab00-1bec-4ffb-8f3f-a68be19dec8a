// RvmatEditorTabFactory.cpp
#include "RvmatEditorTabFactory.h"
#include "RvmatEditorViewport.h"
#include "Styling/AppStyle.h"

#define LOCTEXT_NAMESPACE "FRvmatEditorTabFactory"

FRvmatEditorTabFactory::FRvmatEditorTabFactory(TSharedPtr<FAssetEditorToolkit> InEditor)
    : FWorkflowTabFactory(FName("Preview"), InEditor)  // Initialize the base class with a unique tab name
    , EditorPtr(InEditor)  // Store the editor pointer
{
    // Set tab label, icon, and other properties
    TabLabel = LOCTEXT("RvmatPreviewLabel", "Preview");
    TabIcon = FSlateIcon(FAppStyle::GetAppStyleSetName(), "Kismet.Tabs.Components");
    bIsSingleton = true;  // Ensures only one instance of this tab exists
}

TSharedRef<SWidget> FRvmatEditorTabFactory::CreateTabBody(const FWorkflowTabSpawnInfo& Info) const
{
    // Create a preview scene and pass it to your custom viewport widget
    TSharedPtr<FRvmatPreviewScene> PreviewScene = MakeShareable(new FRvmatPreviewScene(FPreviewScene::ConstructionValues()));

    return SNew(SRvmatEditorViewport)
        .PreviewScene(PreviewScene);  // Pass the PreviewScene as an argument if it's needed by your viewport
}

#undef LOCTEXT_NAMESPACE
