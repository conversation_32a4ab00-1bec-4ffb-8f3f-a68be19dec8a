#include "RvmatEditorViewport.h"
#include "RvmatViewportClient.h"

void SRvmatEditorViewport::Construct(const FArguments& InArgs)
{
    PreviewScene = InArgs._PreviewScene;  // Store the passed preview scene

    SEditorViewport::Construct(SEditorViewport::FArguments());
}

TSharedRef<FEditorViewportClient> SRvmatEditorViewport::MakeEditorViewportClient()
{
    // Pass the necessary arguments to the FRvmatViewportClient constructor
    ViewportClient = MakeShareable(new FRvmatViewportClient(PreviewScene.Get(), SharedThis(this)));
    return ViewportClient.ToSharedRef();
}

TSharedPtr<SWidget> SRvmatEditorViewport::MakeViewportToolbar()
{
    return SNew(SRvmatEditorViewportToolBar, SharedThis(this))
        .Cursor(EMouseCursor::Default);
}

TSharedRef<SEditorViewport> SRvmatEditorViewport::GetViewportWidget()
{
    return SharedThis(this);
}

TSharedPtr<FExtender> SRvmatEditorViewport::GetExtenders() const
{
    return MakeShareable(new FExtender);
}

void SRvmatEditorViewport::OnFloatingButtonClicked()
{
    // Handle floating button click
}
