#include "RvmatPreviewScene.h"
#include "Components/StaticMeshComponent.h"
#include "Components/DirectionalLightComponent.h"
#include "AssetViewerSettings.h"

FRvmatPreviewScene::FRvmatPreviewScene(ConstructionValues CVS)
    : FAdvancedPreviewScene(CVS)  // Call the constructor of FAdvancedPreviewScene
{
    // Load the sphere mesh
    UStaticMesh* SphereMesh = LoadObject<UStaticMesh>(nullptr, TEXT("/Engine/EngineMeshes/Sphere.Sphere"));

    // Create the static mesh component
    PreviewMeshComponent = NewObject<UStaticMeshComponent>();
    PreviewMeshComponent->SetStaticMesh(SphereMesh);
    PreviewMeshComponent->SetRelativeScale3D(FVector(1.0f));

    // Calculate the offset to position the bottom of the sphere at Z = 0
    FVector MeshOrigin = PreviewMeshComponent->Bounds.Origin;
    float SphereRadius = PreviewMeshComponent->Bounds.BoxExtent.Z;
    FVector Offset = FVector(0.0f, 0.0f, SphereRadius);  // Move up by the radius

    // Add the component with the calculated offset
    AddComponent(PreviewMeshComponent, FTransform(FRotator::ZeroRotator, Offset));

    // Setup lighting according to AdvancedPreviewScene defaults
    const UAssetViewerSettings* Settings = UAssetViewerSettings::Get();
    const int32 ProfileIndex = GetCurrentProfileIndex();
    if (Settings->Profiles.IsValidIndex(ProfileIndex))
    {
        // Make a mutable copy of the profile to pass to UpdateScene
        FPreviewSceneProfile MutableProfile = Settings->Profiles[ProfileIndex];
        UpdateScene(MutableProfile);
    }
}



void FRvmatPreviewScene::SetPreviewMaterial(UMaterialInterface* Material)
{
    if (PreviewMeshComponent)
    {
        PreviewMeshComponent->SetMaterial(0, Material);
    }
}

UStaticMeshComponent* FRvmatPreviewScene::GetPreviewMeshComponent() const
{
    return PreviewMeshComponent;
}
