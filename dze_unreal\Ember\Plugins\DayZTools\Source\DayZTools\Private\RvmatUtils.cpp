#include "RvmatUtils.h"
#include <regex>

TMap<FString, FRvmatUtils::VariantType> FRvmatUtils::LoadRVMat(const FString& FilePath)
{
    FString FileContent;
    if (FFileHelper::LoadFileToString(FileContent, *FilePath))
    {
        return ParseRVMat(FileContent);
    }
    return TMap<FString, VariantType>();
}

bool FRvmatUtils::SaveRVMat(const FString& FilePath, const TMap<FString, VariantType>& Data)
{
    FString SerializedContent = SerializeRVMat(Data);
    return FFileHelper::SaveStringToFile(SerializedContent, *FilePath);
}

TMap<FString, FRvmatUtils::VariantType> FRvmatUtils::ParseRVMat(const FString& FileContent)
{
    TArray<FString> Lines;
    FileContent.ParseIntoArray(Lines, TEXT("\n"), true);

    TMap<FString, VariantType> RootData;
    int32 Index = 0;

    while (Index < Lines.Num())
    {
        FString Line = Lines[Index].TrimStartAndEnd();
        if (Line.StartsWith(TEXT("class")))
        {
            FString ClassName;
            std::regex ClassPattern(R"(class\s+(\w+))");
            std::smatch Match;
            std::string LineStr = TCHAR_TO_UTF8(*Line);
            if (std::regex_search(LineStr, Match, ClassPattern))
            {
                ClassName = FString(Match[1].str().c_str());
                TMap<FString, VariantType> ClassData = ParseClass(Lines, Index);
                RootData.Add(ClassName, VariantType(TMap<FString, VariantType>(ClassData)));
            }
        }
        else
        {
            auto ParsedLine = ParseLine(Line);
            if (!ParsedLine.Key.IsEmpty())
            {
                RootData.Add(ParsedLine.Key, ParsedLine.Value);
            }
        }
        Index++;
    }

    return RootData;
}

TPair<FString, FRvmatUtils::VariantType> FRvmatUtils::ParseLine(const FString& Line)
{
    FString Key, Value;
    if (Line.Split(TEXT("="), &Key, &Value))
    {
        Key = Key.TrimStartAndEnd().Replace(TEXT("[]"), TEXT(""));
        Value = Value.TrimStartAndEnd().Replace(TEXT(";"), TEXT(""));

        if (Value.StartsWith(TEXT("{")) && Value.EndsWith(TEXT("}")))
        {
            return TPair<FString, VariantType>(Key, VariantType(ParseArray(Value)));
        }
        else if (Value.IsNumeric())
        {
            return TPair<FString, VariantType>(Key, VariantType(FCString::Atof(*Value)));
        }
        else if (Value.StartsWith(TEXT("\"")) && Value.EndsWith(TEXT("\"")))
        {
            return TPair<FString, VariantType>(Key, VariantType(Value.Mid(1, Value.Len() - 2)));
        }
    }
    return TPair<FString, VariantType>(FString(), VariantType());
}

TArray<float> FRvmatUtils::ParseArray(const FString& Value)
{
    FString ArrayContent = Value.Mid(1, Value.Len() - 2); // Remove curly braces
    TArray<FString> ArrayValues;
    ArrayContent.ParseIntoArray(ArrayValues, TEXT(","), true);

    TArray<float> ParsedArray;
    for (const FString& Element : ArrayValues)
    {
        ParsedArray.Add(FCString::Atof(*Element));
    }

    return ParsedArray;
}

TMap<FString, FRvmatUtils::VariantType> FRvmatUtils::ParseClass(const TArray<FString>& Lines, int32& Index)
{
    TMap<FString, VariantType> ClassData;
    Index++;  // Start after the class definition

    while (Index < Lines.Num())
    {
        FString Line = Lines[Index].TrimStartAndEnd();
        if (Line.StartsWith(TEXT("class")))
        {
            FString ClassName;
            std::regex ClassPattern(R"(class\s+(\w+))");
            std::smatch Match;
            std::string LineStr = TCHAR_TO_UTF8(*Line);
            if (std::regex_search(LineStr, Match, ClassPattern))
            {
                ClassName = FString(Match[1].str().c_str());
                TMap<FString, VariantType> NestedClass = ParseClass(Lines, Index);
                ClassData.Add(ClassName, VariantType(TMap<FString, VariantType>(NestedClass)));
            }
        }
        else if (Line.Equals(TEXT("};")))
        {
            return ClassData;
        }
        else
        {
            auto ParsedLine = ParseLine(Line);
            if (!ParsedLine.Key.IsEmpty())
            {
                ClassData.Add(ParsedLine.Key, ParsedLine.Value);
            }
        }
        Index++;
    }

    return ClassData;
}

FString FRvmatUtils::FormatValue(const VariantType& Value)
{
    switch (Value.Type)
    {
    case EVariantType::Float:
        return FString::SanitizeFloat(Value.Get<float>());
    case EVariantType::String:
        return FString::Printf(TEXT("\"%s\""), *Value.Get<FString>());
    case EVariantType::FloatArray:
    {
        FString FormattedArray = TEXT("{");
        const TArray<float>& Array = Value.Get<TArray<float>>();
        for (int32 i = 0; i < Array.Num(); i++)
        {
            FormattedArray += FString::SanitizeFloat(Array[i]);
            if (i < Array.Num() - 1)
            {
                FormattedArray += TEXT(",");
            }
        }
        FormattedArray += TEXT("}");
        return FormattedArray;
    }
    case EVariantType::Map:
        return SerializeRVMat(Value.Get<TMap<FString, VariantType>>());
    default:
        return FString();
    }
}

TArray<FString> FRvmatUtils::WriteClass(const TMap<FString, VariantType>& Data, int32 Indent)
{
    TArray<FString> Lines;
    FString IndentStr = FString::ChrN(Indent, TEXT(' '));
    for (const auto& Pair : Data)
    {
        if (Pair.Value.Type == EVariantType::Map)
        {
            Lines.Add(IndentStr + FString::Printf(TEXT("class %s"), *Pair.Key));
            Lines.Add(IndentStr + TEXT("{"));
            Lines.Append(WriteClass(Pair.Value.Get<TMap<FString, VariantType>>(), Indent + 4));
            Lines.Add(IndentStr + TEXT("};"));
        }
        else
        {
            FString Key = Pair.Key;
            if (Pair.Value.Type == EVariantType::FloatArray)
            {
                Key += TEXT("[]");
            }
            Lines.Add(IndentStr + FString::Printf(TEXT("%s=%s;"), *Key, *FormatValue(Pair.Value)));
        }
    }
    return Lines;
}

FString FRvmatUtils::SerializeRVMat(const TMap<FString, VariantType>& Data)
{
    TArray<FString> Lines = WriteClass(Data);
    return FString::Join(Lines, TEXT("\n"));
}