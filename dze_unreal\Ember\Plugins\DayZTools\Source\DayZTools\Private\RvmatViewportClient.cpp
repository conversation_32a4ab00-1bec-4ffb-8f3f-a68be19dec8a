#include "RvmatViewportClient.h"
#include "RvmatPreviewScene.h"
#include "Components/StaticMeshComponent.h"
#include "Engine/Engine.h"
#include "UnrealWidget.h"

FRvmatViewportClient::FRvmatViewportClient(FPreviewScene* InPreviewScene, const TSharedRef<SEditorViewport>& InEditorViewportWidget)
    : FEditorViewportClient(nullptr, InPreviewScene, InEditorViewportWidget),
    bIsRotating(false)
{
    PreviewScene = InPreviewScene;
    EditorViewportWidget = InEditorViewportWidget;

    // Configure the viewport
    SetViewMode(VMI_Lit);
    OverrideNearClipPlane(1.0f);
    bUsingOrbitCamera = true;

    // Disable unnecessary gizmos
    Widget->SetDefaultVisibility(false);

    // Set the initial camera position and orientation
    SetViewLocation(FVector(100.0f, 100.0f, 100.0f));
    SetViewRotation(FRotator(-25.0f, 0.0f, 0.0f));

    // Ensure the camera is positioned further back, similar to Material Editor
    FocusViewportOnBounds(FBoxSphereBounds(FVector::ZeroVector, FVector(100.0f, 100.0f, 100.0f), 100.0f), true);
}

void FRvmatViewportClient::Tick(float DeltaSeconds)
{
    FEditorViewportClient::Tick(DeltaSeconds);

    // Ensure the viewport is valid before making adjustments
    if (Viewport && PreviewScene)
    {
        FRvmatPreviewScene* RvmatPreviewScene = static_cast<FRvmatPreviewScene*>(PreviewScene);
        if (RvmatPreviewScene && RvmatPreviewScene->GetPreviewMeshComponent())
        {
            // Check if we need to focus the viewport (e.g., on first initialization)
            if (bShouldFocusOnBounds)
            {
                FocusViewportOnBounds(RvmatPreviewScene->GetPreviewMeshComponent()->Bounds, true);
                bShouldFocusOnBounds = false; // Prevent focusing on every tick
            }
        }

        // Tick the preview scene world
        PreviewScene->GetWorld()->Tick(LEVELTICK_All, DeltaSeconds);
    }
}


void FRvmatViewportClient::Draw(FViewport* InViewport, FCanvas* Canvas)
{
    FEditorViewportClient::Draw(InViewport, Canvas);
}

bool FRvmatViewportClient::InputKey(const FInputKeyEventArgs& EventArgs)
{
    if (EventArgs.Key == EKeys::MouseScrollUp || EventArgs.Key == EKeys::MouseScrollDown)
    {
        // Invert zoom direction
        float ZoomDirection = (EventArgs.Key == EKeys::MouseScrollUp) ? -1.0f : 1.0f;
        float ZoomSpeed = 50.0f;  // Adjust this value to control zoom speed

        FVector CameraLocation = GetViewLocation();
        FVector LookAtLocation = GetLookAtLocation();
        FVector CameraDirection = (CameraLocation - LookAtLocation).GetSafeNormal();

        FVector NewLocation = CameraLocation + CameraDirection * ZoomSpeed * ZoomDirection;
        SetViewLocation(NewLocation);

        Invalidate();
        return true;
    }

    if (EventArgs.Key == EKeys::F && EventArgs.Event == IE_Pressed)
    {
        FRvmatPreviewScene* RvmatPreviewScene = static_cast<FRvmatPreviewScene*>(PreviewScene);
        if (RvmatPreviewScene && RvmatPreviewScene->GetPreviewMeshComponent())
        {
            FocusViewportOnBounds(RvmatPreviewScene->GetPreviewMeshComponent()->Bounds, true);
        }

        return true;
    }

    return FEditorViewportClient::InputKey(EventArgs);
}



bool FRvmatViewportClient::InputAxis(FViewport* InViewport, FInputDeviceId DeviceId, FKey Key, float Delta, float DeltaTime, int32 NumSamples, bool bGamepad)
{
    

    // Pass other inputs to the base class
    return FEditorViewportClient::InputAxis(InViewport, DeviceId, Key, Delta, DeltaTime, NumSamples, bGamepad);
}


bool FRvmatViewportClient::ShouldOrbitCamera() const
{
    // The camera should always orbit around the preview object
    return true;
}

void FRvmatViewportClient::FocusViewportOnBounds(const FBoxSphereBounds& Bounds, bool bInstant)
{
    if (!Viewport)  // Check if Viewport is valid before using it
    {
        return;
    }
    // Calculate the focus position and distance
    const FVector Position = Bounds.Origin;
    float Radius = Bounds.SphereRadius;

    // Adjust for aspect ratio
    float LocalAspectRatio = Viewport->GetDesiredAspectRatio();
    if (LocalAspectRatio > 1.0f)
    {
        Radius *= LocalAspectRatio;
    }

    // Calculate the new camera distance
    const float HalfFOVRadians = FMath::DegreesToRadians(ViewFOV / 2.0f);
    const float DistanceFromSphere = Radius / FMath::Sin(HalfFOVRadians);
    const FVector CameraOffsetVector = GetViewRotation().Vector() * -DistanceFromSphere;

    FViewportCameraTransform& ViewTransform = GetViewTransform();

    // Set the new camera location
    ViewTransform.SetLookAt(Position);
    if (bInstant)
    {
        ViewTransform.SetLocation(Position + CameraOffsetVector);
    }
    else
    {
        //ViewTransform.TransitionToLocation(Position + CameraOffsetVector, EditorViewportWidget, false);
    }

    // Request the viewport to redraw
    Invalidate();
}



