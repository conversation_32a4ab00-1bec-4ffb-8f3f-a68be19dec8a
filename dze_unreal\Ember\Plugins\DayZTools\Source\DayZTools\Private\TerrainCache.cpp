#include "TerrainCache.h"
#include "Engine/World.h"
#include "Landscape.h"
#include "LandscapeProxy.h"
#include "Engine/Texture2D.h"
#include "TextureResource.h"
#include "RenderingThread.h"
#include "RHICommandList.h"
#include "EngineUtils.h"  // Added this include for TActorIterator

void UTerrainCacheSubsystem::Initialize(FSubsystemCollectionBase& Collection)
{
    Super::Initialize(Collection);
    FindLandscapeActor();
}

void UTerrainCacheSubsystem::Deinitialize()
{
    InvalidateAllCaches();
    Super::Deinitialize();
}

void UTerrainCacheSubsystem::FindLandscapeActor()
{
    if (!GetWorld()) return;

    // Fixed: Added proper TActorIterator usage
    for (TActorIterator<ALandscapeProxy> It(GetWorld()); It; ++It)
    {
        CachedLandscape = *It;
        break; // Take the first landscape found
    }
}

UTexture2D* UTerrainCacheSubsystem::GetOrCreateCache(const FString& CacheName, FIntPoint Resolution, bool bForceRecreate)
{
    if (!bForceRecreate && CacheEntries.Contains(CacheName))
    {
        return CacheEntries[CacheName].CacheTexture;
    }

    // Create new texture
    UTexture2D* NewTexture = UTexture2D::CreateTransient(Resolution.X, Resolution.Y, PF_R32_FLOAT);
    NewTexture->CompressionSettings = TC_HDR;
    NewTexture->SRGB = false;
    NewTexture->MipGenSettings = TMGS_NoMipmaps;
    NewTexture->AddressX = TA_Clamp;
    NewTexture->AddressY = TA_Clamp;
    NewTexture->UpdateResource();

    FTerrainCacheEntry& Entry = CacheEntries.FindOrAdd(CacheName);
    Entry.CacheTexture = NewTexture;
    Entry.Resolution = Resolution;
    Entry.LastUpdateTime = FDateTime::Now();

    if (CachedLandscape)
    {
        FVector Origin, BoxExtent;
        CachedLandscape->GetActorBounds(false, Origin, BoxExtent);
        Entry.WorldBounds = FBox2D(
            FVector2D(Origin.X - BoxExtent.X, Origin.Y - BoxExtent.Y),
            FVector2D(Origin.X + BoxExtent.X, Origin.Y + BoxExtent.Y)
        );
    }

    return NewTexture;
}

void UTerrainCacheSubsystem::InvalidateCache(const FString& CacheName)
{
    if (FTerrainCacheEntry* Entry = CacheEntries.Find(CacheName))
    {
        if (Entry->CacheTexture)
        {
            Entry->CacheTexture->ConditionalBeginDestroy();
        }
        CacheEntries.Remove(CacheName);
    }
}

void UTerrainCacheSubsystem::InvalidateAllCaches()
{
    for (auto& Pair : CacheEntries)
    {
        if (Pair.Value.CacheTexture)
        {
            Pair.Value.CacheTexture->ConditionalBeginDestroy();
        }
    }
    CacheEntries.Empty();
}

bool UTerrainCacheSubsystem::UpdateCacheTexture(const FString& CacheName, const TArray<float>& Data)
{
    FTerrainCacheEntry* Entry = CacheEntries.Find(CacheName);
    if (!Entry || !Entry->CacheTexture) return false;

    UTexture2D* Texture = Entry->CacheTexture;
    const int32 ExpectedSize = Entry->Resolution.X * Entry->Resolution.Y;

    if (Data.Num() != ExpectedSize)
    {
        UE_LOG(LogTemp, Error, TEXT("Data size mismatch. Expected %d, got %d"), ExpectedSize, Data.Num());
        return false;
    }

    // Update texture data
    FTexture2DMipMap& Mip = Texture->GetPlatformData()->Mips[0];
    void* TextureData = Mip.BulkData.Lock(LOCK_READ_WRITE);
    FMemory::Memcpy(TextureData, Data.GetData(), Data.Num() * sizeof(float));
    Mip.BulkData.Unlock();

    Texture->UpdateResource();
    Entry->LastUpdateTime = FDateTime::Now();

    return true;
}

bool UTerrainCacheSubsystem::ReadCacheTexture(const FString& CacheName, TArray<float>& OutData)
{
    FTerrainCacheEntry* Entry = CacheEntries.Find(CacheName);
    if (!Entry || !Entry->CacheTexture) return false;

    UTexture2D* Texture = Entry->CacheTexture;
    const int32 Size = Entry->Resolution.X * Entry->Resolution.Y;
    OutData.SetNum(Size);

    FTexture2DMipMap& Mip = Texture->GetPlatformData()->Mips[0];
    const void* TextureData = Mip.BulkData.LockReadOnly();
    FMemory::Memcpy(OutData.GetData(), TextureData, Size * sizeof(float));
    Mip.BulkData.Unlock();

    return true;
}

FVector2D UTerrainCacheSubsystem::WorldToUV(const FVector& WorldLocation) const
{
    for (const auto& Pair : CacheEntries)
    {
        const FBox2D& Bounds = Pair.Value.WorldBounds;
        FVector2D UV(
            (WorldLocation.X - Bounds.Min.X) / (Bounds.Max.X - Bounds.Min.X),
            (WorldLocation.Y - Bounds.Min.Y) / (Bounds.Max.Y - Bounds.Min.Y)
        );
        return UV;
    }
    return FVector2D(0.5f, 0.5f);
}

FVector UTerrainCacheSubsystem::UVToWorld(const FVector2D& UV, float Z) const
{
    for (const auto& Pair : CacheEntries)
    {
        const FBox2D& Bounds = Pair.Value.WorldBounds;
        return FVector(
            FMath::Lerp(Bounds.Min.X, Bounds.Max.X, UV.X),
            FMath::Lerp(Bounds.Min.Y, Bounds.Max.Y, UV.Y),
            Z
        );
    }
    return FVector::ZeroVector;
}