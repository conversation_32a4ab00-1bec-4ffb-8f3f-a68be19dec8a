#include "WRPUtils.h"
#include "CoreMinimal.h"
#include "PCGWorldActor.h"                   // /Engine/Plugins/PCG/Source/PCG/Public/PCGWorldActor.h
#include "Grid/PCGPartitionActor.h"          // /Engine/Plugins/PCG/Source/PCG/Public/Grid/PCGPartitionActor.h
#include "Kismet/GameplayStatics.h"

#include "BinaryReader.h"  // Include the reader here
#include "Misc/Paths.h"
#include "Misc/FileHelper.h"

#include "Async/ParallelFor.h"   // <<< make sure this is at the top

#if WITH_EDITOR
#include "Materials/MaterialInstanceConstant.h"
#include "MaterialEditorModule.h"       // for synchronous compile utilities
#include "ShaderCompiler.h"
#include "MaterialShared.h"
#endif        
// for GetStaticSwitchParameterValue

#include "Landscape.h"
#include "LandscapeInfo.h"
#include "LandscapeComponent.h"             // for TActorIterator
#include "LandscapeStreamingProxy.h"      // for ALandscapeStreamingProxy
#include "ImageUtils.h"
#include "IImageWrapper.h"
#include "IImageWrapperModule.h"
#include "HAL/PlatformFilemanager.h"
#include "Containers/Array.h"
#include "Modules/ModuleManager.h"
#include "Kismet/GameplayStatics.h" // For UGameplayStatics
#include "Engine/World.h"           // For GetWorld()
#include "DayZTools.h"
#include "LevelEditorToolbar.h"

#include "Misc/PackageName.h"

#include "EngineUtils.h"
#include "Editor.h"  // For GEditor

#include "HAL/PlatformProcess.h"
#include "Misc/ScopeExit.h"

#include "Internationalization/Regex.h"
#include "Engine/Texture2D.h"

#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/SceneCaptureComponent2D.h"
#include "Engine/TextureRenderTarget2D.h"

THIRD_PARTY_INCLUDES_START
#include "zlib.h"
THIRD_PARTY_INCLUDES_END

// In your FMaterialGenerator .cpp file, after your #includes:
static const FString BaseTemplate = FString(R"###(ambient[]={0.89999998,0.89999998,0.89999998,1};
diffuse[]={0.89999998,0.89999998,0.89999998,1};
forcedDiffuse[]={0.02,0.02,0.02,1};
emmisive[]={0,0,0,0};
specular[]={0,0,0,0};
specularPower=0;
class Stage0
{
    texture="{base_rel}\S_{tile_col:03d}_{tile_row:03d}_lco.png";
    texGen=3;
};
class Stage1
{
    texture="{base_rel}\M_{tile_col:03d}_{tile_row:03d}_lca.png";
    texGen=4;
};
class TexGen3
{
    uvSource="worldPos";
    class uvTransform
    {
        aside[]={0.001953125,0,0};
        up[]={0,0,0.001953125};
        dir[]={0,-0.001953125,0};
        pos[]={{tile_texgen_pos_x},{tile_texgen_pos_y},0};
    };
};
class TexGen4
{
    uvSource="worldPos";
    class uvTransform
    {
        aside[]={0.001953125,0,0};
        up[]={0,0,0.001953125};
        dir[]={0,-0.001953125,0};
        pos[]={{tile_texgen_pos_x},{tile_texgen_pos_y},0};
    };
};
class TexGen0
{
    uvSource="tex";
    class uvTransform
    {
        aside[]={1,0,0};
        up[]={0,1,0};
        dir[]={0,0,1};
        pos[]={0,0,0};
    };
};
class TexGen1
{
    uvSource="tex";
    class uvTransform
    {
        aside[]={10,0,0};
        up[]={0,10,0};
        dir[]={0,0,10};
        pos[]={0,0,0};
    };
};
class TexGen2
{
    uvSource="tex";
    class uvTransform
    {
        aside[]={10,0,0};
        up[]={0,10,0};
        dir[]={0,0,10};
        pos[]={0,0,0};
    };
};
PixelShaderID="TerrainX";
VertexShaderID="Terrain";
class Stage2
{
    texture="#(rgb,1,1,1)color(0.5,0.5,0.5,1,cdt)";
    texGen=0;
};
{dynamic_stages}
)###");




FMaterialGenerator::FMaterialGenerator()
{
    // Initialize file paths (update these to your environment)
    BaseAbsoluteLayerPath = TEXT("P:\\ez\\worlds\\ember\\data\\layers");
    BaseRelativeLayerPath = TEXT("ez\\worlds\\ember\\data\\layers");
    // Asset zoo
    //SurfaceMaskPath = TEXT("D:\\Projects\\DayZ\\Ember\\Ember_Raw\\EZ\\worlds\\asset_zoo\\textures\\mask_lco.png");
    // Ember
    SurfaceMaskPath = TEXT("D:\\Projects\\DayZ\\Ember\\Ember_Raw\\EZ\\worlds\\ember\\textures\\mask_lco.png");
    LayersConfigPath = TEXT("D:\\Projects\\DayZ\\Ember\\Ember_Raw\\EZ\\worlds\\ember\\layers.cfg");

    // At the top of your WRPUtils.cpp file:
    // Place this at the top of your .cpp file (after your #includes)

    // Initialize GlobalMaterials with index 0 = "None"
    GlobalMaterials.Add(TEXT(""));

    // Initialize the layer-to-splat mapping
    LayerToSplat.Add(0, FColor(0, 0, 0, 255));
    LayerToSplat.Add(1, FColor(255, 0, 0, 255));
    LayerToSplat.Add(2, FColor(0, 255, 0, 255));
    LayerToSplat.Add(3, FColor(0, 0, 255, 255));
    LayerToSplat.Add(4, FColor(0, 0, 0, 128));
    LayerToSplat.Add(5, FColor(0, 0, 0, 0));
}


void FMaterialGenerator::ConvertAllPNGsToPAA(const FString& DirectoryToConvert, const FString& Prefix)
{
    FString ImageToPAAPath = TEXT("D:/SteamLibrary/steamapps/common/DayZ Tools/Bin/ImageToPAA/ImageToPAA.exe");
    FPaths::MakePlatformFilename(ImageToPAAPath);

    // 2) Find every PNG with the prefix
    TArray<FString> PNGFiles;
    FString Mask = Prefix + TEXT("*.png");
    IFileManager::Get().FindFiles(PNGFiles, *(DirectoryToConvert / Mask), /*Files=*/true, /*Dirs=*/false);
    if (PNGFiles.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("No PNGs found with prefix '%s' in %s"), *Prefix, *DirectoryToConvert);
        return;
    }

    // 3) How many concurrent processes? (leave one core free)
    int32 MaxConcurrent = FMath::Clamp(FPlatformMisc::NumberOfCores() - 1, 1, PNGFiles.Num());

    TArray<FProcHandle> Handles;
    Handles.Reserve(MaxConcurrent);

    // 4) For each file, spin up a process (but never exceed MaxConcurrent)
    for (const FString& Name : PNGFiles)
    {
        // Wait until we have a free slot
        while (Handles.Num() >= MaxConcurrent)
        {
            // Sleep briefly, then reap any finished procs
            FPlatformProcess::Sleep(0.01f);
            for (int32 i = 0; i < Handles.Num(); ++i)
            {
                if (!FPlatformProcess::IsProcRunning(Handles[i]))
                {
                    FPlatformProcess::CloseProc(Handles[i]);
                    Handles.RemoveAt(i--);
                }
            }
        }

        // Build source & destination
        FString Src = FPaths::Combine(DirectoryToConvert, Name);
        FString Dst = FPaths::ChangeExtension(Src, TEXT(".paa"));

        FString Args = FString::Printf(TEXT("\"%s\" \"%s\""), *Src, *Dst);
        UE_LOG(LogTemp, Log, TEXT("ImageToPAA: %s %s"), *ImageToPAAPath, *Args);

        // Launch
        FProcHandle Proc = FPlatformProcess::CreateProc(
            *ImageToPAAPath,
            *Args,
            /*bLaunchDetached=*/ true,
            /*bLaunchHidden=*/ false,
            /*bLaunchReallyHidden=*/ false,
            /*OutProcessID=*/ nullptr,
            /*PriorityModifier=*/ 0,
            /*OptionalWorkingDir=*/ nullptr,
            /*PipeWriteChild=*/ nullptr
        );

        if (Proc.IsValid())
        {
            Handles.Add(Proc);
        }
        else
        {
            UE_LOG(LogTemp, Error, TEXT("Failed to launch ImageToPAA for %s"), *Src);
        }
    }

    // 5) Wait for any remaining procs
    for (FProcHandle& Proc : Handles)
    {
        FPlatformProcess::WaitForProc(Proc);
        FPlatformProcess::CloseProc(Proc);
    }
    Handles.Empty();

    UE_LOG(LogTemp, Log, TEXT("All ImageToPAA conversions completed for folder: %s"), *DirectoryToConvert);
}



void FMaterialGenerator::RemoveSurfaceTilePNGs(const FString& DirectoryToConvert)
{
    IFileManager& FileManager = IFileManager::Get();
    FString AllPNGs = FPaths::Combine(DirectoryToConvert, TEXT("*.png"));

    // Find all .png
    TArray<FString> FoundFiles;
    FileManager.FindFiles(FoundFiles, *AllPNGs, true, false); // Files, not directories

    for (const FString& File : FoundFiles)
    {
        FString FullPath = FPaths::Combine(DirectoryToConvert, File);
        bool bDeleted = FileManager.Delete(*FullPath, false, true, true);
        if (!bDeleted)
        {
            UE_LOG(LogTemp, Warning, TEXT("Failed to delete: %s"), *FullPath);
        }
        else
        {
            //UE_LOG(LogTemp, Log, TEXT("Deleted old PNG: %s"), *FullPath);
        }
    }
}


static void SuffixToTuple(const FString& Suffix, TArray<int32>& OutTuple)
{
    OutTuple.Empty();
    TArray<FString> Tokens;
    Suffix.ParseIntoArray(Tokens, TEXT("_"), true);
    for (const FString& Token : Tokens)
    {
        if (Token.StartsWith(TEXT("L")))
        {
            // "L02" -> 2
            FString NumStr = Token.RightChop(1);
            OutTuple.Add(FCString::Atoi(*NumStr));
        }
        else
        {
            // "N" -> 999
            OutTuple.Add(999);
        }
    }
}


bool FMaterialGenerator::SaveTileImage(const FString& SavePath, const TArray<FColor>& ImageData, int32 Width, int32 Height)
{
    // 1. Build raw PNG scanline data with a filter byte (0) at the start of each row.
    const int32 RowSize = 1 + Width * 4; // 1 filter byte + 4 bytes per pixel (RGBA)
    TArray<uint8> RawData;
    RawData.SetNum(Height * RowSize);

    int32 RawIndex = 0;
    for (int32 Row = 0; Row < Height; Row++)
    {
        // Filter byte: 0 = no filter
        RawData[RawIndex++] = 0;
        for (int32 Col = 0; Col < Width; Col++)
        {
            // Assuming ImageData is in row-major order.
            const FColor& Color = ImageData[Row * Width + Col];
            RawData[RawIndex++] = Color.R;
            RawData[RawIndex++] = Color.G;
            RawData[RawIndex++] = Color.B;
            RawData[RawIndex++] = Color.A;
        }
    }

    // 2. Compress the filtered raw data using zlib (produce a raw deflate stream).
    // Use deflateInit2 with window bits = -15 so that zlib doesn't output its own header/trailer.
    // Here we use compress_level = 7, memory level 9, and Z_DEFAULT_STRATEGY.
    z_stream ZStream;
    FMemory::Memzero(&ZStream, sizeof(ZStream));
    int32 Result = deflateInit2(&ZStream,
        7,            // Compression level 7 (more aggressive than 6)
        Z_DEFLATED,
        -15,          // Negative window bits = raw deflate stream
        9,            // Memory level 9 for best compression on uniform data
        Z_DEFAULT_STRATEGY);
    if (Result != Z_OK)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to initialize deflate stream: %d"), Result);
        return false;
    }

    const int32 EstimatedSize = deflateBound(&ZStream, RawData.Num());
    TArray<uint8> CompressedData;
    CompressedData.SetNum(EstimatedSize);

    ZStream.next_in = (Bytef*)RawData.GetData();
    ZStream.avail_in = RawData.Num();
    ZStream.next_out = CompressedData.GetData();
    ZStream.avail_out = CompressedData.Num();

    Result = deflate(&ZStream, Z_FINISH);
    deflateEnd(&ZStream);
    if (Result != Z_STREAM_END)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to compress data: %d"), Result);
        return false;
    }
    CompressedData.SetNum(ZStream.total_out);

    // 3. Build the complete zlib stream manually.
    // Prepend the desired zlib header bytes (0x78, 0x9C) and append the Adler32 checksum.
    TArray<uint8> ZlibData;
    ZlibData.Add(0x78);  // CMF: 0x78 (deflate, 32K window)
    ZlibData.Add(0x9C);  // FLG: 0x9C (which implies FLEVEL = 2)
    ZlibData.Append(CompressedData);

    // Compute Adler32 checksum over the uncompressed (filtered) data.
    uLong Adler = adler32(0L, Z_NULL, 0);
    Adler = adler32(Adler, RawData.GetData(), RawData.Num());
    ZlibData.Add((Adler >> 24) & 0xFF);
    ZlibData.Add((Adler >> 16) & 0xFF);
    ZlibData.Add((Adler >> 8) & 0xFF);
    ZlibData.Add(Adler & 0xFF);

    // 4. Assemble the PNG file.
    TArray<uint8> PNGData;
    PNGData.Reserve(ZlibData.Num() + 1024);  // Reserve extra space for PNG chunks

    // 4a. PNG Signature (8 bytes)
    const uint8 Signature[8] = { 0x89, 'P', 'N', 'G', 0x0D, 0x0A, 0x1A, 0x0A };
    PNGData.Append(Signature, 8);

    // 4b. IHDR Chunk
    const uint32 IHDRLength = 13;
    const int32 IHDRStart = PNGData.Num();
    // Write IHDR length (big-endian)
    PNGData.Add((IHDRLength >> 24) & 0xFF);
    PNGData.Add((IHDRLength >> 16) & 0xFF);
    PNGData.Add((IHDRLength >> 8) & 0xFF);
    PNGData.Add(IHDRLength & 0xFF);
    // Write chunk type: "IHDR"
    PNGData.Append((const uint8*)"IHDR", 4);
    // Write image width and height (big-endian)
    PNGData.Add((Width >> 24) & 0xFF);
    PNGData.Add((Width >> 16) & 0xFF);
    PNGData.Add((Width >> 8) & 0xFF);
    PNGData.Add(Width & 0xFF);
    PNGData.Add((Height >> 24) & 0xFF);
    PNGData.Add((Height >> 16) & 0xFF);
    PNGData.Add((Height >> 8) & 0xFF);
    PNGData.Add(Height & 0xFF);
    // Write IHDR fields: bit_depth=8, color_type=6 (RGBA), compression=0, filter=0, interlace=0.
    PNGData.Add(8);    // Bit depth
    PNGData.Add(6);    // Color type (RGBA)
    PNGData.Add(0);    // Compression method
    PNGData.Add(0);    // Filter method
    PNGData.Add(0);    // Interlace method
    // Compute and append IHDR CRC.
    uint32 CRC = FCrc::MemCrc32(PNGData.GetData() + IHDRStart + 4, IHDRLength + 4);
    PNGData.Add((CRC >> 24) & 0xFF);
    PNGData.Add((CRC >> 16) & 0xFF);
    PNGData.Add((CRC >> 8) & 0xFF);
    PNGData.Add(CRC & 0xFF);

    // 4c. IDAT Chunk
    const int32 IDATStart = PNGData.Num();
    uint32 IDATLength = ZlibData.Num();
    PNGData.Add((IDATLength >> 24) & 0xFF);
    PNGData.Add((IDATLength >> 16) & 0xFF);
    PNGData.Add((IDATLength >> 8) & 0xFF);
    PNGData.Add(IDATLength & 0xFF);
    // Chunk type: "IDAT"
    PNGData.Append((const uint8*)"IDAT", 4);
    // Append our custom zlib stream.
    PNGData.Append(ZlibData);
    CRC = FCrc::MemCrc32(PNGData.GetData() + IDATStart + 4, ZlibData.Num() + 4);
    PNGData.Add((CRC >> 24) & 0xFF);
    PNGData.Add((CRC >> 16) & 0xFF);
    PNGData.Add((CRC >> 8) & 0xFF);
    PNGData.Add(CRC & 0xFF);

    // 4d. IEND Chunk
    PNGData.Add(0); PNGData.Add(0); PNGData.Add(0); PNGData.Add(0);  // Length = 0
    PNGData.Append((const uint8*)"IEND", 4);
    PNGData.Add(0xAE); PNGData.Add(0x42); PNGData.Add(0x60); PNGData.Add(0x82);  // Fixed IEND CRC

    // 5. Save the PNG file.
    if (FFileHelper::SaveArrayToFile(PNGData, *SavePath))
    {
        //UE_LOG(LogTemp, Log, TEXT("Tile image saved to: %s (size: %d bytes)"), *SavePath, PNGData.Num());
        return true;
    }

    return false;
}

TArray<uint16> FMaterialGenerator::GenerateMaterialIndices()
{
    // Parse the layers configuration.
    if (!ParseLayersConfig())
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to parse layers config."));
        return TArray<uint16>();
    }

    // Reset our arrays/mappings.
    GlobalMaterials.SetNum(1);  // Start with a blank entry.
    GlobalBlockMapping.Empty();

    // --- Parameters ---

    /*
    /////// ASSET ZOO ////////
    const int32 ImageSize = 4096;  // Full image dimensions.
    const int32 TileSize = 512;   // Each tile is 512x512.
    const int32 TileCount = 11;    // 11 tiles per row/column.
    const int32 FirstTileOffset = -64;   // Global offset.
    const int32 TileStep = 384;   // Tiles overlap: step < TileSize.
    const int32 BlockSize = 32;    // Each block in a tile is 32x32.
    const int32 Pad = 64;    // Padding when computing dynamic block bounds.
    const int32 CellSize = 32;    // Global grid cell size.
    */

    /* EMBER */

    const int32 ImageSize = 16384;  // Full image dimensions.
    const int32 TileSize = 512;   // Each tile is 512x512.
    const int32 TileCount = 43;    // 11 tiles per row/column.
    const int32 FirstTileOffset = -64;   // Global offset.
    const int32 TileStep = 384;   // Tiles overlap: step < TileSize.
    const int32 BlockSize = 32;    // Each block in a tile is 32x32.
    const int32 Pad = 64;    // Padding when computing dynamic block bounds.
    const int32 CellSize = 32;    // Global grid cell size.

    // Build splat tile data (this call uses your existing routines to process each tile,
    // generate RVMat files, and fill GlobalBlockMapping).
    CreateSplatTilesIncludingPadding(ImageSize, TileSize, TileCount, FirstTileOffset, TileStep, BlockSize);

    // --- Global Grid Setup ---
    const int32 LandRange = ImageSize / CellSize;  // e.g. 4096/32 = 128 cells per side.
    TArray<uint16> OutGlobalMaterialIndices;
    OutGlobalMaterialIndices.Init(0, LandRange * LandRange);

    // Precompute dynamic block boundaries (identical for every tile).
    TArray<TPair<int32, int32>> XBounds = GetDynamicBlockBounds(TileSize, Pad, BlockSize);
    TArray<TPair<int32, int32>> YBounds = GetDynamicBlockBounds(TileSize, Pad, BlockSize);

    // --- Global Iteration with Inverted Y ---
    // In your expected output, the very first index (i.e. [0]) comes from the bottom of the image.
    // So we iterate so that cell index 0 corresponds to the bottom row.
    for (int32 cellY = 0; cellY < LandRange; cellY++)
    {
        // Invert the Y cell coordinate: cell 0 becomes the bottom row.
        int32 invertedCellY = LandRange - cellY - 1;
        for (int32 cellX = 0; cellX < LandRange; cellX++)
        {
            // Compute the global pixel coordinate.
            int32 globalX = cellX * CellSize;
            int32 globalY = invertedCellY * CellSize;

            // Determine which tile covers this global coordinate.
            int32 tileCol = GetGridIndex(globalX, TileStep, TileCount, ImageSize);
            int32 tileRow = GetGridIndex(globalY, TileStep, TileCount, ImageSize);

            // Compute the tile's top left origin.
            int32 tileOriginX = FirstTileOffset + tileCol * TileStep;
            int32 tileOriginY = FirstTileOffset + tileRow * TileStep;

            // Convert global coordinates to local coordinates within the tile.
            int32 localX = globalX - tileOriginX;
            int32 localY = globalY - tileOriginY;

            // Determine the block indices within the tile using both X and Y boundaries.
            int32 blockX = FindDynamicBlockIndex(localX, XBounds);
            int32 blockY = FindDynamicBlockIndex(localY, YBounds);

            // Build the key used when registering blocks (must match ProcessTileAndRegisterBlocks).
            FIntVector MappingKey(tileCol, tileRow, blockX * 1000 + blockY);
            uint16 matIndex = GlobalBlockMapping.Contains(MappingKey) ? GlobalBlockMapping[MappingKey] : 0;

            // Compute the flat index into our global array.
            int32 flatIndex = cellY * LandRange + cellX;
            OutGlobalMaterialIndices[flatIndex] = matIndex;
        }
    }

    return OutGlobalMaterialIndices;
}


//---------------------------------------------------------------------
// Parse the layers configuration file into LayersInfo.
//---------------------------------------------------------------------
bool FMaterialGenerator::ParseLayersConfig()
{
    FString ConfigText;
    if (!FFileHelper::LoadFileToString(ConfigText, *LayersConfigPath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load layers config: %s"), *LayersConfigPath);
        return false;
    }

    FString LayersBlock;
    // Use (?s) so that . matches newline (DOTALL mode)
    if (!ExtractLayersBlock(ConfigText, LayersBlock))
    {
        UE_LOG(LogTemp, Warning, TEXT("No 'Layers' block found in config."));
    }
    else
    {
        // Pattern with DOTALL: (?s) tells regex to treat . as matching newlines.
        FRegexPattern Pattern(TEXT("(?s)class\\s+(\\w+)\\s*\\{(.*?)\\}\\s*;"));
        FRegexMatcher Matcher(Pattern, LayersBlock);
        while (Matcher.FindNext())
        {
            FString LayerName = Matcher.GetCaptureGroup(1);
            FString Content = Matcher.GetCaptureGroup(2);
            FLayerInfo Info;

            // Use a pattern that matches until the next quote
            FRegexPattern TexturePattern(TEXT("texture\\s*=\\s*\"([^\"]*)\";"));
            FRegexMatcher TextureMatcher(TexturePattern, Content);
            if (TextureMatcher.FindNext())
            {
                Info.Texture = TextureMatcher.GetCaptureGroup(1);
            }
            else
            {
                Info.Texture = ""; // fallback if not found
            }

            FRegexPattern MaterialPattern(TEXT("material\\s*=\\s*\"([^\"]*)\";"));
            FRegexMatcher MaterialMatcher(MaterialPattern, Content);
            if (MaterialMatcher.FindNext())
            {
                Info.Material = MaterialMatcher.GetCaptureGroup(1);
            }
            else
            {
                Info.Material = "";
            }
            // Only add the layer if it hasn t been seen yet (to preserve insertion order)
            if (!LayersInfo.Contains(LayerName))
            {
                LayersInfo.Add(LayerName, Info);
                GlobalLayerNames.Add(LayerName);
            }
        }
    }

    // In ParseLayersConfig(), replace the color parsing section with this:
    FRegexPattern ColorPattern(TEXT("(\\w+)\\[\\]\\s*=\\s*\\{\\{\\s*([0-9,\\s]+)\\s*\\}\\};"));
    FRegexMatcher ColorMatcher(ColorPattern, ConfigText);

    UE_LOG(LogTemp, Log, TEXT("=== Starting Color Parsing ==="));

    while (ColorMatcher.FindNext())
    {
        FString LayerName = ColorMatcher.GetCaptureGroup(1);
        FString ColorStr = ColorMatcher.GetCaptureGroup(2);

        //UE_LOG(LogTemp, Log, TEXT("Found color definition:"));
        //UE_LOG(LogTemp, Log, TEXT("  Layer: %s"), *LayerName);
        //UE_LOG(LogTemp, Log, TEXT("  Raw color string: %s"), *ColorStr);

        TArray<FString> ColorComponents;
        ColorStr.ParseIntoArray(ColorComponents, TEXT(","), true);

        //UE_LOG(LogTemp, Log, TEXT("  Parsed components (%d):"), ColorComponents.Num());
        for (const FString& Comp : ColorComponents)
        {
            //UE_LOG(LogTemp, Log, TEXT("    Component: '%s'"), *Comp.TrimStartAndEnd());
        }

        int32 R = 0, G = 0, B = 0;
        if (ColorComponents.Num() >= 3)
        {
            R = FCString::Atoi(*ColorComponents[0].TrimStartAndEnd());
            G = FCString::Atoi(*ColorComponents[1].TrimStartAndEnd());
            B = FCString::Atoi(*ColorComponents[2].TrimStartAndEnd());
        }

        // IMPORTANT: The colors in the layers.cfg file are now in LINEAR color space
        // Create a linear FColor directly from the parsed values
        FColor LinearColor(R, G, B, 255);  // Always set alpha to 255 for comparison

        UE_LOG(LogTemp, Verbose, TEXT("Parsed linear color for layer %s: (%d,%d,%d)"),
            *LayerName, LinearColor.R, LinearColor.G, LinearColor.B);

        if (LayersInfo.Contains(LayerName))
        {
            LayersInfo[LayerName].Color = LinearColor;
            UE_LOG(LogTemp, Verbose, TEXT("  Updated existing layer with linear color"));
        }
        else
        {
            FLayerInfo Info;
            Info.Color = LinearColor;
            LayersInfo.Add(LayerName, Info);
            GlobalLayerNames.Add(LayerName);
            UE_LOG(LogTemp, Verbose, TEXT("  Created new layer with linear color"));
        }
    }

    // Build ColorToLayer mapping using the EXACT same order as Python
    ColorToLayer.Empty();
    for (int32 i = 0; i < GlobalLayerNames.Num(); i++)
    {
        const FString& LayerName = GlobalLayerNames[i];
        if (LayersInfo.Contains(LayerName))
        {
            FColor KeyColor = LayersInfo[LayerName].Color;
            KeyColor.A = 255;  // Ensure alpha is 255 for comparison
            ColorToLayer.Add(KeyColor, i);
        }
    }

    // Debug: Print the raw config text first
    //UE_LOG(LogTemp, Log, TEXT("Raw config text (first 500 chars): %s"),
    //    *ConfigText.Left(500));

    // Debug: Print all layer info
    UE_LOG(LogTemp, Log, TEXT("=== Layer Information ==="));
    for (const auto& Pair : LayersInfo)
    {
        const FString& LayerName = Pair.Key;
        const FLayerInfo& Info = Pair.Value;
        UE_LOG(LogTemp, Log, TEXT("Layer: %s"), *LayerName);
        UE_LOG(LogTemp, Log, TEXT("  Color: (%d,%d,%d,%d)"),
            Info.Color.R, Info.Color.G, Info.Color.B, Info.Color.A);
        UE_LOG(LogTemp, Log, TEXT("  Texture: %s"), *Info.Texture);
        UE_LOG(LogTemp, Log, TEXT("  Material: %s"), *Info.Material);
    }

    // Debug: Print GlobalLayerNames order
    UE_LOG(LogTemp, Log, TEXT("=== Global Layer Names (in order) ==="));
    for (int32 i = 0; i < GlobalLayerNames.Num(); i++)
    {
        UE_LOG(LogTemp, Log, TEXT("[%d]: %s"), i, *GlobalLayerNames[i]);
    }

    // Debug: Print ColorToLayer mapping
    UE_LOG(LogTemp, Log, TEXT("=== Color To Layer Mapping ==="));
    for (const auto& Pair : ColorToLayer)
    {
        const FColor& Color = Pair.Key;
        int32 LayerIndex = Pair.Value;
        UE_LOG(LogTemp, Log, TEXT("Color(%d,%d,%d,%d) -> Layer %d"),
            Color.R, Color.G, Color.B, Color.A, LayerIndex);
    }

    return true;
}




//---------------------------------------------------------------------
// Extract the "Layers" block from the config text.
//---------------------------------------------------------------------
bool FMaterialGenerator::ExtractLayersBlock(const FString& ConfigText, FString& OutBlock)
{
    int32 StartIdx = ConfigText.Find(TEXT("class Layers"));
    if (StartIdx == INDEX_NONE)
    {
        return false;
    }
    int32 BraceIdx = ConfigText.Find(TEXT("{"), ESearchCase::IgnoreCase, ESearchDir::FromStart, StartIdx);
    if (BraceIdx == INDEX_NONE)
    {
        return false;
    }
    int32 Depth = 0;
    int32 EndIdx = BraceIdx;
    for (int32 i = BraceIdx; i < ConfigText.Len(); i++)
    {
        TCHAR ch = ConfigText[i];
        if (ch == '{')
        {
            Depth++;
        }
        else if (ch == '}')
        {
            Depth--;
            if (Depth == 0)
            {
                EndIdx = i;
                break;
            }
        }
    }
    if (EndIdx > BraceIdx)
    {
        OutBlock = ConfigText.Mid(BraceIdx + 1, EndIdx - BraceIdx - 1);
        return true;
    }
    return false;
}

//---------------------------------------------------------------------
// Get the ordered list of unique layers in the tile data.
//---------------------------------------------------------------------
void FMaterialGenerator::GetTileLayerOrder(const TArray<TArray<uint8>>& TileData, TArray<int32>& OutTileOrder)
{
    // Clear the output array.
    OutTileOrder.Empty();

    // Loop over the tile and collect unique layers.
    for (int32 y = 0; y < TileData.Num(); y++)
    {
        for (int32 x = 0; x < TileData[y].Num(); x++)
        {
            OutTileOrder.AddUnique(TileData[y][x]);
        }
    }

    // Now sort the layers numerically.
    OutTileOrder.Sort([](int32 A, int32 B)
        {
            return A < B;
        });
}



//---------------------------------------------------------------------
// Build the layer index map from the surface mask image.
//---------------------------------------------------------------------

#include "Rendering/Texture2DResource.h"

bool FMaterialGenerator::BuildLayerIndexMap(int32 Size)
{
    #if WITH_EDITOR
    UWorld* World = nullptr;
    if (GEditor)
    {
        // This gives you the “play” or “editor” world
        World = GEditor->GetEditorWorldContext().World();
    }
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("BuildLayerIndexMap: could not find an active editor world"));
        return false;
    }
#else
    UE_LOG(LogTemp, Error, TEXT("BuildLayerIndexMap only works in the editor"));
    return false;
#endif

    // Derive the sharedassets path from the current map
    // e.g. "/Game/ez/worlds/ember/ember" → "/Game/ez/worlds/ember/ember_sharedassets/ember_surface_lco"
    const FString WorldPackage = World->GetOutermost()->GetName();               // "/Game/ez/worlds/ember/ember"
    const FString WorldBase    = FPackageName::GetShortName(WorldPackage);           // "ember"
    const FString WorldDir     = FPackageName::GetLongPackagePath(WorldPackage);     // "/Game/ez/worlds/ember"
    const FString SharedDir    = WorldDir / (WorldBase + TEXT("_sharedassets"));     // "/Game/ez/worlds/ember/ember_sharedassets"
    const FString AssetName    = WorldBase + TEXT("_surface_lco");                   // "ember_surface_lco"
    const FString AssetPath    = SharedDir / AssetName + TEXT(".") + AssetName;      // ".../ember_surface_lco.ember_surface_lco"

    // 1) Load our mask texture asset
    UTexture2D* MaskTex = Cast<UTexture2D>(
        StaticLoadObject(UTexture2D::StaticClass(), nullptr, *AssetPath)
    );
    if (!MaskTex)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load texture asset: %s"), *AssetPath);
        return false;
    }

   // 2) Read directly from the Source (linear bytes you Init’d), not the compressed platform data
   MaskTex->SetForceMipLevelsToBeResident(30.0f);
   MaskTex->WaitForStreaming();

   // 3) Access the raw RGBA8 bulk data
   FTexturePlatformData* PlatData = MaskTex->GetPlatformData();
   if (!PlatData || PlatData->Mips.Num() == 0)
   {
       UE_LOG(LogTemp, Error, TEXT("Texture has no platform data or mips"));
       return false;
   }

   FTexture2DMipMap& Mip0 = PlatData->Mips[0];
   const int32 W = Mip0.SizeX;
   const int32 H = Mip0.SizeY;
    if (W != Size || H != Size)
    {
        UE_LOG(LogTemp, Error, TEXT("Expected %d×%d, got %d×%d"), Size, Size, W, H);
        return false;
    }

    // 4) Prepare output buffer
    MaskSize = Size;
    LayerFlat.SetNumUninitialized(W * H);

    // 5) Build fast color→layer lookup in LINEAR color space
    TMap<uint32, uint8> FastColorMap;
    for (auto& Pair : ColorToLayer)
    {
        // Get the sRGB color from the map
        const FColor& SRGBColor = Pair.Key;

        // Convert to linear color space
        FLinearColor LinearColor = FLinearColor::FromSRGBColor(SRGBColor);
        FColor LinearFColor = LinearColor.ToFColor(false);

        // Create key from linear color
        uint32 Key = (LinearFColor.R << 16) | (LinearFColor.G << 8) | LinearFColor.B;

        UE_LOG(LogTemp, Verbose, TEXT("Color mapping: sRGB(%d,%d,%d) -> Linear(%d,%d,%d) -> Key: 0x%06X"),
            SRGBColor.R, SRGBColor.G, SRGBColor.B,
            LinearFColor.R, LinearFColor.G, LinearFColor.B,
            Key);

        FastColorMap.Add(Key, Pair.Value);
    }

   // 6) Lock & read your original pixels
   void* RawPtr = Mip0.BulkData.Lock(LOCK_READ_ONLY);
   const FColor* Pixels = static_cast<const FColor*>(RawPtr);

    ParallelFor(H, [&](int32 y)
    {
        const FColor* RowSrc = Pixels + y * W;
        uint8* RowDst = LayerFlat.GetData() + y * W;

        for (int32 x = 0; x < W; ++x)
        {
            // Get the pixel color (which is in sRGB space)
            const FColor& SRGBPixel = RowSrc[x];

            // Convert to linear color space
            FLinearColor LinearColor = FLinearColor::FromSRGBColor(SRGBPixel);
            FColor LinearFColor = LinearColor.ToFColor(false);

            // Build key from linear color values
            uint32 Key = (LinearFColor.R << 16) | (LinearFColor.G << 8) | LinearFColor.B;

            // Map to layer
            if (uint8* Found = FastColorMap.Find(Key))
            {
                RowDst[x] = *Found;
            }
            else
            {
                // If no exact match, try to find the closest color
                uint8 BestLayer = 0;
                int32 BestDistance = INT32_MAX;

                for (const auto& Pair : FastColorMap)
                {
                    uint32 MapKey = Pair.Key;
                    uint8 MapR = (MapKey >> 16) & 0xFF;
                    uint8 MapG = (MapKey >> 8) & 0xFF;
                    uint8 MapB = MapKey & 0xFF;

                    int32 Distance = FMath::Abs((int32)LinearFColor.R - (int32)MapR) +
                                    FMath::Abs((int32)LinearFColor.G - (int32)MapG) +
                                    FMath::Abs((int32)LinearFColor.B - (int32)MapB);

                    if (Distance < BestDistance)
                    {
                        BestDistance = Distance;
                        BestLayer = Pair.Value;
                    }
                }

                // Use the closest layer if the distance is within a reasonable threshold
                if (BestDistance < 30) // Adjust threshold as needed
                {
                    RowDst[x] = BestLayer;
                }
                else
                {
                    RowDst[x] = 0; // Default to layer 0
                }
            }
        }
    });

    Mip0.BulkData.Unlock();
    return true;
}



//---------------------------------------------------------------------
// Extract a tile s layer indices from the full layer map (with clamping).
//---------------------------------------------------------------------
void FMaterialGenerator::ExtractTileLayerIndices(int32 XStart,
    int32 YStart,
    int32 TileSize,
    TArray<TArray<uint8>>& OutTileData)
{
OutTileData.SetNum(TileSize);

const int32 W = MaskSize;
for (int32 ty = 0; ty < TileSize; ++ty)
{
// clamp Y once
int32 sy = FMath::Clamp(YStart + ty, 0, W - 1);

OutTileData[ty].SetNumUninitialized(TileSize);
uint8* Dst = OutTileData[ty].GetData();

// clamp XStart so the first byte is valid,
// then memcpy a straight run of TileSize bytes.
int32 sx0 = FMath::Clamp(XStart, 0, W - 1);
const uint8* Src = LayerFlat.GetData() + sy * W + sx0;
FMemory::Memcpy(Dst, Src, TileSize);
}
}
//---------------------------------------------------------------------
// Convert tile indices to an RGBA image (splat) stored as an array of FColor.
//---------------------------------------------------------------------
void FMaterialGenerator::TileIndicesToSplatRGBA(const TArray<TArray<uint8>>& TileData, TArray<FColor>& OutImageData)
{
    static const TArray<FColor> COLORS = {
        FColor(0, 0, 0, 255),      // Black
        FColor(255, 0, 0, 255),    // Red
        FColor(0, 255, 0, 255),    // Green
        FColor(0, 0, 255, 255),    // Blue
        FColor(0, 0, 0, 128),      // Mid-grey
        FColor(0, 0, 0, 0)         // Transparent
    };

    int32 TileSize = TileData.Num();
    OutImageData.SetNum(TileSize * TileSize);

    // First pass: collect unique layers
    TSet<uint8> UniqueLayers;
    for (int32 y = 0; y < TileSize; y++)
    {
        for (int32 x = 0; x < TileSize; x++)
        {
            UniqueLayers.Add(TileData[y][x]);
        }
    }

    // Convert to sorted array
    TArray<uint8> SortedLayers = UniqueLayers.Array();
    SortedLayers.Sort();

    // Create layer to color mapping
    TMap<uint8, FColor> LayerToColor;
    if (SortedLayers.Num() == 1)
    {
        // Single layer always gets black
        LayerToColor.Add(SortedLayers[0], COLORS[0]);
    }
    else
    {
        // Multiple layers get mapped in order
        for (int32 i = 0; i < SortedLayers.Num(); i++)
        {
            int32 ColorIndex = FMath::Min(i, COLORS.Num() - 1);
            LayerToColor.Add(SortedLayers[i], COLORS[ColorIndex]);
        }
    }

    // Second pass: apply colors
    for (int32 y = 0; y < TileSize; y++)
    {
        for (int32 x = 0; x < TileSize; x++)
        {
            uint8 Layer = TileData[y][x];
            OutImageData[y * TileSize + x] = LayerToColor.FindRef(Layer);
        }
    }

    // Debug log
    //UE_LOG(LogTemp, Log, TEXT("TileIndicesToSplatRGBA - Unique layers found: %d"), UniqueLayers.Num());
    for (uint8 Layer : SortedLayers)
    {
        const FColor& Color = LayerToColor[Layer];
        //UE_LOG(LogTemp, Log, TEXT("Layer %d -> Color(%d,%d,%d,%d)"),
        //    Layer, Color.R, Color.G, Color.B, Color.A);
    }
}

// NEW: Helper function that computes dynamic boundaries for one dimension.
TArray<TPair<int32, int32>> FMaterialGenerator::GetDynamicBlockBounds(int32 TileDim, int32 Pad, int32 BlockSize)
{
    TArray<TPair<int32, int32>> Bounds;
    // The first block extends from 0 to (Pad + BlockSize)
    Bounds.Add(TPair<int32, int32>(0, Pad + BlockSize));
    // The valid region (excluding padding on both sides) is from Pad to (TileDim - Pad)
    int32 ValidWidth = (TileDim - 2 * Pad);
    int32 NumInterior = FMath::CeilToInt((float)ValidWidth / (float)BlockSize);
    // For interior blocks (if any)
    for (int32 i = 1; i < NumInterior; i++)
    {
        int32 Left = Pad + i * BlockSize;
        int32 Right = Left + BlockSize;
        // For the last block, extend to TileDim.
        if (i == NumInterior - 1)
        {
            Right = TileDim;
        }
        Bounds.Add(TPair<int32, int32>(Left, Right));
    }
    return Bounds;
}


// NEW: Helper to determine the block index given a coordinate and a set of boundaries.
int32 FMaterialGenerator::FindDynamicBlockIndex(int32 Coord, const TArray<TPair<int32, int32>>& Bounds)
{
    for (int32 i = 0; i < Bounds.Num(); i++)
    {
        int32 Left = Bounds[i].Key;
        int32 Right = Bounds[i].Value;
        if (Coord >= Left && Coord < Right)
        {
            return i;
        }
    }
    return Bounds.Num() - 1;
}


//---------------------------------------------------------------------
// Process a single tile: subdivide into blocks, compute a suffix for each block,
// register materials, and update GlobalBlockMapping.
//---------------------------------------------------------------------
void FMaterialGenerator::ProcessTileAndRegisterBlocks(const TArray<TArray<uint8>>& TileData,
    int32 TileCol, int32 TileRow,
    const TArray<int32>& TileOrder,
    int32 BlockSize,
    int32 Pad)  // typically 64
{
    int32 TileSize = TileData.Num(); // e.g. 512
    TArray<TPair<int32, int32>> XBounds = GetDynamicBlockBounds(TileSize, Pad, BlockSize);
    TArray<TPair<int32, int32>> YBounds = GetDynamicBlockBounds(TileSize, Pad, BlockSize);

    bool DEBUG = false;

    if (DEBUG)
    {
        //UE_LOG(LogTemp, Log, TEXT("\n[Dynamic Block] Analyzing Tile %03d-%03d:"), TileCol, TileRow);
        FString XBoundStr;
        for (const auto& B : XBounds)
        {
            XBoundStr += FString::Printf(TEXT("[%d,%d] "), B.Key, B.Value);
        }
        FString YBoundStr;
        for (const auto& B : YBounds)
        {
            YBoundStr += FString::Printf(TEXT("[%d,%d] "), B.Key, B.Value);
        }
        //UE_LOG(LogTemp, Log, TEXT("X bounds: %s"), *XBoundStr);
        //UE_LOG(LogTemp, Log, TEXT("Y bounds: %s"), *YBoundStr);
    }

    // Instead of collecting unique suffixes in an alphabetically sorted set,
    // we will record them in the order encountered in a flat array.
    TMap<FIntPoint, FString> BlockSuffixes;
    TArray<FString> OrderedSuffixes;

    //asset zoo
    float texgenPosX = 0.125f - 0.75f * TileCol;
    //float texgenPosY = 8.125f - 0.75f * TileRow;

    float texgenPosY = 32.125f - 0.75f * TileRow;

    // Loop through block grid in row-first order.
    for (int32 by = 0; by < YBounds.Num(); by++)
    {
        for (int32 bx = 0; bx < XBounds.Num(); bx++)
        {
            int32 x0 = XBounds[bx].Key;
            int32 x1 = XBounds[bx].Value;
            int32 y0 = YBounds[by].Key;
            int32 y1 = YBounds[by].Value;

            TSet<int32> LayersInBlock;
            for (int32 y = y0; y < y1 && y < TileSize; y++)
            {
                for (int32 x = x0; x < x1 && x < TileSize; x++)
                {
                    LayersInBlock.Add(TileData[y][x]);
                }
            }
            // Build a suffix string: for each element in TileOrder, if present then "Lxx", else "N".
            TArray<FString> Placeholders;
            Placeholders.Init(TEXT("N"), TileOrder.Num());
            for (int32 L : TileOrder)
            {
                if (LayersInBlock.Contains(L))
                {
                    int32 OrderIndex = TileOrder.Find(L);
                    if (OrderIndex != INDEX_NONE)
                    {
                        Placeholders[OrderIndex] = FString::Printf(TEXT("L%02d"), L);
                    }
                }
            }
            FString Suffix = FString::Join(Placeholders, TEXT("_"));
            BlockSuffixes.Add(FIntPoint(bx, by), Suffix);
            if (!OrderedSuffixes.Contains(Suffix))
            {
                OrderedSuffixes.Add(Suffix);
            }
            if (DEBUG)
            {
                FString LayersStr;
                for (int32 l : LayersInBlock)
                {
                    LayersStr += FString::Printf(TEXT("%d "), l);
                }
                //UE_LOG(LogTemp, Log, TEXT("Block (%d,%d) [%d:%d, %d:%d] layers: %s -> Suffix: %s"),
                //    bx, by, x0, x1, y0, y1, *LayersStr, *Suffix);
            }
        }
    }

    OrderedSuffixes.Sort([](const FString& A, const FString& B) {
        TArray<FString> TokensA, TokensB;
        A.ParseIntoArray(TokensA, TEXT("_"));
        B.ParseIntoArray(TokensB, TEXT("_"));

        // Compare each position
        for (int32 i = 0; i < FMath::Min(TokensA.Num(), TokensB.Num()); i++)
        {
            const FString& TokenA = TokensA[i];
            const FString& TokenB = TokensB[i];

            // If one is "N" and other is "L", "L" comes first
            if (TokenA.StartsWith("L") && TokenB == "N") return true;
            if (TokenB.StartsWith("L") && TokenA == "N") return false;

            // If both are "L", compare numbers
            if (TokenA.StartsWith("L") && TokenB.StartsWith("L"))
            {
                int32 NumA = FCString::Atoi(*TokenA.RightChop(1));
                int32 NumB = FCString::Atoi(*TokenB.RightChop(1));
                if (NumA != NumB) return NumA < NumB;
            }
        }

        // If we get here, use length as final tiebreaker
        return TokensA.Num() > TokensB.Num();
        });


    // Generate an RVMat for each unique suffix in the order they were first encountered.
    TMap<FString, int32> SuffixToIndex;
    for (const FString& Suffix : OrderedSuffixes)
    {
        // Use TileCol and TileRow as passed in.
        FString RVMatName = FString::Printf(TEXT("P_%03d-%03d_%s.rvmat"), TileCol, TileRow, *Suffix);
        FString RelativePath = FPaths::Combine(BaseRelativeLayerPath, RVMatName);
        RelativePath.ReplaceInline(TEXT("/"), TEXT("\\"));
        FString AbsolutePath = FPaths::Combine(BaseAbsoluteLayerPath, RVMatName);

        if (DEBUG)
        {
            UE_LOG(LogTemp, Log, TEXT("Generating RVMat for suffix: %s"), *Suffix);
        }

        FString DynamicStages;
        TArray<FString> Tokens;
        Suffix.ParseIntoArray(Tokens, TEXT("_"), true);
        for (int32 i = 0; i < Tokens.Num(); i++)
        {
            int32 StageIdxNormal = 3 + 2 * i;
            int32 StageIdxDiffuse = StageIdxNormal + 1;
            FString Token = Tokens[i];
            if (Token == TEXT("N"))
            {
                DynamicStages += FString::Printf(TEXT("class Stage%d\n{\n\ttexture=\"\";\n\ttexGen=1;\n};\n"), StageIdxNormal);
                DynamicStages += FString::Printf(TEXT("class Stage%d\n{\n\ttexture=\"\";\n\ttexGen=2;\n};\n"), StageIdxDiffuse);
            }
            else
            {
                int32 LNum = FCString::Atoi(*Token.RightChop(1));
                FString DiffuseTex = TEXT("");
                FString NormalTex = TEXT("");
                if (GlobalLayerNames.IsValidIndex(LNum))
                {
                    FString LayerName = GlobalLayerNames[LNum];
                    if (LayersInfo.Contains(LayerName))
                    {
                        DiffuseTex = LayersInfo[LayerName].Texture;
                        if (DiffuseTex.EndsWith(TEXT("_ca.paa")))
                        {
                            NormalTex = DiffuseTex;
                            NormalTex.RemoveFromEnd(TEXT("_ca.paa"));
                            NormalTex += TEXT("_nopx.paa");
                        }
                    }
                }
                DynamicStages += FString::Printf(TEXT("class Stage%d\n{\n\ttexture=\"%s\";\n\ttexGen=1;\n};\n"), StageIdxNormal, *NormalTex);
                DynamicStages += FString::Printf(TEXT("class Stage%d\n{\n\ttexture=\"%s\";\n\ttexGen=2;\n};\n"), StageIdxDiffuse, *DiffuseTex);
            }
        }
        FString RVMatContent = BaseTemplate;
        RVMatContent = RVMatContent.Replace(TEXT("{dynamic_stages}"), *DynamicStages)
            .Replace(TEXT("{base_rel}"), *BaseRelativeLayerPath)
            .Replace(TEXT("{tile_col:03d}"), *FString::Printf(TEXT("%03d"), TileCol))
            .Replace(TEXT("{tile_row:03d}"), *FString::Printf(TEXT("%03d"), TileRow))
            .Replace(TEXT("{tile_texgen_pos_x}"), *FString::Printf(TEXT("%.3f"), texgenPosX))
            .Replace(TEXT("{tile_texgen_pos_y}"), *FString::Printf(TEXT("%.3f"), texgenPosY));

        // TODO: CURRENTLY WE ARE SKIPPING CREATING TILE RVMATS IF THEY ALREADY EXIST. WHY?

        if (!FPaths::FileExists(AbsolutePath))
        {
            IFileManager::Get().MakeDirectory(*BaseAbsoluteLayerPath, true);
            FFileHelper::SaveStringToFile(RVMatContent, *AbsolutePath);
        }
        int32 FoundIndex = GlobalMaterials.IndexOfByKey(RelativePath);
        if (FoundIndex == INDEX_NONE)
        {
            FoundIndex = GlobalMaterials.Num();
            GlobalMaterials.Add(RelativePath);
        }
        SuffixToIndex.Add(Suffix, FoundIndex);
    }

    for (int32 by = 0; by < YBounds.Num(); by++) {
        for (int32 bx = 0; bx < XBounds.Num(); bx++) {
            FString Suffix = BlockSuffixes.FindChecked(FIntPoint(bx, by));

            //UE_LOG(LogTemp, Warning, TEXT("Registering block - col: %d, row: %d, blockKey: %d"),
            //    TileCol, TileRow, bx * 1000 + by);

            // Store using NON-inverted row (this is the key change)
            FIntVector MappingKey(TileCol, TileRow, bx * 1000 + by);

            int32 MatIndex = SuffixToIndex.Contains(Suffix) ? SuffixToIndex[Suffix] : 0;
            GlobalBlockMapping.Add(MappingKey, MatIndex);
        }
    }
}




//---------------------------------------------------------------------
// Create splat tiles (with padding) and process each tile.
//---------------------------------------------------------------------
void FMaterialGenerator::CreateSplatTilesIncludingPadding(
    int32 ImageSize, int32 TileSize, int32 TileCount,
    int32 FirstTileOffset, int32 TileStep, int32 BlockSize)
{
    // 1) rebuild flat map
    if (!BuildLayerIndexMap(ImageSize)) return;

    const int32 Pad = 64;
    auto XBounds = GetDynamicBlockBounds(TileSize, Pad, BlockSize);
    auto YBounds = XBounds;

    // Ensure output dir
    IFileManager::Get().MakeDirectory(*BaseAbsoluteLayerPath, true);

    // --- PASS 1: registration (serial!) ---
    for (int32 Row = 0; Row < TileCount; ++Row)
    {
        for (int32 Col = 0; Col < TileCount; ++Col)
        {
            int32 X0 = FirstTileOffset + Col * TileStep;
            int32 Y0 = FirstTileOffset + Row * TileStep;

            // extract just like before
            TArray<TArray<uint8>> TileData;
            ExtractTileLayerIndices(X0, Y0, TileSize, TileData);

            // register
            TArray<int32> TileOrder;
            GetTileLayerOrder(TileData, TileOrder);
            ProcessTileAndRegisterBlocks(TileData, Col, Row, TileOrder, BlockSize, Pad);
        }
    }

    // --- PASS 2: write out PNGs in parallel ---
    const int32 Total = TileCount * TileCount;
    ParallelFor(Total, [&](int32 Idx)
    {
        int32 Row = Idx / TileCount;
        int32 Col = Idx % TileCount;
        int32 X0 = FirstTileOffset + Col * TileStep;
        int32 Y0 = FirstTileOffset + Row * TileStep;

        // extract again
        TArray<TArray<uint8>> TileData;
        ExtractTileLayerIndices(X0, Y0, TileSize, TileData);

        // save image
        TArray<FColor> SplatImage;
        TileIndicesToSplatRGBA(TileData, SplatImage);
        FString Name = FString::Printf(TEXT("M_%03d_%03d_lca.png"), Col, Row);
        FString Path = FPaths::Combine(BaseAbsoluteLayerPath, Name);
        SaveTileImage(Path, SplatImage, TileSize, TileSize);
    });

    // final conversion
    ConvertAllPNGsToPAA(BaseAbsoluteLayerPath, TEXT("M_"));
}



//---------------------------------------------------------------------
// Helper: Get the grid index for a global coordinate.
//---------------------------------------------------------------------
int32 FMaterialGenerator::GetGridIndex(int32 GlobalCoord, int32 TileStep, int32 TileCount, int32 FullValidSize)
{
    // Build boundaries: for i in 0..tile_count-2, boundary = TileStep*(i+1), and last boundary = full_valid_size.
    for (int32 i = 0; i < TileCount - 1; i++)
    {
        int32 Boundary = TileStep * (i + 1);
        if (GlobalCoord < Boundary)
        {
            return i;
        }
    }
    return TileCount - 1;
}



///////////////////////////////////////////////
/////////// END LAYER SHIT////////////////////
/////////////////////////////////////////////


// EditableWrp

EditableWrp::EditableWrp(FBinaryReader& Input)
{
    Read(Input);
}

void EditableWrp::Read(FBinaryReader& Input)
{
    if (Input.ReadAscii(4) != "8WVR")
    {
        UE_LOG(LogTemp, Error, TEXT("8WVR file does not start with correct file signature"));
        return;
    }

    ReadContent(Input);
}

void EditableWrp::ReadContent(FBinaryReader& Input)
{
    UE_LOG(LogTemp, Log, TEXT("STARTING TO READ CONTENT"));
    LandRangeX = Input.ReadInt32();
    LandRangeY = Input.ReadInt32();
    TerrainRangeX = Input.ReadInt32();
    TerrainRangeY = Input.ReadInt32();
    CellSize = Input.ReadFloat();

    UE_LOG(LogTemp, Log, TEXT("STARTING TO READ ELEVATION"));
    std::vector<float> ElevationVector = Input.ReadFloats(TerrainRangeX * TerrainRangeY);
    Elevation.Append(ElevationVector.data(), ElevationVector.size());


    UE_LOG(LogTemp, Log, TEXT("STARTING TO READ MATERIAL INDICES"));
    std::vector<uint16> MaterialIndexVector = Input.ReadUshorts(LandRangeX * LandRangeY);
    MaterialIndex.Append(MaterialIndexVector.data(), MaterialIndexVector.size());

    // Read the number of materials
    int32 numMaterials = Input.ReadInt32();
    UE_LOG(LogTemp, Log, TEXT("NumMaterials: %d"), numMaterials);
    MatNames.Empty(numMaterials);  // Prepare for reading materials

    // Read materials
    for (int32 i = 0; i < numMaterials; i++)
    {
        FString MatName;
        int32 len;
        do
        {
            len = Input.ReadInt32();
            if (len != 0)
            {
                MatName = FString(Input.ReadAscii(len).c_str());
            }
        } while (len != 0);

        // Log the material name for debugging purposes
        //UE_LOG(LogTemp, Log, TEXT("MaterialName[%d]: %s"), i, *MatName);

        // Store the material name
        MatNames.Add(MatName);
    }

    // Log that materials have been read
    UE_LOG(LogTemp, Log, TEXT("Finished reading materials."));
    //UE_LOG(LogTemp, Log, TEXT("Finished reading materials at position: %lld"), Input.GetPosition());

    // Continue reading objects if applicable
    while (!Input.HasReachedEnd())
    {
        Objects.Add(EditableWrpObject(Input));
    }
}


void ExportLandscapeHeightmap(UWorld* World, const FString& Filename)
{
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid World pointer"));
        return;
    }

    ALandscape* Landscape = nullptr;
    for (TActorIterator<ALandscape> It(World); It; ++It)
    {
        Landscape = *It;
        break; // Get the first landscape found
    }

    if (!Landscape)
    {
        UE_LOG(LogTemp, Error, TEXT("No Landscape found in the world"));
        return;
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get LandscapeInfo"));
        return;
    }

    // Ensure the filename has the correct extension
    FString FullFilename = Filename;

    // Call the ExportHeightmap function
    LandscapeInfo->ExportHeightmap(FullFilename);
    UE_LOG(LogTemp, Log, TEXT("Landscape heightmap exported to: %s"), *FullFilename);

    // Get the landscape dimensions
    int32 MinX, MinY, MaxX, MaxY;
    if (LandscapeInfo->GetLandscapeExtent(MinX, MinY, MaxX, MaxY))
    {
        int32 Width = MaxX - MinX + 1;
        int32 Height = MaxY - MinY + 1;

        // Call ReadAndPrintHeightmap function
        ReadAndPrintHeightmap(FullFilename, Width, Height);
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get Landscape extent"));
    }
}

void ReadAndPrintHeightmap(const FString& InputFilePath, int32 Width, int32 Height)
{
    /*
    //////// ASSET ZOO - 1024x1024 ////////

    int32 FullWidth = 1135;
    int32 FullHeight = 1135;
    int32 DesiredWidth = 1024;
    int32 DesiredHeight = 1024;
    */

    int32 FullWidth = 4159;
    int32 FullHeight = 4159;
    int32 DesiredWidth = 4096;
    int32 DesiredHeight = 4096;

    TArray<uint8> RawData;
    if (!FFileHelper::LoadFileToArray(RawData, *InputFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load heightmap file: %s"), *InputFilePath);
        return;
    }

        // bytes per height sample
    const int32 BytesPerSample = 2;

    // total number of height samples in the file
    const int32 NumSamples = RawData.Num() / BytesPerSample;

    // infer the side length (assuming square)
    const int32 DetectedSize = FMath::RoundToInt(FMath::Sqrt((float)NumSamples));

    // log what we found
    UE_LOG(LogTemp, Log,
        TEXT("Detected heightmap size from raw data: %dx%d"),
        DetectedSize, DetectedSize);

    // Check if the file size matches the expected size
    if (RawData.Num() != FullWidth * FullHeight * 2) // 2 bytes per height value
    {
        UE_LOG(LogTemp, Error, TEXT("Heightmap file size doesn't match expected size"));
        return;
    }

    TArray<float> HeightValuesInMeters;
    HeightValuesInMeters.SetNum(DesiredWidth * DesiredHeight);

    // Convert height values to meters, reading only the desired area
    const float BaseValue = 32768.0f;
    const float HeightScaleFactor = 1.0f / 128.0f; // 128 units = 1 meter

    for (int32 Y = 0; Y < DesiredHeight; ++Y)
    {
        for (int32 X = 0; X < DesiredWidth; ++X)
        {
            // Calculate the index in the full heightmap data
            int32 FullIndex = (Y * FullWidth + X) * 2; // 2 bytes per height value

            // Read the height value
            uint16 HeightValue = (RawData[FullIndex + 1] << 8) | RawData[FullIndex];

            // Convert to meters
            float HeightDifference = static_cast<float>(HeightValue) - BaseValue;
            HeightValuesInMeters[Y * DesiredWidth + X] = HeightDifference * HeightScaleFactor;
        }
    }

    // Print out the first 10x10 height values in meters
    //UE_LOG(LogTemp, Log, TEXT("First 10x10 heightmap values (in meters):"));
    for (int32 Y = 0; Y < FMath::Min(10, DesiredHeight); ++Y)
    {
        FString RowString;
        for (int32 X = 0; X < FMath::Min(10, DesiredWidth); ++X)
        {
            int32 Index = Y * DesiredWidth + X;
            RowString += FString::Printf(TEXT("%8.3f "), HeightValuesInMeters[Index]);
        }
        //UE_LOG(LogTemp, Log, TEXT("%s"), *RowString);
    }

    // Calculate and print some basic statistics for the height values in meters
    float MinHeight = HeightValuesInMeters[0];
    float MaxHeight = HeightValuesInMeters[0];
    double AverageHeight = 0;

    for (float Height : HeightValuesInMeters)
    {
        MinHeight = FMath::Min(MinHeight, Height);
        MaxHeight = FMath::Max(MaxHeight, Height);
        AverageHeight += Height;
    }
    AverageHeight /= HeightValuesInMeters.Num();

    //UE_LOG(LogTemp, Log, TEXT("Heightmap Statistics (in meters):"));
    //UE_LOG(LogTemp, Log, TEXT("Min Height: %.3f m"), MinHeight);
    //UE_LOG(LogTemp, Log, TEXT("Max Height: %.3f m"), MaxHeight);
    //UE_LOG(LogTemp, Log, TEXT("Average Height: %.3f m"), AverageHeight);
}

TArray<float> ReadHeightmap(const FString& InputFilePath)
{
    TArray<float> ElevationValues;

    /*
    // ASSET ZOO - 1024x1024
    int32 FullWidth = 1135;
    int32 FullHeight = 1135;
    int32 DesiredWidth = 1024;
    int32 DesiredHeight = 1024;
    */

    int32 FullWidth = 4159;
    int32 FullHeight = 4159;
    int32 DesiredWidth = 4096;
    int32 DesiredHeight = 4096;


    int32 StartX = 0;
    int32 StartY = 0;

    TArray<uint8> RawData;
    if (!FFileHelper::LoadFileToArray(RawData, *InputFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to load heightmap file: %s"), *InputFilePath);
        return ElevationValues; // Return an empty array
    }

        // bytes per height sample
        const int32 BytesPerSample = 2;

        // total number of height samples in the file
        const int32 NumSamples = RawData.Num() / BytesPerSample;

        // infer the side length (assuming square)
        const int32 DetectedSize = FMath::RoundToInt(FMath::Sqrt((float)NumSamples));

        // log what we found
        UE_LOG(LogTemp, Log,
            TEXT("Detected heightmap size from raw data: %dx%d"),
            DetectedSize, DetectedSize);

    // Check if the file size matches the expected size
    if (RawData.Num() != FullWidth * FullHeight * 2) // 2 bytes per height value
    {
        UE_LOG(LogTemp, Error, TEXT("Heightmap file size doesn't match expected size"));
        return ElevationValues; // Return an empty array
    }

    ElevationValues.SetNum(DesiredWidth * DesiredHeight);

    // Convert height values to meters, reading only the desired area
    const float BaseValue = 32768.0f;
    const float HeightScaleFactor = 1.0f / 128.0f; // 128 units = 1 meter

    for (int32 Y = 0; Y < DesiredHeight; ++Y)
    {
        for (int32 X = 0; X < DesiredWidth; ++X)
        {
            // Original coordinates
            int32 TempX = StartX + X;
            int32 TempY = StartY + Y;

            // Flip horizontally (before rotation)
            TempX = FullWidth - TempX - 1;

            // Rotate 90 degrees clockwise
            int32 FullX = TempY;
            int32 FullY = TempX;

            // Flip vertically (after rotation)
            FullY = FullHeight - FullY - 1;

            // Check bounds
            if (FullX < 0 || FullX >= FullWidth || FullY < 0 || FullY >= FullHeight)
            {
                ElevationValues[Y * DesiredWidth + X] = 0.0f;
                continue;
            }

            int32 FullIndex = (FullY * FullWidth + FullX) * 2; // 2 bytes per height value

            // Read the height value (Little Endian)
            uint16 HeightValue = (RawData[FullIndex + 1] << 8) | RawData[FullIndex];

            // Convert to meters and store in the array
            float HeightDifference = static_cast<float>(HeightValue) - BaseValue;
            ElevationValues[Y * DesiredWidth + X] = HeightDifference * HeightScaleFactor;
        }
    }

    return ElevationValues;
}



// Utility functions
FDecomposedTransform DecomposeMatrix(const FMatrix& InMatrix)
{
    FDecomposedTransform Result;

    // Extract position (translation)
    Result.Location = FVector(InMatrix.M[0][3] + 200000.0f, InMatrix.M[1][3], InMatrix.M[2][3]);

    // Extract scale factors
    Result.Scale.X = FVector(InMatrix.M[0][0], InMatrix.M[1][0], InMatrix.M[2][0]).Size();
    Result.Scale.Y = FVector(InMatrix.M[0][1], InMatrix.M[1][1], InMatrix.M[2][1]).Size();
    Result.Scale.Z = FVector(InMatrix.M[0][2], InMatrix.M[1][2], InMatrix.M[2][2]).Size();

    // Normalize the rotation matrix to remove scale
    float M11 = InMatrix.M[0][0] / Result.Scale.X;
    float M12 = InMatrix.M[0][1] / Result.Scale.Y;
    float M13 = InMatrix.M[0][2] / Result.Scale.Z;
    float M21 = InMatrix.M[1][0] / Result.Scale.X;
    float M22 = InMatrix.M[1][1] / Result.Scale.Y;
    float M23 = InMatrix.M[1][2] / Result.Scale.Z;
    float M31 = InMatrix.M[2][0] / Result.Scale.X;
    float M32 = InMatrix.M[2][1] / Result.Scale.Y;
    float M33 = InMatrix.M[2][2] / Result.Scale.Z;

    // Extract Euler angles using XZY rotation order
    // Pitch (rotation around X-axis)
    float PitchRad = FMath::Asin(-M32);
    float PitchDeg = FMath::RadiansToDegrees(PitchRad);

    // Yaw (rotation around Y-axis)
    float YawRad = FMath::Atan2(-M31, M33);  // Negate M31 for correct sign
    float YawDeg = FMath::RadiansToDegrees(YawRad);

    // Roll (rotation around Z-axis)
    float RollRad = FMath::Atan2(M12, M22);
    float RollDeg = FMath::RadiansToDegrees(RollRad);

    // Adjust angles to positive range if necessary
    if (PitchDeg < 0) PitchDeg += 360.0f;
    if (YawDeg < 0) YawDeg += 360.0f;
    if (RollDeg < 0) RollDeg += 360.0f;

    // Assign the rotation values
    Result.Rotation.Pitch = PitchDeg;
    Result.Rotation.Yaw = YawDeg;
    Result.Rotation.Roll = RollDeg;

    //UE_LOG(LogTemp, Log, TEXT("Decomposed Transform:%f, %f, %f"), Result.Location.X, Result.Location.Y, Result.Location.Z);

    return Result;
}


FMatrix ComposeMatrix(const FDecomposedTransform& InTransform)
{
    FDecomposedTransform Transform = InTransform;

    // Convert rotations from degrees to radians and negate Pitch and Roll
    float PitchRad = FMath::DegreesToRadians(-Transform.Rotation.Pitch);  // Negated X-axis rotation
    float YawRad = FMath::DegreesToRadians(Transform.Rotation.Yaw - 90.0f);    // Y-axis rotation
    float RollRad = FMath::DegreesToRadians(-Transform.Rotation.Roll);   // Negated Z-axis rotation

    // Create the rotation matrices for each axis
    FMatrix RotX, RotY, RotZ;
    RotX.SetIdentity();
    RotY.SetIdentity();
    RotZ.SetIdentity();

    // X-axis rotation (Pitch)
    RotX.M[1][1] = FMath::Cos(PitchRad);
    RotX.M[1][2] = -FMath::Sin(PitchRad);
    RotX.M[2][1] = FMath::Sin(PitchRad);
    RotX.M[2][2] = FMath::Cos(PitchRad);

    // Y-axis rotation (Yaw)
    RotY.M[0][0] = FMath::Cos(YawRad);
    RotY.M[0][2] = FMath::Sin(YawRad);
    RotY.M[2][0] = -FMath::Sin(YawRad);
    RotY.M[2][2] = FMath::Cos(YawRad);

    // Z-axis rotation (Roll)
    RotZ.M[0][0] = FMath::Cos(RollRad);
    RotZ.M[0][1] = -FMath::Sin(RollRad);
    RotZ.M[1][0] = FMath::Sin(RollRad);
    RotZ.M[1][1] = FMath::Cos(RollRad);

    // Combine rotations in DirectX order: Pitch(X) * Roll(Z) * Yaw(Y)
    FMatrix FinalRotationMatrix = RotX * RotZ * RotY;

    // Create the result matrix
    FMatrix Result;
    Result.SetIdentity();

    // Apply scale and rotation
    for (int i = 0; i < 3; ++i)
    {
        for (int j = 0; j < 3; ++j)
        {
            Result.M[i][j] = FinalRotationMatrix.M[i][j] * Transform.Scale[j];
        }
    }

    // Apply translation (keeping your existing coordinate conversion)
    Result.M[0][3] = Transform.Location.X - 200000.0f;
    Result.M[1][3] = Transform.Location.Z;
    Result.M[2][3] = Transform.Location.Y;

    // Set last row
    Result.M[3][0] = 0.0f;
    Result.M[3][1] = 0.0f;
    Result.M[3][2] = 0.0f;
    Result.M[3][3] = 1.0f;

    return Result;
}



void PrintDecomposedMatrix(const FMatrix& Matrix)
{
    // Create a transform from the matrix
    FTransform TransformDecomposed(Matrix);

    // Extract location, rotation, and scale
    FVector Location = TransformDecomposed.GetLocation();
    FRotator Rotation = TransformDecomposed.Rotator();
    FVector Scale = TransformDecomposed.GetScale3D();

    // Log the decomposed transform
    //UE_LOG(LogTemp, Log, TEXT("Decomposed Transform:"));
    //UE_LOG(LogTemp, Log, TEXT("Location: X=%f Y=%f Z=%f"), Location.X, Location.Y, Location.Z);
    //UE_LOG(LogTemp, Log, TEXT("Rotation: Pitch=%f Yaw=%f Roll=%f"), Rotation.Pitch, Rotation.Yaw, Rotation.Roll);
    //UE_LOG(LogTemp, Log, TEXT("Scale: X=%f Y=%f Z=%f"), Scale.X, Scale.Y, Scale.Z);
}


FString FDecomposedTransform::ToString() const
{
    return FString::Printf(TEXT("Decomposed Transform:\n"
        "Location: X=%.3f Y=%.3f Z=%.3f\n"
        "Rotation: Pitch=%.3f Yaw=%.3f Roll=%.3f\n"
        "Scale: X=%.3f Y=%.3f Z=%.3f"),
        Location.X, Location.Y, Location.Z,
        Rotation.Pitch, Rotation.Yaw, Rotation.Roll,
        Scale.X, Scale.Y, Scale.Z);
}

// EditableWrp.cpp

void EditableWrp::Write(FBinaryWriter& Output) const
{
    // Write the signature
    Output.WriteAscii("8WVR");

    // Write LandRangeX, LandRangeY, TerrainRangeX, TerrainRangeY, CellSize
    Output.WriteInt32(LandRangeX);
    Output.WriteInt32(LandRangeY);
    Output.WriteInt32(TerrainRangeX);
    Output.WriteInt32(TerrainRangeY);
    Output.WriteFloat(CellSize);

    // Write Elevation data
    Output.WriteFloatArray(Elevation);

    // Write MaterialIndex data
    Output.WriteUInt16Array(MaterialIndex);

    // Write the number of materials
    Output.WriteInt32(MatNames.Num());

    // Write materials
    for (const FString& MatName : MatNames)
    {
        // Start of ConcatStrings for this material

        if (!MatName.IsEmpty())
        {
            // Convert material name to ASCII
            std::string MatNameAscii = TCHAR_TO_UTF8(*MatName);
            int32 len = MatNameAscii.length();

            // Write the length of the material name
            Output.WriteInt32(len);

            // Write the material name
            Output.WriteAscii(MatNameAscii);
        }
        // If MatName is empty, we skip writing the name but still need to write the terminator

        // Write zero length to indicate end of ConcatStrings for this material
        Output.WriteInt32(0);
    }

    // Log that materials have been written
    UE_LOG(LogTemp, Log, TEXT("Finished writing materials"));

    // Write Objects
    for (const EditableWrpObject& Object : Objects)
    {
        Object.Write(Output);
    }

    UE_LOG(LogTemp, Log, TEXT("Finished writing WRP file"));
}



/**
 * Gathers all AP3DActor instances in the current level and converts them to EditableWrpObject instances.
 *
 * @param World The current UWorld context.
 * @param OutObjects The array to populate with EditableWrpObject instances.
 */
void GatherP3DActors(UWorld* World, TArray<EditableWrpObject>& OutObjects)
{
    if (!World)
    {
        UE_LOG(LogTemp, Warning, TEXT("No valid world context found."));
        return;
    }

    // Array to hold all found P3DActor instances
    TArray<AActor*> FoundActors;

    // Retrieve all actors of class AP3DActor in the current level
    UGameplayStatics::GetAllActorsOfClass(World, AP3DActor::StaticClass(), FoundActors);

    // Check if any P3DActor instances were found
    if (FoundActors.Num() == 0)
    {
        UE_LOG(LogTemp, Warning, TEXT("No P3DActor instances found in the current level."));
        // Don't return here as we'll also check for PCG instances
    }
    else
    {
        //UE_LOG(LogTemp, Log, TEXT("Found %d P3DActor instances."), FoundActors.Num());
    }

    int32 CurrentObjectID = 0;  // Start ObjectID at 0

    // PART 1: Process P3DActor instances
    for (AActor* Actor : FoundActors)
    {
        AP3DActor* P3DActor = Cast<AP3DActor>(Actor);
        if (!P3DActor)
        {
            // This should not happen as we've already filtered by AP3DActor::StaticClass()
            //UE_LOG(LogTemp, Warning, TEXT("Actor %s could not be cast to AP3DActor."), *Actor->GetName());
            continue;
        }

        // Check if this P3DActor is a child of another P3DActor
        AActor* ParentActor = P3DActor->GetAttachParentActor();
        if (ParentActor && ParentActor->IsA(AP3DActor::StaticClass()))
        {
            // Skip this actor as it's a child of another P3DActor
            //UE_LOG(LogTemp, Log, TEXT("Skipping P3DActor %s as it's a child of P3DActor %s"),
            //    *P3DActor->GetName(),
            //    *ParentActor->GetName());
            continue;
        }

        FString P3DPath = P3DActor->P3DPath;
        bool bAutoCenter = P3DActor->bAutoCenter;
        FVector UnrealPivotLocation = P3DActor->GetActorLocation();
        FRotator Rotation = P3DActor->ActorToWorld().GetRotation().Rotator();
        FVector Scale = P3DActor->GetActorScale3D();

        // Log the properties for debugging
        //UE_LOG(LogTemp, Log, TEXT("Processing P3DActor: %s"), *P3DActor->GetName());
        //UE_LOG(LogTemp, Log, TEXT(" - P3DPath: %s"), *P3DPath);
        //UE_LOG(LogTemp, Log, TEXT(" - Original Location: X=%f, Y=%f, Z=%f"),
        //    UnrealPivotLocation.X, UnrealPivotLocation.Y, UnrealPivotLocation.Z);
        //UE_LOG(LogTemp, Log, TEXT(" - Rotation: Pitch=%f, Yaw=%f, Roll=%f"),
        //    Rotation.Pitch, Rotation.Yaw, Rotation.Roll);
        //UE_LOG(LogTemp, Log, TEXT(" - Scale: X=%f, Y=%f, Z=%f"),
        //    Scale.X, Scale.Y, Scale.Z);

        FVector AdjustedLocationUnconverted;

        if (bAutoCenter)
        {
            // Get the actor's bounding box
            FBox ActorBounds = Actor->GetComponentsBoundingBox(true); // Include all components

            // Calculate the center of the bounding box
            FVector Center = ActorBounds.GetCenter();

            //UE_LOG(LogTemp, Log, TEXT(" - AutoCenter Enabled: Actor Center at X=%f, Y=%f, Z=%f"),
            //    Center.X, Center.Y, Center.Z);

            // Set AdjustedLocationUnconverted to center
            AdjustedLocationUnconverted = Center;
        }
        else
        {
            // Set AdjustedLocationUnconverted to pivot
            AdjustedLocationUnconverted = UnrealPivotLocation;
        }

        // Convert to DayZ coordinates
        FVector Location = FLevelEditorToolbar::ConvertUnrealToDayZCoords(AdjustedLocationUnconverted);
        FVector Location2 = FLevelEditorToolbar::ConvertUnrealToDayZCoords(UnrealPivotLocation);

        // Decompose the transform
        FDecomposedTransform DecomposedTransform;

        // Apply your custom mapping here
        DecomposedTransform.Location.X = Location2.X + 200000.0f;
        DecomposedTransform.Location.Y = Location2.Y;
        DecomposedTransform.Location.Z = Location2.Z;

        DecomposedTransform.Rotation.Pitch = Rotation.Pitch;
        DecomposedTransform.Rotation.Yaw = Rotation.Yaw;
        DecomposedTransform.Rotation.Roll = Rotation.Roll;

        DecomposedTransform.Scale.X = Scale.X;
        DecomposedTransform.Scale.Y = Scale.Y;
        DecomposedTransform.Scale.Z = Scale.Z;

        // Compose the matrix back using your custom function
        FMatrix ComposedMatrix = ComposeMatrix(DecomposedTransform);

        // Optionally, log the decomposed matrix for debugging
        DecomposeMatrix(ComposedMatrix);

        // Create the EditableWrpObject
        EditableWrpObject WrpObject;
        WrpObject.ObjectID = CurrentObjectID++;
        WrpObject.Model = P3DPath.Replace(TEXT("/"), TEXT("\\"));
        WrpObject.Transform = ComposedMatrix;

        // Add the object to the output array
        OutObjects.Add(WrpObject);
    }

    // You can either look for the world actor by class or by name/tag. Here we assume a single world actor of class APCGWorldActor.
    // Get the editor world

// Find the first PCGWorldActor in the scene
TArray<AActor*> WorldActors;
UGameplayStatics::GetAllActorsOfClass(World, APCGWorldActor::StaticClass(), WorldActors);

if (WorldActors.Num() == 0)
{
    UE_LOG(LogTemp, Warning, TEXT("No PCGWorldActor found in the level."));
    return;
}

APCGWorldActor* PCGWorldActor = Cast<APCGWorldActor>(WorldActors[0]);
if (!PCGWorldActor)
{
    UE_LOG(LogTemp, Warning, TEXT("First actor is not a PCGWorldActor."));
    return;
}

UE_LOG(LogTemp, Log, TEXT("Using PCGWorldActor: %s"), *PCGWorldActor->GetName());

// Find all PCGPartitionActor in the world
TArray<AActor*> PartitionActors;
UGameplayStatics::GetAllActorsOfClass(World, APCGPartitionActor::StaticClass(), PartitionActors);

if (PartitionActors.Num() == 0)
{
    UE_LOG(LogTemp, Warning, TEXT("No PCGPartitionActor found in the level."));
}
else
{
    for (AActor* Actor : PartitionActors)
    {
        APCGPartitionActor* PartitionActor = Cast<APCGPartitionActor>(Actor);
        if (!PartitionActor)
        {
            continue;
        }

        UE_LOG(LogTemp, Log, TEXT("Processing PCGPartitionActor: %s"), *PartitionActor->GetName());

        // Gather all instanced mesh components (HISM and ISM)
        TArray<UInstancedStaticMeshComponent*> MeshComponents;
        PartitionActor->GetComponents<UInstancedStaticMeshComponent>(MeshComponents);

        if (MeshComponents.Num() == 0)
        {
            // Empty partition, skip
            continue;
        }

        // Your existing mesh‐to‐P3D logic
        for (UInstancedStaticMeshComponent* ISM : MeshComponents)
        {
            if (!ISM)
            {
                continue;
            }

            UStaticMesh* StaticMesh = ISM->GetStaticMesh();
            if (!StaticMesh)
            {
                UE_LOG(LogTemp, Warning, TEXT("ISM component %s has no mesh."), *ISM->GetName());
                continue;
            }

            // --- BEGIN mesh→P3D lookup ---
            FString MeshName        = StaticMesh->GetName();
            FString FolderPath, AssetName;
            FString PackagePath     = StaticMesh->GetOutermost()->GetName();
            if (PackagePath.Split(TEXT("/"), &FolderPath, &AssetName, ESearchCase::IgnoreCase, ESearchDir::FromEnd))
            {
                FolderPath += TEXT("/");
            }

            FString PfAssetPath     = FolderPath + FString::Printf(TEXT("PF_%s"), *MeshName);
            UObject* PfAsset        = LoadObject<UObject>(nullptr, *PfAssetPath);
            if (!PfAsset)
            {
                continue;
            }

            // Extract P3D info
            FString     P3DPath;
            bool        bAutoCenter = false;
            AP3DActor*  P3DTemplate = nullptr;

            if (AP3DActor* Direct = Cast<AP3DActor>(PfAsset))
            {
                P3DPath      = Direct->P3DPath;
                bAutoCenter  = Direct->bAutoCenter;
                P3DTemplate  = Direct;
            }
            else if (UBlueprint* BP = Cast<UBlueprint>(PfAsset);
                     BP && BP->GeneratedClass->IsChildOf(AP3DActor::StaticClass()))
            {
                if (AP3DActor* DefaultObj = Cast<AP3DActor>(BP->GeneratedClass->GetDefaultObject()))
                {
                    P3DPath      = DefaultObj->P3DPath;
                    bAutoCenter  = DefaultObj->bAutoCenter;
                    P3DTemplate  = DefaultObj;
                }
            }

            if (P3DPath.IsEmpty() || !P3DTemplate)
            {
                continue;
            }

            // Process instances
            const int32 InstanceCount = ISM->GetInstanceCount();
            for (int32 Index = 0; Index < InstanceCount; ++Index)
            {
                FTransform LocalTransform;
                if (!ISM->GetInstanceTransform(Index, LocalTransform, /*bWorldSpace=*/false))
                {
                    UE_LOG(LogTemp, Warning, TEXT("Failed to get transform for %s[%d]"), *MeshName, Index);
                    continue;
                }

                // World‐space transform
                FTransform WorldTransform = LocalTransform * PartitionActor->GetActorTransform();
                FVector Pivot             = WorldTransform.GetLocation();
                FRotator Rot              = WorldTransform.GetRotation().Rotator();
                FVector Scale             = WorldTransform.GetScale3D();

                FVector AdjustedLoc = Pivot;
                if (bAutoCenter)
                {
                    FBox Bounds = StaticMesh->GetBoundingBox().TransformBy(WorldTransform);
                    AdjustedLoc = Bounds.GetCenter();
                }

                // Convert and compose
                FVector DayZLoc = FLevelEditorToolbar::ConvertUnrealToDayZCoords(AdjustedLoc);
                FDecomposedTransform DT;
                DT.Location = { DayZLoc.X + 200000.0f, DayZLoc.Y, DayZLoc.Z };
                DT.Rotation = { Rot.Pitch, Rot.Yaw, Rot.Roll };
                DT.Scale    = { Scale.X, Scale.Y, Scale.Z };
                FMatrix Mat = ComposeMatrix(DT);

                EditableWrpObject W;
                W.ObjectID  = CurrentObjectID++;
                W.Model     = P3DPath.Replace(TEXT("/"), TEXT("\\"));
                W.Transform = Mat;
                OutObjects.Add(W);
            }
            // --- END mesh→P3D lookup ---
        }
    }
}


}

// EditableWrpObject

EditableWrpObject::EditableWrpObject(FBinaryReader& Input)
{
    // Initialize an identity matrix
    Transform = FMatrix::Identity;

    //UE_LOG(LogTemp, Log, TEXT("Starting to read matrix at position: %lld"), Input.GetPosition());

    // Temporary array to hold the matrix data
    float TempMatrix[4][3];

    // Read the matrix in column-major order from the file
    for (int32 Col = 0; Col < 4; ++Col)
    {
        for (int32 Row = 0; Row < 3; ++Row)
        {
            TempMatrix[Col][Row] = Input.ReadFloat();
        }
    }

    // Map the axes and store in the Transform matrix
    for (int32 Col = 0; Col < 4; ++Col)
    {

        Transform.M[0][Col] = TempMatrix[Col][0]; // X remains X
        Transform.M[1][Col] = TempMatrix[Col][1]; // Z (DayZ) becomes Y (Unreal)
        Transform.M[2][Col] = TempMatrix[Col][2]; // Y (DayZ) becomes Z (Unreal)
    }

    // Set the fourth row for homogenous coordinates
    Transform.M[3][0] = 0.0f;
    Transform.M[3][1] = 0.0f;
    Transform.M[3][2] = 0.0f;
    Transform.M[3][3] = 1.0f;

    DecomposeMatrix(Transform);

    // Read the object ID
    ObjectID = Input.ReadInt32();

    // Read the length of the model name
    int32 NameLength = Input.ReadInt32();

    // Read the model name as an ASCII string
    Model = FString(Input.ReadAscii(NameLength).c_str());

    // Log the object info for debugging
    //UE_LOG(LogTemp, Log, TEXT("ObjectID: %d, ModelName: %s"), ObjectID, *Model);
}


// EditableWrpObject.cpp

void EditableWrpObject::Write(FBinaryWriter& Output) const
{
    // Temporary array to hold the matrix data
    float TempMatrix[4][3];

    // Map the axes from Unreal back to DayZ coordinate system
    for (int32 Col = 0; Col < 4; ++Col)
    {
        TempMatrix[Col][0] = Transform.M[0][Col]; // X remains X
        TempMatrix[Col][1] = Transform.M[1][Col]; // Y remains Y
        TempMatrix[Col][2] = Transform.M[2][Col]; // Z remains Z
    }

    // Write the matrix in column-major order
    for (int32 Col = 0; Col < 4; ++Col)
    {
        for (int32 Row = 0; Row < 3; ++Row)
        {
            Output.WriteFloat(TempMatrix[Col][Row]);
        }
    }

    // Write the object ID
    Output.WriteInt32(ObjectID);

    // Write the length of the model name
    std::string ModelAscii = TCHAR_TO_UTF8(*Model);
    int32 NameLength = ModelAscii.length();
    Output.WriteInt32(NameLength);

    // Write the model name
    if (NameLength > 0)
    {
        Output.WriteAscii(ModelAscii);
    }

    // **Write additional properties if necessary**
    // Example: bAutoCenter
    //Output.WriteBool(bAutoCenter);
}


// Specialized function for exporting terrain heightmap
bool ExportTerrainHeightmapImpl(UWorld* World, const FString& OutputPath)
{
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid World pointer in ExportTerrainHeightmapImpl"));
        return false;
    }

    // Find the landscape in the world
    ALandscape* Landscape = nullptr;
    for (TActorIterator<ALandscape> It(World); It; ++It)
    {
        Landscape = *It;
        break; // Get the first landscape found
    }

    if (!Landscape)
    {
        UE_LOG(LogTemp, Error, TEXT("No Landscape found in the world"));
        return false;
    }

    ULandscapeInfo* LandscapeInfo = Landscape->GetLandscapeInfo();
    if (!LandscapeInfo)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to get LandscapeInfo"));
        return false;
    }

    // Create a temporary heightmap file
    FString TempHeightmapPath = FPaths::Combine(FPaths::ProjectIntermediateDir(), TEXT("TempHeightmap.r16"));

    // Export the heightmap to the temporary file
    LandscapeInfo->ExportHeightmap(TempHeightmapPath);
    UE_LOG(LogTemp, Log, TEXT("Landscape heightmap exported to temporary file: %s"), *TempHeightmapPath);

    // Read the heightmap data
    TArray<float> ElevationData = ReadHeightmap(TempHeightmapPath);
    if (ElevationData.Num() == 0)
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to read heightmap data from temporary file"));
        return false;
    }

    // Read the existing WRP file to get the structure
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *OutputPath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to read existing WRP file: %s"), *OutputPath);
        return false;
    }

    std::vector<uint8_t> Data(FileData.GetData(), FileData.GetData() + FileData.Num());
    FBinaryReader Reader(Data);

    EditableWrp WrpData(Reader);

    // Update only the elevation data
    WrpData.Elevation = ElevationData;

    // Write the updated WRP file
    FBinaryWriter Writer;
    WrpData.Write(Writer);

    // Save to file
    if (Writer.SaveToFile(OutputPath))
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully exported terrain heightmap to: %s"), *OutputPath);
        return true;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain heightmap to: %s"), *OutputPath);
        return false;
    }
}

// Specialized function for exporting terrain layers/materials
bool ExportTerrainLayersImpl(UWorld* World, const FString& OutputPath)
{
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid World pointer in ExportTerrainLayersImpl"));
        return false;
    }

    // Generate material indices
    FMaterialGenerator MaterialGen;
    TArray<uint16> MaterialIndices = MaterialGen.GenerateMaterialIndices();

    // Read the existing WRP file to get the structure
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *OutputPath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to read existing WRP file: %s"), *OutputPath);
        return false;
    }

    std::vector<uint8_t> Data(FileData.GetData(), FileData.GetData() + FileData.Num());
    FBinaryReader Reader(Data);

    EditableWrp WrpData(Reader);

    // Update only the material data
    WrpData.MaterialIndex = MaterialIndices;
    WrpData.MatNames = MaterialGen.GlobalMaterials;

    // Write the updated WRP file
    FBinaryWriter Writer;
    WrpData.Write(Writer);

    // Save to file
    if (Writer.SaveToFile(OutputPath))
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully exported terrain layers to: %s"), *OutputPath);
        return true;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain layers to: %s"), *OutputPath);
        return false;
    }
}

// For UTextureRenderTarget2D
#include "Engine/TextureRenderTarget2D.h"
// For ASceneCapture2D and its component USceneCaptureComponent2D
#include "Engine/SceneCapture2D.h"
#include "Components/SceneCaptureComponent2D.h"
// For UWorld, AActor, TActorIterator
#include "Engine/World.h"
#include "GameFramework/Actor.h"
// For ALandscapeStreamingProxy
#include "LandscapeStreamingProxy.h"
// For FImageUtils
#include "ImageUtils.h"
// For FFileHelper
#include "Misc/FileHelper.h"
// For FPlatformFileManager
#include "HAL/PlatformFileManager.h"
// For FPaths
#include "Misc/Paths.h"
// For FlushRenderingCommands
#include "RHICommandList.h"
// For ULevel::bIsVisible (fallback for landscape visibility)
#include "Engine/Level.h"
// For UObject::MarkAsGarbage
#include "UObject/UObjectGlobals.h"

// Specialized function for exporting terrain objects
bool ExportTerrainObjectsImpl(UWorld* World, const FString& OutputPath)
{
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid World pointer in ExportTerrainObjectsImpl"));
        return false;
    }

    // Read the existing WRP file to get the structure
    TArray<uint8> FileData;
    if (!FFileHelper::LoadFileToArray(FileData, *OutputPath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to read existing WRP file: %s"), *OutputPath);
        return false;
    }

    std::vector<uint8_t> Data(FileData.GetData(), FileData.GetData() + FileData.Num());
    FBinaryReader Reader(Data);

    EditableWrp WrpData(Reader);

    // Gather P3DActor objects from the current level
    TArray<EditableWrpObject> P3DObjects;
    GatherP3DActors(World, P3DObjects);

    // Update only the objects data
    WrpData.Objects = P3DObjects;

    // Write the updated WRP file
    FBinaryWriter Writer;
    WrpData.Write(Writer);

    // Save to file
    if (Writer.SaveToFile(OutputPath))
    {
        UE_LOG(LogTemp, Log, TEXT("Successfully exported terrain objects to: %s"), *OutputPath);
        return true;
    }
    else
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain objects to: %s"), *OutputPath);
        return false;
    }
}

// Original function that now uses the specialized functions
void ReadAndWriteWrp(const FString& InputFilePath, const FString& OutputFilePath)
{
    UWorld* World = GEngine->GetWorldContexts()[0].World();
    if (!World)
    {
        UE_LOG(LogTemp, Error, TEXT("Invalid World pointer in ReadAndWriteWrp"));
        return;
    }

    // Export heightmap
    if (!ExportTerrainHeightmapImpl(World, OutputFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain heightmap"));
        return;
    }

    // Export layers
    if (!ExportTerrainLayersImpl(World, OutputFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain layers"));
        return;
    }

    // Export objects
    if (!ExportTerrainObjectsImpl(World, OutputFilePath))
    {
        UE_LOG(LogTemp, Error, TEXT("Failed to export terrain objects"));
        return;
    }

    UE_LOG(LogTemp, Log, TEXT("Successfully exported all terrain data to: %s"), *OutputFilePath);
}


#include "LandscapeEdit.h"
#include "LandscapeInfo.h"
#include "LandscapeComponent.h"
#include "LandscapeProxy.h"

#include "Misc/FileHelper.h"
#include "HAL/PlatformFilemanager.h"
#include "ImageUtils.h"
#include "LandscapeStreamingProxy.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Assets/TerrainLayers/TerrainLayers.h"

#include "UObject/Package.h"
#include "AssetRegistry/AssetRegistryModule.h"
#include "Modules/ModuleManager.h"

// Helper function to find the TerrainLayers asset in the same directory as the current level
UTerrainLayers* FindTerrainLayersAsset(UWorld* World)
{
    if (!World || !GEditor)
    {
        return nullptr;
    }

    // Get the current level path
    FString LevelPath;
    if (World->PersistentLevel && World->PersistentLevel->GetOutermost())
    {
        LevelPath = World->PersistentLevel->GetOutermost()->GetPathName();
        // Extract the directory path
        FString LevelDirectory = FPaths::GetPath(LevelPath);

        UE_LOG(LogTemp, Log, TEXT("Current level directory: %s"), *LevelDirectory);

        // Look for a TerrainLayers asset named "layers" in the same directory
        FString TerrainLayersPath = FPaths::Combine(LevelDirectory, TEXT("layers"));

        // Try to load the TerrainLayers asset
        UTerrainLayers* TerrainLayersAsset = LoadObject<UTerrainLayers>(nullptr, *TerrainLayersPath);
        if (TerrainLayersAsset)
        {
            UE_LOG(LogTemp, Log, TEXT("Found TerrainLayers asset: %s"), *TerrainLayersPath);
            return TerrainLayersAsset;
        }
        else
        {
            UE_LOG(LogTemp, Warning, TEXT("No TerrainLayers asset named 'layers' found in directory: %s"), *LevelDirectory);

            // If not found by direct path, try looking for any TerrainLayers asset in the directory
            FString WildcardPath = FPaths::Combine(LevelDirectory, TEXT("*"));
            TArray<FString> FoundFiles;
            IFileManager::Get().FindFiles(FoundFiles, *WildcardPath, false, true);

            for (const FString& FileName : FoundFiles)
            {
                if (FileName.EndsWith(TEXT(".uasset")))
                {
                    FString AssetName = FileName.Left(FileName.Len() - 7); // Remove .uasset
                    FString AssetPath = FPaths::Combine(LevelDirectory, AssetName);

                    UE_LOG(LogTemp, Log, TEXT("Trying to load asset: %s"), *AssetPath);
                    UObject* LoadedAsset = LoadObject<UObject>(nullptr, *AssetPath);

                    if (UTerrainLayers* FoundAsset = Cast<UTerrainLayers>(LoadedAsset))
                    {
                        UE_LOG(LogTemp, Log, TEXT("Found TerrainLayers asset: %s"), *AssetPath);
                        return FoundAsset;
                    }
                }
            }
        }
    }

    return nullptr;
}

bool ExportSurfaceMask(UWorld* World, const FString& OutputDir)
{
    if (!World) return false;

    FString FullMapName = World->GetMapName();
    FString WorldShortName = FPackageName::GetShortName(FullMapName);

    // Find the TerrainLayers asset
    UTerrainLayers* TerrainLayersAsset = FindTerrainLayersAsset(World);
    if (!TerrainLayersAsset)
    {
        UE_LOG(LogTemp, Error, TEXT("No TerrainLayers asset named 'layers' found in the current level directory. Surface mask export aborted."));
        return false;
    }

    // 1) Find our pre-placed SceneCapture2D by name
    ASceneCapture2D* CaptureActor = nullptr;
    for (TActorIterator<ASceneCapture2D> It(World); It; ++It)
    {
        if (It->GetActorLabel().Equals(TEXT("SurfaceMaskCapture"), ESearchCase::IgnoreCase))
        {
            CaptureActor = *It;
            break;
        }
    }
    if (!CaptureActor)
    {
        UE_LOG(LogTemp, Error, TEXT("No SceneCapture2D named 'SurfaceMaskCapture' found."));
        return false;
    }

    // 2) Grab its RenderTarget
    USceneCaptureComponent2D* Comp = CaptureActor->GetCaptureComponent2D();
    UTextureRenderTarget2D* RT = Comp ? Cast<UTextureRenderTarget2D>(Comp->TextureTarget) : nullptr;
    if (!RT)
    {
        UE_LOG(LogTemp, Error, TEXT("CaptureComponent has no TextureTarget."));
        return false;
    }

    // 3) Only draw the landscape proxies
    Comp->ShowOnlyActors.Empty();
    for (TActorIterator<ALandscapeStreamingProxy> It(World); It; ++It)
    {
        Comp->ShowOnlyActors.Add(*It);
    }

    // 4) Ensure output directory exists
    IFileManager::Get().MakeDirectory(*OutputDir, /*Tree=*/ true);

    // 5) Prep tiling math (all units are cm; 1 UU == 1cm)
    const float TerrainSizeCm = 16384.f * 100.f;            // 16 384 m → cm
    const float TileSizeCm = Comp->OrthoWidth;           // 204 800 cm = 2 048 m
    const int32 NumTilesX = FMath::CeilToInt(TerrainSizeCm / TileSizeCm);
    const int32 NumTilesY = FMath::CeilToInt(TerrainSizeCm / TileSizeCm);
    const float HalfTile = TileSizeCm * 0.5f;
    const float CaptureZ = 80000.f;                    // arbitrary Z

    // Lambda: clean stray pixels like your Python script
    auto CleanPaletteMask = [&](TArray<FColor>& Pixels, int32 W, int32 H)
        {
            // 1) Define palette from TerrainLayers asset
            TArray<FColor> Palette;

            // Get colors from TerrainLayers asset
            if (TerrainLayersAsset && TerrainLayersAsset->Layers.Num() > 0)
            {
                UE_LOG(LogTemp, Log, TEXT("Using colors from TerrainLayers asset with %d layers"), TerrainLayersAsset->Layers.Num());

                // Add each layer color to the palette, converting from sRGB to linear
                for (const FTerrainLayer& Layer : TerrainLayersAsset->Layers)
                {
                    // Convert FColor (sRGB) to FLinearColor (linear space)
                    FLinearColor LinearColor = FLinearColor::FromSRGBColor(Layer.LayerColor);

                    // Convert back to FColor but preserve the linear values
                    FColor LinearFColor = LinearColor.ToFColor(false);

                    // Add the linear color to the palette
                    Palette.Add(LinearFColor);

                    UE_LOG(LogTemp, Log, TEXT("Added layer color for '%s': sRGB(%d,%d,%d,%d) [#%02X%02X%02X] -> Linear(%d,%d,%d,%d) [#%02X%02X%02X]"),
                        *Layer.LayerName,
                        Layer.LayerColor.R,
                        Layer.LayerColor.G,
                        Layer.LayerColor.B,
                        Layer.LayerColor.A,
                        Layer.LayerColor.R,
                        Layer.LayerColor.G,
                        Layer.LayerColor.B,
                        LinearFColor.R,
                        LinearFColor.G,
                        LinearFColor.B,
                        LinearFColor.A,
                        LinearFColor.R,
                        LinearFColor.G,
                        LinearFColor.B);
                }
            }
            else
            {
                // Fallback to default palette if no layers found
                UE_LOG(LogTemp, Warning, TEXT("No layers found in TerrainLayers asset, using default palette"));

                // Define default colors in sRGB space
                TArray<FColor> DefaultSRGBColors = {
                    FColor(0,255,  0),  // idx=1
                    FColor(76, 76, 76),  // idx=2
                    FColor(144,144,144),  // idx=3
                    FColor(17, 73,  0),  // idx=4
                    FColor(69, 82,  0),  // idx=5
                    FColor(76, 46,  7)   // idx=6
                };

                // Convert each default color to linear space
                for (int32 i = 0; i < DefaultSRGBColors.Num(); ++i)
                {
                    const FColor& SRGBColor = DefaultSRGBColors[i];

                    // Convert FColor (sRGB) to FLinearColor (linear space)
                    FLinearColor LinearColor = FLinearColor::FromSRGBColor(SRGBColor);

                    // Convert back to FColor but preserve the linear values
                    FColor LinearFColor = LinearColor.ToFColor(false);

                    // Add the linear color to the palette
                    Palette.Add(LinearFColor);

                    UE_LOG(LogTemp, Verbose, TEXT("Added default color %d: sRGB(%d,%d,%d,%d) [#%02X%02X%02X] -> Linear(%d,%d,%d,%d) [#%02X%02X%02X]"),
                        i + 1,
                        SRGBColor.R, SRGBColor.G, SRGBColor.B, SRGBColor.A,
                        SRGBColor.R, SRGBColor.G, SRGBColor.B,
                        LinearFColor.R, LinearFColor.G, LinearFColor.B, LinearFColor.A,
                        LinearFColor.R, LinearFColor.G, LinearFColor.B);
                }
            }

            const int32 NumLabels = Palette.Num();

            // 2) Build initial label map (0=stray, 1..6=palette index)
            TArray<int32> LabelMap;
            LabelMap.AddZeroed(W * H);
            for (int32 i = 0; i < W * H; ++i)
            {
                // Use a tolerance-based approach for color matching
                const FColor& C = Pixels[i];
                int32 bestIdx = -1;
                int32 bestDiff = 255 * 3 + 1; // Max possible difference + 1

                for (int32 idx = 0; idx < NumLabels; ++idx)
                {
                    // Calculate color difference using Manhattan distance (sum of absolute differences)
                    int32 rDiff = FMath::Abs(C.R - Palette[idx].R);
                    int32 gDiff = FMath::Abs(C.G - Palette[idx].G);
                    int32 bDiff = FMath::Abs(C.B - Palette[idx].B);
                    int32 totalDiff = rDiff + gDiff + bDiff;

                    // If exact match, use it immediately
                    if (totalDiff == 0)
                    {
                        bestIdx = idx;
                        break;
                    }

                    // If very close match (within tolerance), consider it
                    const int32 ColorTolerance = 2; // Allow up to 2 units difference per channel
                    if (rDiff <= ColorTolerance && gDiff <= ColorTolerance && bDiff <= ColorTolerance && totalDiff < bestDiff)
                    {
                        bestDiff = totalDiff;
                        bestIdx = idx;
                    }
                }

                if (bestIdx >= 0)
                {
                    LabelMap[i] = bestIdx + 1;

                    // If there was a small difference, log it at verbose level
                    if (bestDiff > 0)
                    {
                        UE_LOG(LogTemp, Verbose, TEXT("Matched color (%d,%d,%d) to palette color (%d,%d,%d) with difference %d"),
                            C.R, C.G, C.B,
                            Palette[bestIdx].R, Palette[bestIdx].G, Palette[bestIdx].B,
                            bestDiff);
                    }
                }
            }

            // 3) Iterative dilation
            const int32 Offsets[9][2] = {
                {-1,-1},{0,-1},{1,-1},
                {-1, 0},{0, 0},{1, 0},
                {-1, 1},{0, 1},{1, 1}
            };
            bool bGrew = true;
            TArray<int32> PrevMap;
            PrevMap.AddZeroed(W * H);

            while (bGrew)
            {
                bGrew = false;
                PrevMap = LabelMap;

                for (int32 label = 1; label <= NumLabels; ++label)
                {
                    for (int32 y = 0; y < H; ++y)
                    {
                        for (int32 x = 0; x < W; ++x)
                        {
                            int32 idxLin = y * W + x;
                            if (PrevMap[idxLin] != 0) continue;

                            for (auto& Off : Offsets)
                            {
                                int32 nx = x + Off[0], ny = y + Off[1];
                                if (nx >= 0 && nx < W && ny >= 0 && ny < H)
                                {
                                    if (PrevMap[ny * W + nx] == label)
                                    {
                                        LabelMap[idxLin] = label;
                                        bGrew = true;
                                        break;
                                    }
                                }
                            }
                        }
                    }
                }
            }

            // 4) Nearest‐distance fill for any remaining stray pixels
            TArray<int32> Strays;
            for (int32 i = 0; i < W * H; ++i)
                if (LabelMap[i] == 0) Strays.Add(i);

            if (Strays.Num())
            {
                // collect source indices per label
                TArray<TArray<int32>> Sources;
                Sources.SetNum(NumLabels);
                for (int32 i = 0; i < W * H; ++i)
                {
                    int32 lab = LabelMap[i];
                    if (lab > 0) Sources[lab - 1].Add(i);
                }

                for (int32 strayIdx : Strays)
                {
                    int32 sx = strayIdx % W, sy = strayIdx / W;
                    float bestD2 = FLT_MAX;
                    int32 bestLab = 1;

                    for (int32 label = 0; label < NumLabels; ++label)
                    {
                        for (int32 srcIdx : Sources[label])
                        {
                            int32 dx = (srcIdx % W) - sx;
                            int32 dy = (srcIdx / W) - sy;
                            float d2 = float(dx * dx + dy * dy);
                            if (d2 < bestD2)
                            {
                                bestD2 = d2;
                                bestLab = label + 1;
                            }
                        }
                    }
                    LabelMap[strayIdx] = bestLab;
                }
            }

            // 5) Rewrite pixels
            for (int32 i = 0; i < W * H; ++i)
            {
                Pixels[i] = Palette[LabelMap[i] - 1];
            }
        };

      // ──────────────────────────────────────────────────────────────────────
        //  NEW: find the root Landscape actor and its material instance
        ALandscape* LandscapeActor = nullptr;
        for (TActorIterator<ALandscape> It(World); It; ++It)
        {
            LandscapeActor = *It;
            break;
        }
        if (!LandscapeActor)
        {
            UE_LOG(LogTemp, Error, TEXT("No ALandscape actor found."));
            return false;
        }

        // The LandscapeMaterial property on ALandscape is your MIC asset
        UMaterialInstanceConstant* LandscapeMIC = Cast<UMaterialInstanceConstant>(LandscapeActor->LandscapeMaterial);
        if (!LandscapeMIC)
        {
            UE_LOG(LogTemp, Error, TEXT("LandscapeMaterial is not a UMaterialInstance."));
            return false;
        }

        // Read the current static-switch (bool) value:
        static const FMaterialParameterInfo ParamInfo(TEXT("VIEW_SURFACEMASK"));
        bool bWasOn = false;
        FGuid ExpressionGuid;
        // bOverriddenOnly=false to fetch the default if not explicitly overridden
        LandscapeMIC->GetStaticSwitchParameterValue(ParamInfo, bWasOn, ExpressionGuid, /*bOverriddenOnly=*/false);

        #if WITH_EDITOR
        // If it was off, switch it on now
            if (!bWasOn)
            {
                // Turn the switch ON for capture
            LandscapeMIC->SetStaticSwitchParameterValueEditorOnly(ParamInfo, /*Value=*/true);
            LandscapeMIC->PostEditChange();
            }
        #endif
        // ──────────────────────────────────────────────────────────────────────

    #if WITH_EDITOR
        // 1) Request synchronous compilation of your MIC
        // Force a full, synchronous recompile of our MIC before we capture
        //   Options=0 → default behavior; Platform=GMaxRHIShaderPlatform → current target
        FMaterialUpdateContext UpdateContext(0, GMaxRHIShaderPlatform);
        UpdateContext.AddMaterialInterface(LandscapeMIC);  // register our material for update :contentReference[oaicite:1]{index=1}
            
        // flush render threads so they see the new shader, then block until the compile queue is empty
        FlushRenderingCommands();
        if (GShaderCompilingManager)
        {
        GShaderCompilingManager->ProcessAsyncResults(true, /*bLimitExecutionTime=*/false);
        }
    // 2) Flush rendering threads & shader compile queue
    FlushRenderingCommands();
    if (GShaderCompilingManager)
    {
        GShaderCompilingManager->ProcessAsyncResults(true, /* bLimitExecutionTime = */ false);
    }
#endif
    // 6) Prepare full‐texture buffer
    const int32 TileW = RT->SizeX;
    const int32 TileH = RT->SizeY;
    const int32 FullW = NumTilesX * TileW;
    const int32 FullH = NumTilesY * TileH;
    TArray<FColor> FullMask;
    FullMask.AddUninitialized(FullW * FullH);

    TArray<FColor> PixelBuffer;

    // 7) Loop through each tile and blit into FullMask

    for (int32 Ty = 0; Ty < NumTilesY; ++Ty)
    {
        for (int32 Tx = 0; Tx < NumTilesX; ++Tx)
        {
            // Compute world‐space center for tile (Tx,Ty)
            const float CX = HalfTile + Tx * TileSizeCm;
            const float CY = HalfTile + Ty * TileSizeCm;
            CaptureActor->SetActorLocation(FVector(CX, CY, CaptureZ));

            // Capture
            FlushRenderingCommands();
            Comp->CaptureScene();
            FlushRenderingCommands();

            // Read back
            // tell ReadPixels “don’t convert linear→gamma”
            FReadSurfaceDataFlags ReadFlags;
            ReadFlags.SetLinearToGamma(false);   // <— disable the implicit sRGB bake
            // (optionally) ReadFlags.SetInvertY(true);

            if (!RT->GameThread_GetRenderTargetResource()->ReadPixels(PixelBuffer, ReadFlags))
            {
                UE_LOG(LogTemp, Error, TEXT("Failed ReadPixels at tile %d,%d"), Tx, Ty);
                continue;
            }

            // Clean stray pixels to your palette
            CleanPaletteMask(PixelBuffer, RT->SizeX, RT->SizeY);

            // --- blit this tile with correct orientation ---
            for (int32 y = 0; y < TileH; ++y)
            {
                // 1) read rows in natural (top-first) order
                int32 SrcRow = y * TileW;

                // 2) place tile-row so Ty=0 ends up at bottom of the full mask
                int32 DestY = (NumTilesY - 1 - Ty) * TileH + y;
                int32 DstRow = DestY * FullW + (Tx * TileW);

                FMemory::Memcpy(
                    /*dest=*/ &FullMask[DstRow],
                    /*src=*/  &PixelBuffer[SrcRow],
                    TileW * sizeof(FColor)
                );
            }
        }
    }

    // ──────────────────────────────────────────────────────────────────────
    //  NEW: restore original VIEW_SURFACEMASK
    #if WITH_EDITOR
        if (!bWasOn)
        {
        // Switch it back OFF
        LandscapeMIC->SetStaticSwitchParameterValueEditorOnly(ParamInfo, /*Value=*/false);
        LandscapeMIC->PostEditChange();
        }
    #endif
    // ──────────────────────────────────────────────────────────────────────

     // 8) Create a merged UTexture2D asset in-editor
#if WITH_EDITOR
    // 1) Compute package & object paths
    const FString MapPackage   = World->GetOutermost()->GetName();               // "/Game/.../ember/ember"
    const FString MapBase      = FPackageName::GetShortName(MapPackage);         // "ember"
    const FString MapDir       = FPackageName::GetLongPackagePath(MapPackage);   // "/Game/.../ember"
    const FString SharedDir    = MapDir / (MapBase + TEXT("_sharedassets"));     // ".../ember_sharedassets"
    const FString AssetName    = MapBase + TEXT("_surface_lco");                 // "ember_surface_lco"
    const FString PackagePath  = SharedDir / AssetName;                         // "/Game/.../ember_sharedassets/ember_surface_lco"
    const FString ObjectPath   = PackagePath + TEXT(".") + AssetName;           // "/Game/.../ember_sharedassets/ember_surface_lco.ember_surface_lco"

    // 2) Try to load the existing texture
    UTexture2D* MergedTex = LoadObject<UTexture2D>(nullptr, *ObjectPath);
    UPackage*   Pkg        = nullptr;
    bool        bCreated   = false;

    if (MergedTex)
    {
        // Already loaded or on disk—grab its package and prepare to overwrite
        Pkg = MergedTex->GetOutermost();
        MergedTex->Modify();
    }
    else
    {
        // Didn't exist yet: create the package and the texture
        Pkg = CreatePackage(*PackagePath);
        MergedTex = NewObject<UTexture2D>(
            Pkg,
            *AssetName,
            RF_Public | RF_Standalone
        );
        bCreated = true;
    }

    // 3) Overwrite its Source block with your FullMask data
    MergedTex->Source.Init(
        FullW, FullH,
        /*NumSlices=*/1,
        /*NumMips=*/1,
        ETextureSourceFormat::TSF_BGRA8,
        reinterpret_cast<const uint8*>(FullMask.GetData())
    );

    // 4) Re-apply all your build settings
    MergedTex->LODGroup            = TEXTUREGROUP_8BitData;
    MergedTex->CompressionSettings = TextureCompressionSettings::TC_VectorDisplacementmap;
    MergedTex->CompressionNoAlpha  = true;
    MergedTex->Filter              = TF_Nearest;
    MergedTex->MipGenSettings      = TMGS_NoMipmaps;
    MergedTex->SRGB                = false;

    // 5) Tell the editor about the change
    MergedTex->PostEditChange();
    MergedTex->MarkPackageDirty();

    // 6) If brand-new, register it so it appears in the browser
    if (bCreated)
    {
        FAssetRegistryModule& ARM = FModuleManager::LoadModuleChecked<FAssetRegistryModule>(TEXT("AssetRegistry"));
        ARM.Get().AssetCreated(MergedTex);
    }

    // 7) Save (overwrites existing uasset/uexp if present)
    const FString Filename = FPackageName::LongPackageNameToFilename(
        PackagePath,
        FPackageName::GetAssetPackageExtension()
    );
    bool bOK = UPackage::SavePackage(
        Pkg,
        MergedTex,
        EObjectFlags::RF_Public | RF_Standalone,
        *Filename
    );

    UE_LOG(LogTemp, Log,
        TEXT("%s surface_lco asset to %s : %s"),
        bCreated ? TEXT("Created") : TEXT("Updated"),
        *Filename,
        bOK ? TEXT("OK") : TEXT("FAILED")
    );
#endif


     return true;
}






// StaticEntityInfo

StaticEntityInfo::StaticEntityInfo(FBinaryReader& Input)
{
    ClassName = FString(Input.ReadAsciiz().c_str());
    ShapeName = FString(Input.ReadAsciiz().c_str());
    Position = FVector(Input.ReadFloat(), Input.ReadFloat(), Input.ReadFloat());
    ObjectId = FObjectId(Input.ReadInt32());
}
