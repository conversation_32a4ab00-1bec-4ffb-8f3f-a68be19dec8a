#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "P3DBlueprint.h"

class FAssetTypeActions_P3D : public FAssetTypeActions_Base
{
public:
    virtual FText GetName() const override { return NSLOCTEXT("AssetTypeActions", "AssetTypeActions_P3D", "P3D"); }
    virtual FColor GetTypeColor() const override { return FColor::Emerald; }
    virtual UClass* GetSupportedClass() const override { return UP3DBlueprint::StaticClass(); }
    virtual uint32 GetCategories() override { return EAssetTypeCategories::Misc; } // Use your custom category if desired

    virtual void OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor) override;
    virtual bool CanFilter() override { return true; }
};