// TerrainLayers.h
#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "Engine/EngineTypes.h"
#include "Misc/FileHelper.h"

#include "TerrainLayers.generated.h"

/** Represents one terrain layer entry */
USTRUCT(BlueprintType)
struct FTerrainLayer
{
    GENERATED_BODY()

    // The layer�s display name.
    UPROPERTY(EditAnywhere, Category = "Terrain Layer")
    FString LayerName;

    // The color of the layer.
    UPROPERTY(EditAnywhere, Category = "Terrain Layer")
    FColor LayerColor;

    // Material linked to the layer.
    UPROPERTY(EditAnywhere, Category = "Terrain Layer")
    UMaterialInterface* LayerMaterial;

    // Texture linked to the layer.
    UPROPERTY(EditAnywhere, Category = "Terrain Layer")
    UTexture* LayerTexture;
};

/** Custom asset that holds terrain layer information */
UCLASS(BlueprintType)
class DAYZTOOLS_API UTerrainLayers : public UDataAsset
{
    GENERATED_BODY()

public:
    virtual void PostLoad() override;
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
    virtual void PreSave(const ITargetPlatform* TargetPlatform) override;
    // The config file path for the layers configuration.
    // (Using FFilePath gives you a file-browse widget in the editor.)
    UPROPERTY(EditAnywhere, Category = "Terrain Layers")
    FFilePath LayersConfigFile;

    // Material instance for the terrain
    UPROPERTY(EditAnywhere, Category = "Terrain Layers", meta = (DisplayName = "Terrain Material"))
    UMaterialInterface* TerrainMaterial;

    // We'll track whether this file path is valid or not
    UPROPERTY(VisibleAnywhere, Category = "Terrain Layers")
    bool bHasValidConfigFile = false;

    // The array of layers. We use an edit condition here so that the array is disabled
    // until a valid file is selected.
    UPROPERTY(EditAnywhere, Category = "Terrain Layers", meta = (EditCondition = "bHasValidConfigFile"))
    TArray<FTerrainLayer> Layers;

    // Call this in the editor (via a button) to parse the config file and populate Layers.
    UFUNCTION(CallInEditor, Category = "Terrain Layers")
    bool ParseAndPopulateLayers();

    UFUNCTION(CallInEditor, Category = "Terrain Layers", meta = (DisplayName = "Reload From Disk"))
    bool ReloadFromDisk();

    // Updates the material parameter with the given layer color
    void UpdateMaterialParameterForLayer(const FString& LayerName, const FColor& LayerColor);

    // Syncs all layer colors with material parameters
    UFUNCTION(CallInEditor, Category = "Terrain Layers", meta = (DisplayName = "Sync Colors With Material"))
    void SyncAllLayerColorsWithMaterial();

private:

    bool ExtractLayersBlock(const FString& ConfigText, FString& OutBlock)
    {
        int32 StartIdx = ConfigText.Find(TEXT("class Layers"));
        if (StartIdx == INDEX_NONE)
        {
            return false;
        }
        int32 BraceIdx = ConfigText.Find(TEXT("{"), ESearchCase::IgnoreCase, ESearchDir::FromStart, StartIdx);
        if (BraceIdx == INDEX_NONE)
        {
            return false;
        }
        int32 Depth = 0;
        int32 EndIdx = BraceIdx;
        for (int32 i = BraceIdx; i < ConfigText.Len(); i++)
        {
            TCHAR ch = ConfigText[i];
            if (ch == '{')
            {
                Depth++;
            }
            else if (ch == '}')
            {
                Depth--;
                if (Depth == 0)
                {
                    EndIdx = i;
                    break;
                }
            }
        }
        if (EndIdx > BraceIdx)
        {
            OutBlock = ConfigText.Mid(BraceIdx + 1, EndIdx - BraceIdx - 1);
            return true;
        }
        return false;
    }

    // Helper function to convert DayZ asset paths to Unreal asset paths
    bool ConvertDayZPathToUnrealPath(const FString& DayZPath, FString& OutUnrealPath, const FString& Extension)
    {
        if (DayZPath.IsEmpty())
        {
            return false;
        }

        // Convert backslashes to forward slashes for consistency
        FString NormalizedPath = DayZPath;
        NormalizedPath = NormalizedPath.Replace(TEXT("\\"), TEXT("/"));

        // Remove the extension if it exists
        if (NormalizedPath.EndsWith(Extension))
        {
            NormalizedPath = NormalizedPath.Left(NormalizedPath.Len() - Extension.Len());
        }

        // Add "/Game/" prefix
        OutUnrealPath = FString::Printf(TEXT("/Game/%s"), *NormalizedPath);

        return true;
    }

};
