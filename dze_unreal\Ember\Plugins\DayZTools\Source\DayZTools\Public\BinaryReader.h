#pragma once

#include "CoreMinimal.h"
#include <vector>
#include <string>
#include <functional>
#include <memory>

class FBinaryReader
{
public:
    FBinaryReader(const std::vector<uint8>& InData) : Data(InData) {}
    //FBinaryReader(const std::vector<uint8_t>& data);

    bool UseCompressionFlag = false;
    bool UseLZOCompression = false;
    int32 Version = 0;

    int64 GetPosition() const;
    void SetPosition(int64 NewPosition);

    bool HasReachedEnd() const;

    uint32 ReadUInt24();
    std::string ReadAscii(int32 Count);
    std::string ReadAscii();
    std::string ReadAscii32();
    std::string ReadAsciiz();

    std::vector<uint8_t> ReadBytes(int32_t count);

    template<typename T>
    std::vector<T> ReadArrayBase(std::function<T(FBinaryReader&)> ReadElement, int32 Size);

    template<typename T>
    std::vector<T> ReadArray(std::function<T(FBinaryReader&)> ReadElement);

    std::vector<float> ReadFloatArray();
    std::vector<int32> ReadIntArray();
    std::vector<std::string> ReadStringArray();

    template<typename T>
    std::vector<T> ReadCompressedArray(std::function<T(FBinaryReader&)> ReadElement, int32 ElemSize);

    std::vector<int16> ReadCompressedShortArray();
    std::vector<int32> ReadCompressedIntArray();
    std::vector<float> ReadCompressedFloatArray();
    std::vector<uint8> ReadCompressedByteArray();

    template<typename T>
    std::vector<T> ReadCondensedArray(std::function<T(FBinaryReader&)> ReadElement, int32 SizeOfT);

    std::vector<int32> ReadCondensedIntArray();

    int32 ReadCompactInteger();
    std::vector<uint8> ReadCompressed(uint32 ExpectedSize, bool ForceCompressed = false);
    std::vector<uint8> ReadLZO(uint32 ExpectedSize, bool ForceCompressed = false);
    std::vector<uint8> ReadLZSS(uint32 ExpectedSize, bool InPAA = false);
    std::vector<uint8> ReadCompressedIndices(int32 BytesToRead, uint32 ExpectedSize);
    std::vector<float> ReadCompressedFloats(int32 NElements);
    std::vector<float> ReadFloats(int32 NElements);
    std::vector<uint16> ReadUshorts(int32 NElements);

    template<typename T>
    std::vector<T> ReadCompressed(std::function<T(FBinaryReader&)> ReadElement, int32 NElements, int32 ElemSize);

    uint8 ReadByte();
    int16 ReadInt16();
    uint16 ReadUInt16();
    int32 ReadInt32();
    uint32 ReadUInt32();
    float ReadFloat();
    float ReadFloat2();
    bool ReadBoolean();

private:
    const std::vector<uint8>& Data;
    int64 Position = 0;
};