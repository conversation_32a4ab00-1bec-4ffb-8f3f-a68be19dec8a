// FBinaryWriter.h

#pragma once

#include "CoreMinimal.h"
#include <vector>
#include <string>

class FBinaryWriter
{
public:
    FBinaryWriter();
    int64 GetPosition() const;
    void SetPosition(int64 NewPosition);

    void WriteByte(uint8 Value);
    void WriteInt16(int16 Value);
    void WriteUInt16(uint16 Value);
    void WriteInt32(int32 Value);
    void WriteUInt32(uint32 Value);
    void WriteUInt24(uint32 Value);
    void WriteFloat(float Value);
    void WriteAscii(const std::string& Value);
    void WriteAscii(int32 Count, const std::string& Value);
    void WriteAscii32(const std::string& Value);
    void WriteAsciiz(const std::string& Value);
    void WriteBytes(const std::vector<uint8_t>& Data);
    void WriteBytes(const uint8_t* Data, int32 Length);
    void WriteBytes(const TArray<uint8>& InData);

    // Write arrays
    void WriteFloatArray(const std::vector<float>& Array);
    void WriteFloatArray(const TArray<float>& Array);

    void WriteUInt16Array(const std::vector<uint16>& Array);
    void WriteUInt16Array(const TArray<uint16>& Array);

    // Save data to file
    bool SaveToFile(const FString& FilePath) const;

    // Get the binary data
    const std::vector<uint8_t>& GetData() const { return Data; }

private:
    std::vector<uint8_t> Data;
    size_t Position;  // Changed from int64 to size_t
};
