#pragma once

#include "CoreMinimal.h"
#include "Engine/DataAsset.h"
#include "BiomeTypes.h"
#include "BiomeData.generated.h"

UCLASS(BlueprintType)
class DAYZTOOLS_API UBiomeData : public UPrimaryDataAsset
{
    GENERATED_BODY()
    
public:
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Biome")
    FString BiomeName = "Unnamed Biome";
    
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Biome")
    EBiomeType BiomeType = EBiomeType::Forest;
    
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Biome")
    FColor DebugColor = FColor::Green;
    
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Biome")
    TArray<FBiomeSpecies> Species;
    
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Biome")
    TArray<FTerrainAttributeRange> BiomeConditions;
    
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Biome", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float BiomePriority = 0.5f;
    
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Biome")
    bool bIsSubBiome = false;
    
    UPROPERTY(EditAnywhere, BlueprintReadOnly, Category = "Biome", meta = (EditCondition = "bIsSubBiome"))
    TArray<EBiomeType> ParentBiomes;
    
public:
    virtual FPrimaryAssetId GetPrimaryAssetId() const override
    {
        return FPrimaryAssetId("BiomeData", GetFName());
    }
};