#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "BiomeTypes.h"
#include "BiomeDebugVisualizer.generated.h"

UENUM(BlueprintType)
enum class EBiomeDebugMode : uint8
{
    None            UMETA(DisplayName = "None"),
    Slope           UMETA(DisplayName = "Slope"),
    Aspect          UMETA(DisplayName = "Aspect"),
    Occlusion       UMETA(DisplayName = "Occlusion"),
    FlowAccumulation UMETA(DisplayName = "Flow Accumulation"),
    Curvature       UMETA(DisplayName = "Curvature"),
    WindExposure    UMETA(DisplayName = "Wind Exposure"),
    Altitude        UMETA(DisplayName = "Altitude"),
    Custom          UMETA(DisplayName = "Custom Attribute")
};

UCLASS()
class DAYZTOOLS_API ABiomeDebugVisualizer : public AActor
{
    GENERATED_BODY()

public:
    ABiomeDebugVisualizer();

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    EBiomeDebugMode DebugMode = EBiomeDebugMode::None;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug", meta = (EditCondition = "DebugMode == EBiomeDebugMode::Custom"))
    FString CustomAttributeName = "Slope";

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    bool bShowLegend = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    float VisualizationScale = 1000.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Debug")
    class UPCGComponent* PCGComponent;

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void UpdateVisualization();

    UFUNCTION(BlueprintCallable, Category = "Debug")
    void ClearVisualization();

protected:
    virtual void BeginPlay() override;
    virtual void Tick(float DeltaTime) override;

#if WITH_EDITOR
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif

private:
    UPROPERTY()
    TArray<class UInstancedStaticMeshComponent*> DebugMeshComponents;

    void VisualizeAttribute(const FString& AttributeName);
    FLinearColor GetColorForValue(float Value, EBiomeDebugMode Mode);
};