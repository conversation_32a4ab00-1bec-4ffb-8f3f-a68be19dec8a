// CELootPoint.h
// Copyright Epic Games, Inc. All Rights Reserved.

#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "CELootPoint.generated.h"

class USceneComponent;
class UStaticMeshComponent;
class UMaterialInstanceDynamic;

UCLASS(Blueprintable, Category = "DayZ Tools|CE")
class DAYZTOOLS_API ACELootPoint : public AActor
{
	GENERATED_BODY()

public:
	ACELootPoint();

	// Tick every frame (including in the editor viewport)
	virtual void Tick(float DeltaTime) override;
#if WITH_EDITOR
	virtual bool ShouldTickIfViewportsOnly() const override { return true; }
#endif

protected:
	virtual void BeginPlay() override;
	virtual void PostLoad() override;

#if WITH_EDITOR
	virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
	virtual void OnConstruction(const FTransform& Transform) override;
	virtual void EditorApplyTranslation(
		const FVector& DeltaTranslation,
		bool bAltDown, bool bShiftDown, bool bCtrlDown) override;
	virtual void EditorApplyScale(
		const FVector& DeltaScale,
		const FVector* PivotLocation,
		bool bAltDown, bool bShiftDown, bool bCtrlDown) override;
#endif

#if WITH_EDITOR
	// Prevent viewport rotations
	virtual void EditorApplyRotation(
		const FRotator& DeltaRotation,
		bool bAltDown, bool bShiftDown, bool bCtrlDown) override;
#endif

	// Uniform X/Y, independent Z
	void UpdateScale();
	void UpdateScale(float TargetXY);

	// (Re)apply or create our translucent green material
	void SetupMaterial();

	// Runtime-safe loader for our base material
	UMaterialInstanceDynamic* CreateDefaultTranslucentMaterial();

	/** Scene root at actor origin */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	USceneComponent* SceneRootComponent;

	/** Solid, translucent cylinder */
	UPROPERTY(VisibleAnywhere, BlueprintReadOnly, Category = "Components")
	UStaticMeshComponent* CylinderMeshComponent;

	/** Dynamic translucent material */
	UPROPERTY(Transient)
	UMaterialInstanceDynamic* DynamicMaterialInstance;

	/** Color for the solid cylinder */
	UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Appearance")
	FLinearColor CylinderColor = FLinearColor(0.f, 0.37f, 0.05f, 0.2f);

public:
    // Set selected state
    void SetSelected(bool bSelected);

    // Get item index
    int32 GetItemIndex() const { return ItemIndex; }

    // Set item index
    void SetItemIndex(int32 InItemIndex) { ItemIndex = InItemIndex; }

    // Public method to set uniform XY scale
    void SetUniformXYScale(float TargetXY) { UpdateScale(TargetXY); }

private:
	bool bIsUpdatingScale = false;

    // Item index in the asset
    int32 ItemIndex = -1;

    // Selected state
    bool bIsSelected = false;
};
