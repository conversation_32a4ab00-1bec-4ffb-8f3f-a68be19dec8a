// CELootPoints.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "ConfigClass/ConfigClass.h"
#include "CEType/CEUsage.h"
#include "CEType/CECategory.h"
#include "CEType/CETag.h"
#include "CELootPoints.generated.h"

// Forward declarations
class UP3DBlueprint;

// Enum for container types
UENUM(BlueprintType)
enum class EContainerType : uint8
{
    LootFloor UMETA(DisplayName = "lootFloor"),
    LootShelves UMETA(DisplayName = "lootshelves"),
    LootWeapons UMETA(DisplayName = "lootweapons")
};

// Root item structure
USTRUCT(BlueprintType)
struct FCELootPointsRoot
{
    GENERATED_USTRUCT_BODY()

    // Array of usage types
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Settings")
    TArray<TSoftObjectPtr<UCEUsage>> Usage;

    // Config class asset reference for the model
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Settings")
    TSoftObjectPtr<UConfigClass> Type;

    // Maximum amount of loot
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Root Settings", meta = (ClampMin = "0"))
    int32 LootMax = 5;

    // Helper function to get the P3D model from ConfigClass
    UP3DBlueprint* GetModel() const;
};

// Container item structure
USTRUCT(BlueprintType)
struct FCELootPointsContainer
{
    GENERATED_USTRUCT_BODY()

    // Container type (lootFloor, lootshelves, lootweapons)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Container Settings")
    EContainerType ContainerType = EContainerType::LootFloor;

    // Maximum amount of loot
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Container Settings", meta = (ClampMin = "0"))
    int32 LootMax = 5;

    // Array of categories
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Container Settings")
    TArray<TSoftObjectPtr<UCECategory>> Category;

    // Array of tags
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Container Settings")
    TArray<TSoftObjectPtr<UCETag>> Tag;

    // Name for display in hierarchy (derived from ContainerType)
    UPROPERTY()
    FString Name;

    // Update the name based on the container type
    void UpdateName();
};

// Loot point item structure
USTRUCT(BlueprintType)
struct FCELootPointsPoint
{
    GENERATED_USTRUCT_BODY()

    // Position coordinates
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Settings")
    float X = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Settings")
    float Y = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Settings")
    float Z = 0.0f;

    // Scale (uniform X/Y, independent Z)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Settings")
    float ScaleXY = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Settings")
    float ScaleZ = 1.0f;

    // Force spawn flag
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Point Settings")
    bool bForceSpawn = false;

    // Name for display in hierarchy
    UPROPERTY()
    FString Name = TEXT("LootPoint");
};

// Item type enum
UENUM(BlueprintType)
enum class ECELootPointsItemType : uint8
{
    Root UMETA(DisplayName = "Root"),
    Container UMETA(DisplayName = "Container"),
    Point UMETA(DisplayName = "Point")
};

// Hierarchical item structure
USTRUCT(BlueprintType)
struct FCELootPointsItem
{
    GENERATED_USTRUCT_BODY()

    // Type of this item
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Settings")
    ECELootPointsItemType ItemType = ECELootPointsItemType::Root;

    // Root data (valid if ItemType == Root)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Settings", meta = (EditCondition = "ItemType == ECELootPointsItemType::Root", EditConditionHides))
    FCELootPointsRoot RootData;

    // Container data (valid if ItemType == Container)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Settings", meta = (EditCondition = "ItemType == ECELootPointsItemType::Container", EditConditionHides))
    FCELootPointsContainer ContainerData;

    // Point data (valid if ItemType == Point)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Item Settings", meta = (EditCondition = "ItemType == ECELootPointsItemType::Point", EditConditionHides))
    FCELootPointsPoint PointData;

    // Parent index (-1 for root)
    UPROPERTY()
    int32 ParentIndex = -1;

    // Get the display name for this item
    FString GetDisplayName() const;
};

/**
 * CE Loot Points asset
 * Used to define loot points for a building or area
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UCELootPoints : public UObject
{
    GENERATED_BODY()

public:
    UCELootPoints(const FObjectInitializer& ObjectInitializer);

    // Asset name
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loot Points")
    FString AssetName;

    // Hierarchical items
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Loot Points")
    TArray<FCELootPointsItem> Items;

    // Add a container item
    void AddContainer(EContainerType ContainerType);

    // Add a point item to a container
    void AddPoint(int32 ContainerIndex);

    // Remove an item
    void RemoveItem(int32 ItemIndex);

    // Export to XML string
    UFUNCTION(BlueprintCallable, Category = "Loot Points")
    FString ExportToXML() const;

    // Export to XML string with all properties
    UFUNCTION(BlueprintCallable, Category = "Loot Points")
    FString ExportToXMLWithAllProperties() const;

    // Initialize with default structure
    void InitializeDefault();
};
