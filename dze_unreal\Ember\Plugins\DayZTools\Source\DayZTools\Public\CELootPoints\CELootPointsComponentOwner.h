// CELootPointsComponentOwner.h
#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "CELootPointsComponentOwner.generated.h"

/**
 * Actor that owns components for the CELootPoints editor preview scene.
 * This is used to ensure proper component lifetime management.
 */
UCLASS(Transient)
class DAYZTOOLS_API ACELootPointsComponentOwner : public AActor
{
    GENERATED_BODY()

public:
    // Sets default values for this actor's properties
    ACELootPointsComponentOwner();

    // Called when the game starts or when spawned
    virtual void BeginPlay() override;

    // Called every frame
    virtual void Tick(float DeltaTime) override;
};
