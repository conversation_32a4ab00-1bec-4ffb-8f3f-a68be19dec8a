// CELootPointsEditorCommands.h
#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"

/**
 * Commands for the CELootPoints editor
 */
class FCELootPointsEditorCommands : public TCommands<FCELootPointsEditorCommands>
{
public:
    // Constructor
    FCELootPointsEditorCommands();

    // TCommands interface
    virtual void RegisterCommands() override;

    // Commands
    TSharedPtr<FUICommandInfo> ExportToXML;
    TSharedPtr<FUICommandInfo> ToggleGroundPlane;

    // Copy/Paste commands
    TSharedPtr<FUICommandInfo> CopyItem;
    TSharedPtr<FUICommandInfo> PasteItem;
};
