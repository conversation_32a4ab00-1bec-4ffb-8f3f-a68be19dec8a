// CELootPointsEditorToolkit.h
#pragma once

#include "CoreMinimal.h"
#include "Toolkits/AssetEditorToolkit.h"
#include "EditorUndoClient.h"            // ← for FEditorUndoClient
#include "CELootPoints/CELootPoints.h"

// Forward declarations
class IDetailsView;
class SCELootPointsEditorViewport;
class SCELootPointsHierarchy;
class UCELootPointsItemProxy;

/**
 * Editor toolkit for CELootPoints assets
 */
class FCELootPointsEditorToolkit
  : public FAssetEditorToolkit
  , public FEditorUndoClient      // ← implement undo/redo callbacks
{
public:
    // Constructor
    FCELootPointsEditorToolkit();

    // Destructor
    virtual ~FCELootPointsEditorToolkit();

    // FEditorUndoClient interface
    virtual void PostUndo(bool bSuccess) override;
    virtual void PostRedo(bool bSuccess) override;

    // Initialize the editor
    void InitCELootPointsEditorToolkit(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UCELootPoints* InLootPointsAsset);

    // FAssetEditorToolkit interface
    virtual FName GetToolkitFName() const override;
    virtual FText GetBaseToolkitName() const override;
    virtual FString GetWorldCentricTabPrefix() const override;
    virtual FLinearColor GetWorldCentricTabColorScale() const override;
    virtual void RegisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;
    virtual void UnregisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;

    // Select an item
    void SelectItem(int32 ItemIndex);

    // Handle transform update from the preview scene
    void HandleTransformUpdate(int32 ItemIndex, const FTransform& NewTransform);

    // Refresh the editor
    void RefreshEditor();

    // Export to XML
    void ExportToXML();

    // Toggle ground plane visibility
    void ToggleGroundPlane();

    // Check if ground plane is visible
    bool IsGroundPlaneVisible() const;

    // Copy the selected item
    void CopySelectedItem();

    // Paste the copied item
    void PasteItem();

    // Check if paste is possible
    bool CanPasteItem() const;

    // → Convenience accessor for viewport/client code
    UCELootPoints* GetAsset() const { return LootPointsAsset; }

private:
    // Tab identifiers
    static const FName ViewportTabID;
    static const FName DetailsTabID;
    static const FName HierarchyTabID;

    // The asset being edited
    UCELootPoints* LootPointsAsset;

    // The selected item index
    int32 SelectedItemIndex;

    // The selected item proxy
    UCELootPointsItemProxy* SelectedItemProxy;

    // The viewport widget
    TSharedPtr<SCELootPointsEditorViewport> ViewportWidget;

    // The hierarchy widget
    TSharedPtr<SCELootPointsHierarchy> HierarchyWidget;

    // The details view
    TSharedPtr<IDetailsView> DetailsView;

    // Clipboard storage for copy/paste operations
    FCELootPointsItem ClipboardItem;

    // Flag to track if we have a valid item in the clipboard
    bool bHasValidClipboardItem;

    // Flag to prevent recursive selection during copy/paste operations
    bool bIsSelectingItem;

    // Create the editor layout
    TSharedRef<FTabManager::FLayout> CreateEditorLayout();

    // Create a tab
    TSharedRef<SDockTab> SpawnViewportTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnDetailsTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnHierarchyTab(const FSpawnTabArgs& Args);

    // Create a proxy for the selected item
    void CreateItemProxy(int32 ItemIndex);

    // Update the details view
    void UpdateDetailsView();

    // Property changed callback
    void OnPropertyChanged(const FPropertyChangedEvent& PropertyChangedEvent);

    // Bind commands
    void BindCommands();

    // Register toolbar extender
    void RegisterToolbarExtender();

    // Build toolbar
    void BuildToolbar(FToolBarBuilder& ToolBarBuilder);

    // Attempt to rename the asset based on the selected Config Class
    bool AttemptRenameToConfigClass();
};
