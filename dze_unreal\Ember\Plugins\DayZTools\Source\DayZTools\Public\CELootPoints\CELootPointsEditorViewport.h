// CELootPointsEditorViewport.h
#pragma once

#include "CoreMinimal.h"
#include "SEditorViewport.h"
#include "Editor/UnrealEd/Public/SCommonEditorViewportToolbarBase.h"
#include "CELootPoints/CELootPoints.h"
#include "CELootPoints/CELootPointsPreviewScene.h"
#include "CELootPoints/CELootPointsViewportClient.h"
#include "Widgets/SWidget.h"
#include "Widgets/DeclarativeSyntaxSupport.h"
#include "Misc/Attribute.h"

class FCELootPointsEditorToolkit;
class SCELootPointsEditorViewportToolBar;
class IToolkitHost;

/**
 * Viewport widget for the CELootPoints editor
 */
class SCELootPointsEditorViewport : public SEditorViewport, public ICommonEditorViewportToolbarInfoProvider
{
public:
    SLATE_BEGIN_ARGS(SCELootPointsEditorViewport) {}
        SLATE_ARGUMENT(TSharedPtr<FCELootPointsPreviewScene>, PreviewScene)
        SLATE_ARGUMENT(UCELootPoints*, Asset)
        SLATE_ARGUMENT(FCELootPointsEditorToolkit*, EditorToolkit)
    SLATE_END_ARGS()

    // Construct the widget
    void Construct(const FArguments& InArgs);

    // Destructor
    virtual ~SCELootPointsEditorViewport();

    // Set the asset being edited
    void SetAsset(UCELootPoints* InAsset);

    // Refresh the viewport
    void RefreshViewport();

    // Select an item
    void SelectItem(int32 ItemIndex);

    // Get the preview scene
    TSharedPtr<FCELootPointsPreviewScene> GetPreviewScene() const { return PreviewScene; }

    // Get the viewport client
    TSharedPtr<FCELootPointsViewportClient> GetViewportClient() const { return ViewportClient; }

    // ICommonEditorViewportToolbarInfoProvider interface
    virtual TSharedRef<SEditorViewport> GetViewportWidget() override;
    virtual TSharedPtr<FExtender> GetExtenders() const override;
    virtual void OnFloatingButtonClicked() override;

    // Toggle the ground plane visibility
    void ToggleGroundPlaneVisibility();

    // Check if the ground plane is visible
    bool IsGroundPlaneVisible() const;

    // Export the event group
    void ExportEventGroup();

    

    // Add an extender to the viewport toolbar
    void AddViewportToolbarExtender(TSharedPtr<FExtender> Extender)
    {
        ViewportToolbarExtenders.Add(Extender);
    }

protected:
    // SEditorViewport interface
    virtual TSharedRef<FEditorViewportClient> MakeEditorViewportClient() override;
    virtual TSharedPtr<SWidget> MakeViewportToolbar() override;

private:
    // The viewport client
    TSharedPtr<FCELootPointsViewportClient> ViewportClient;

    // The preview scene
    TSharedPtr<FCELootPointsPreviewScene> PreviewScene;

    // The asset being edited
    UCELootPoints* Asset;

    // The editor toolkit
    FCELootPointsEditorToolkit* EditorToolkit;

    // Timer handle for delayed refresh
    FTimerHandle WorldCheckTimerHandle;

    // Number of attempts to check for a valid world
    int32 WorldCheckAttempts;

    // Maximum number of attempts to check for a valid world
    static const int32 MaxWorldCheckAttempts = 10;

    // Interval between world check attempts (in seconds)
    static const float WorldCheckInterval;

    // Check if the world is valid and refresh the viewport if it is
    void CheckWorldAndRefresh();

    // Start the world check timer
    void StartWorldCheckTimer();

    TArray<TSharedPtr<FExtender>> ViewportToolbarExtenders;

    // Flag to track ground plane visibility
    bool bShowGroundPlane = false;
};
