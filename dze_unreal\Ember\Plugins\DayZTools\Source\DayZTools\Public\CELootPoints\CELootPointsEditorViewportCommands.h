// CELootPointsEditorViewportCommands.h
#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "EditorStyleSet.h"

class FCELootPointsEditorViewportCommands : public TCommands<FCELootPointsEditorViewportCommands>
{
public:
    FCELootPointsEditorViewportCommands()
        : TCommands<FCELootPointsEditorViewportCommands>(
            TEXT("CELootPointsEditorViewport"), // Context name
            NSLOCTEXT("Contexts", "CELootPointsEditorViewport", "CE Loot Points Editor Viewport"), // Display name
            NAME_None, // No parent
            FAppStyle::GetAppStyleSetName() // Icon Style Set
        )
    {
    }

    virtual void RegisterCommands() override;

    // Command for toggling the ground plane
    TSharedPtr<FUICommandInfo> ToggleGroundPlane;

    // Command for exporting the event group
    TSharedPtr<FUICommandInfo> ExportEventGroup;
};
