// CELootPointsEditorViewportToolBar.h
#pragma once

#include "CoreMinimal.h"
#include "Editor/UnrealEd/Public/SCommonEditorViewportToolbarBase.h"

class SCELootPointsEditorViewportToolBar : public SCommonEditorViewportToolbarBase
{
public:
    SLATE_BEGIN_ARGS(SCELootPointsEditorViewportToolBar) {}
    SLATE_END_ARGS()

    void Construct(const FArguments& InArgs, TSharedPtr<class SCELootPointsEditorViewport> InViewport);

protected:
    // Create our custom toolbar
    TSharedRef<SWidget> MakeToolbar(const TSharedPtr<FExtender> InExtenders);

private:
    // Reference to the editor viewport
    TSharedPtr<class SCELootPointsEditorViewport> EditorViewport;
};
