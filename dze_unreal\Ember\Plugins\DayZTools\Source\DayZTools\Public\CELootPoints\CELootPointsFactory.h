// CELootPointsFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CELootPointsFactory.generated.h"

/**
 * Factory for creating CELootPoints assets
 */
UCLASS()
class DAYZTOOLS_API UCELootPointsFactory : public UFactory
{
    GENERATED_BODY()

public:
    UCELootPointsFactory();

    // UFactory interface
    virtual UObject* FactoryCreateNew(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
