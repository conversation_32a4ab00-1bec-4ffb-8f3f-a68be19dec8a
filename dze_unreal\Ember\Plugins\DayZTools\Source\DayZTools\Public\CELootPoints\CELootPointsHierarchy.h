// CELootPointsHierarchy.h
#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "CELootPoints/CELootPoints.h"

template <typename ItemType> class STreeView;
class FCELootPointsEditorToolkit;

/**
 * Tree item for the hierarchy view
 */
class FCELootPointsHierarchyItem : public TSharedFromThis<FCELootPointsHierarchyItem>
{
public:
    // The index of this item in the asset
    int32 ItemIndex;

    // The display name of this item
    FString DisplayName;

    // Child items
    TArray<TSharedPtr<FCELootPointsHierarchyItem>> Children;

    // Constructor
    FCELootPointsHierarchyItem(int32 InItemIndex, const FString& InDisplayName)
        : ItemIndex(InItemIndex)
        , DisplayName(InDisplayName)
    {
    }
};

/**
 * Hierarchy widget for the CELootPoints editor
 */
class SCELootPointsHierarchy : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(SCELootPointsHierarchy)
    {}
    SLATE_END_ARGS()

    // Construct the widget
    void Construct(const FArguments& InArgs, TSharedPtr<FCELootPointsEditorToolkit> InEditorToolkit);

    // Set the asset being edited
    void SetAsset(UCELootPoints* InAsset);

    // Refresh the hierarchy
    void RefreshHierarchy();

    // Select an item
    void SelectItem(int32 ItemIndex);

    // Get the selected item index
    int32 GetSelectedItemIndex() const;

    // Add a container
    void AddContainer(EContainerType ContainerType);

    // Add a point to the selected container
    void AddPoint();

    // Remove the selected item
    void RemoveSelectedItem();

private:
    // The editor toolkit
    TWeakPtr<FCELootPointsEditorToolkit> EditorToolkit;

    // The asset being edited
    UCELootPoints* Asset;

    // The tree view widget
    TSharedPtr<STreeView<TSharedPtr<FCELootPointsHierarchyItem>>> TreeView;

    // Root items for the tree
    TArray<TSharedPtr<FCELootPointsHierarchyItem>> RootItems;

    // Currently selected item
    TSharedPtr<FCELootPointsHierarchyItem> SelectedItem;

    // Map to store expansion state of items
    TMap<int32, bool> ExpandedItems;

    // Build the hierarchy from the asset
    void BuildHierarchy();

    // Generate a row for the tree view
    TSharedRef<ITableRow> GenerateTreeRow(TSharedPtr<FCELootPointsHierarchyItem> Item, const TSharedRef<STableViewBase>& OwnerTable);

    // Get children for a tree item
    void GetChildrenForTree(TSharedPtr<FCELootPointsHierarchyItem> Item, TArray<TSharedPtr<FCELootPointsHierarchyItem>>& OutChildren);

    // Handle selection changed
    void OnSelectionChanged(TSharedPtr<FCELootPointsHierarchyItem> Item, ESelectInfo::Type SelectInfo);

    // Context menu
    TSharedPtr<SWidget> OnContextMenuOpening();

    // Context menu handlers
    FReply OnAddContainer(EContainerType ContainerType);
    FReply OnAddPoint();
    FReply OnRemoveItem();

    // Check if the selected item is a container
    bool IsContainerSelected() const;

    // Check if the selected item can be removed
    bool CanRemoveSelectedItem() const;
};
