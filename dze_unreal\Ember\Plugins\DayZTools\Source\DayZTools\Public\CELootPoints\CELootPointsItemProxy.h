// CELootPointsItemProxy.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CELootPoints/CELootPoints.h"
#include "CELootPointsItemProxy.generated.h"

/**
 * Proxy object for editing CELootPoints items in the details panel
 */
UCLASS()
class DAYZTOOLS_API UCELootPointsItemProxy : public UObject
{
    GENERATED_BODY()

public:
    // The loot points asset being edited
    UPROPERTY()
    UCELootPoints* LootPointsAsset;

    // The index of the item being edited
    UPROPERTY()
    int32 ItemIndex;

    // Root data (valid if item is root)
    UPROPERTY(EditAnywhere, Category = "Root Settings", meta = (EditCondition = "bIsRoot", EditConditionHides))
    TArray<TSoftObjectPtr<UCEUsage>> Usage;

    UPROPERTY(EditAnywhere, Category = "Root Settings", meta = (EditCondition = "bIsRoot", EditConditionHides))
    TSoftObjectPtr<UConfigClass> Type;

    UPROPERTY(EditAnywhere, Category = "Root Settings", meta = (EditCondition = "bIsRoot", EditConditionHides, ClampMin = "0"))
    int32 RootLootMax;

    // Container data (valid if item is container)
    UPROPERTY(EditAnywhere, Category = "Container Settings", meta = (EditCondition = "bIsContainer", EditConditionHides))
    EContainerType ContainerType;

    UPROPERTY(EditAnywhere, Category = "Container Settings", meta = (EditCondition = "bIsContainer", EditConditionHides, ClampMin = "0"))
    int32 LootMax;

    UPROPERTY(EditAnywhere, Category = "Container Settings", meta = (EditCondition = "bIsContainer", EditConditionHides))
    TArray<TSoftObjectPtr<UCECategory>> Category;

    UPROPERTY(EditAnywhere, Category = "Container Settings", meta = (EditCondition = "bIsContainer", EditConditionHides))
    TArray<TSoftObjectPtr<UCETag>> Tag;

    // Point data (valid if item is point)
    UPROPERTY(EditAnywhere, Category = "Transform", meta = (EditCondition = "bIsPoint", EditConditionHides))
    FVector Location;

    // Scale (uniform X/Y, independent Z)
    UPROPERTY(EditAnywhere, Category = "Transform", meta = (EditCondition = "bIsPoint", EditConditionHides, ClampMin = "0.0001"))
    float ScaleXY = 1.0f;

    UPROPERTY(EditAnywhere, Category = "Transform", meta = (EditCondition = "bIsPoint", EditConditionHides, ClampMin = "0.0001"))
    float ScaleZ = 1.0f;

    // Force spawn flag (valid if item is point)
    UPROPERTY(EditAnywhere, Category = "Point Settings", meta = (EditCondition = "bIsPoint", EditConditionHides))
    bool bForceSpawn = false;

    // Flags to control property visibility (hidden from UI)
    UPROPERTY(Transient)
    bool bIsRoot;

    UPROPERTY(Transient)
    bool bIsContainer;

    UPROPERTY(Transient)
    bool bIsPoint;

    // Initialize from an item in the asset
    void Initialize(UCELootPoints* InAsset, int32 InItemIndex);

    // Apply changes back to the asset
    void ApplyChanges();

#if WITH_EDITOR
    // Property changed callback
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
#endif
};
