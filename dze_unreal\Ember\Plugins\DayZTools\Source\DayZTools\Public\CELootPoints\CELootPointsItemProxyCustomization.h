// CELootPointsItemProxyCustomization.h
#pragma once

#include "CoreMinimal.h"
#include "IDetailCustomization.h"

/**
 * Custom details panel for the CELootPoints Item Proxy
 * Ensures properties are displayed without checkboxes
 */
class FCELootPointsItemProxyCustomization : public IDetailCustomization
{
public:
    static TSharedRef<IDetailCustomization> MakeInstance();
    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;
};
