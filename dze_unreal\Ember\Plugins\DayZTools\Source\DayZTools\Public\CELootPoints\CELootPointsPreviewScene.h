// CELootPointsPreviewScene.h
#pragma once

#include "CoreMinimal.h"
#include "AdvancedPreviewScene.h"
#include "CELootPoints/CELootPoints.h"

class ACELootPoint;
class UStaticMeshComponent;
class UStaticMesh;
class UMaterial;
class UChildActorComponent;
class AC<PERSON>ootPointsComponentOwner;
class USkyLightComponent;
class UDirectionalLightComponent;

/**
 * Preview scene for the CELootPoints editor
 */
class FCELootPointsPreviewScene : public FAdvancedPreviewScene
{
public:
    // Constructor
    FCELootPointsPreviewScene(const ConstructionValues& CVS);

    // Destructor
    virtual ~FCELootPointsPreviewScene();

    // Set the asset being previewed
    void SetAsset(UCELootPoints* InAsset);

    // Refresh the scene
    void RefreshScene();

    // Select an item
    void SelectItem(int32 ItemIndex);

    // Get the selected item index
    int32 GetSelectedItemIndex() const { return SelectedItemIndex; }

    // Get a specific loot point actor by index
    ACELootPoint* GetLootPointActor(int32 ItemIndex) const;

    // Get all loot point actors
    const TArray<ACELootPoint*>& GetLootPointActors() const { return LootPointActors; }

    // Toggle ground plane visibility
    void ToggleGroundPlane(bool bVisible);

    // Check if ground plane is visible
    bool IsGroundPlaneVisible() const;

    // Handle transform update from the viewport
    void HandleTransformUpdate(ACELootPoint* LootPoint, const FTransform& NewTransform);

    void SetEditorToolkit(class FCELootPointsEditorToolkit* InEditorToolkit) { EditorToolkit = InEditorToolkit; }

    // Get the asset being previewed
    UCELootPoints* GetAsset() const { return Asset; }

    // Get a specific child actor component by index
    UChildActorComponent* GetChildActorComponent(int32 Index) const;

    // Get all child actor components
    const TArray<UChildActorComponent*>& GetChildActorComponents() const { return ChildActorComponents; }

    // Update a child's transform
    void UpdateChildTransform(int32 Index, const FTransform& NewTransform);

    // Update the P3D model from the root item's Type property
    // Returns true if the P3D model was successfully updated
    bool UpdateP3DModelFromRootType();


private:
    // The asset being previewed
    UCELootPoints* Asset;

    class FCELootPointsEditorToolkit* EditorToolkit = nullptr;

    // The selected item index
    int32 SelectedItemIndex;

    // The loot point actors
    TArray<ACELootPoint*> LootPointActors;

    // Array of child actor components
    TArray<UChildActorComponent*> ChildActorComponents;

    // Map to store scale information for each item index
    TMap<int32, FVector> ItemScales;

    // Root actor that owns all components
    ACELootPointsComponentOwner* RootOwnerActor;

    // The floor mesh component
    UStaticMeshComponent* FloorMeshComponent;

    // The P3D model component for the root item's Type
    UChildActorComponent* P3DModelComponent;

    // The preview world
    UWorld* PreviewWorld;

    // Lighting components
    UDirectionalLightComponent* DirectionalLight;
    USkyLightComponent* SkyLight;

    // Create loot point actors
    void CreateLootPointActors();

    // Clear all loot point actors
    void ClearLootPointActors();

    // Update loot point transforms
    void UpdateLootPointTransforms();

    // Setup the floor mesh
    void SetupFloorMesh();

    // Update actor selection state
    void UpdateActorSelectionState();

    // Create a child actor component
    UChildActorComponent* CreateChildActorComponent(TSubclassOf<ACELootPoint> ActorClass, const FTransform& Transform);

    // Create a child actor component from a P3D Blueprint
    UChildActorComponent* CreateChildActorFromP3DBlueprint(UP3DBlueprint* Blueprint, const FTransform& Transform);
};
