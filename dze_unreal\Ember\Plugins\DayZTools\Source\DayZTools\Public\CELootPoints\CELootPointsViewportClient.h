// CELootPointsViewportClient.h
#pragma once

#include "CoreMinimal.h"
#include "EditorViewportClient.h"
#include "UnrealWidget.h"
#include "HitProxies.h"
#include "InputState.h"
#include "Toolkits/IToolkitHost.h"

class FCELootPointsPreviewScene;
class FCELootPointsEditorToolkit;
class SCELootPointsEditorViewport;
class FCanvas;
class FSceneView;
class FViewport;
class HHitProxy;
struct FInputKeyEventArgs;
struct FInputEventState;

// Define a hit proxy for CELootPoint actors
struct HCELootPointProxy : public HHitProxy
{
    DECLARE_HIT_PROXY();

    int32 ItemIndex;

    HCELootPointProxy(int32 InItemIndex) : HHitProxy(HPP_World), ItemIndex(InItemIndex) {}

    virtual EMouseCursor::Type GetMouseCursor() override { return EMouseCursor::Crosshairs; }
};

/**
 * Viewport client for the CELootPoints editor
 */
class FCELootPointsViewportClient : public FEditorViewportClient
{
public:
    // Constructor
    FCELootPointsViewportClient(
        const TSharedRef<IToolkitHost>& InToolkitHost,
        FCELootPointsPreviewScene* InPreviewScene,
        const TSharedRef<SEditorViewport>& InViewportWidget,
        FCELootPointsEditorToolkit* InEditorToolkit = nullptr
    );

    // FEditorViewportClient interface
    virtual void Tick(float DeltaSeconds) override;
    virtual void Draw(FViewport* InViewport, FCanvas* Canvas) override;
    virtual bool InputKey(const FInputKeyEventArgs& EventArgs) override;
    virtual bool InputAxis(FViewport* Viewport, FInputDeviceId DeviceID, FKey Key, float Delta, float DeltaTime, int32 NumSamples = 1, bool bGamepad = false) override;
    virtual void ProcessClick(FSceneView& View, HHitProxy* HitProxy, FKey Key, EInputEvent Event, uint32 HitX, uint32 HitY) override;
    virtual bool InputWidgetDelta(FViewport* Viewport, EAxisList::Type CurrentAxis, FVector& Drag, FRotator& Rot, FVector& Scale) override;
    virtual void TrackingStarted(const FInputEventState& InInputState, bool bIsDragging, bool bNudge) override;
    virtual void TrackingStopped() override;
    virtual bool ShouldOrbitCamera() const override;

    // Gizmo related overrides
    virtual FVector GetWidgetLocation() const override;
    virtual FMatrix GetWidgetCoordSystem() const override;

    // Focus the viewport on the bounds of all children
    void FocusViewportOnBounds(const FBoxSphereBounds& Bounds, bool bInstant = false);

    // Get/Set the selected item index
    int32 GetSelectedItemIndex() const { return SelectedItemIndex; }
    void SetSelectedItemIndex(int32 InItemIndex);

    // Get all children bounds
    FBoxSphereBounds GetAllChildrenBounds() const;

private:
    // The preview scene
    FCELootPointsPreviewScene* PreviewScenePtr;

    // The editor toolkit
    FCELootPointsEditorToolkit* EditorToolkit;

    // Whether we should focus on bounds next tick
    bool bShouldFocusOnBounds;

    // The currently selected item index
    int32 SelectedItemIndex;

    // Whether we're currently manipulating a widget
    bool bIsManipulating;
};
