// CECategory.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CECategory.generated.h"

/**
 * CE Category asset for DayZ Central Economy configuration
 * Simple asset type to represent categories in the CE Type system
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UCECategory : public UObject
{
    GENERATED_BODY()

public:
    UCECategory(const FObjectInitializer& ObjectInitializer);
};
