// CECategoryFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CECategoryFactory.generated.h"

UCLASS()
class DAYZTOOLS_API UCECategoryFactory : public UFactory
{
    GENERATED_BODY()

public:
    UCECategoryFactory();

    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
