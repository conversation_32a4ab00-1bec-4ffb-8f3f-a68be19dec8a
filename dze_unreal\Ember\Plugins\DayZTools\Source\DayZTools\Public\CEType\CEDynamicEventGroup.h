// CEDynamicEventGroup.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "P3DBlueprint.h"
#include "ConfigClass/ConfigClass.h"
#include "CEDynamicEventGroup.generated.h"

// Child item structure
USTRUCT(BlueprintType)
struct FCEDynamicEventGroupChild
{
    GENERATED_USTRUCT_BODY()

    // ConfigClass asset reference instead of direct P3D reference, but keep the name as "Type"
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings", meta = (DisplayName = "Type"))
    TSoftObjectPtr<UConfigClass> Type;

    // Whether to disable loot for this child
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings")
    bool bDeloot = false;

    // Loot min and max values
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings", meta = (ClampMin = "0"))
    int32 LootMin = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings", meta = (ClampMin = "0"))
    int32 LootMax = 3;

    // Position coordinates
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings")
    float X = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings")
    float Z = 0.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings")
    float Y = 0.0f;

    // Rotation angle (in degrees)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings")
    float A = 0.0f;
    
    // Helper function to get the P3D model from ConfigClass
    UP3DBlueprint* GetModel() const
    {
        if (Type.IsValid())
        {
            return Type.Get()->Model.Get();
        }
        return nullptr;
    }
};

/**
 * CE Dynamic Event Group asset
 * Used to define a group of objects that can be placed in the world
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UCEDynamicEventGroup : public UObject
{
    GENERATED_BODY()

public:
    UCEDynamicEventGroup(const FObjectInitializer& ObjectInitializer);

    // Group name (for XML export)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Event Group")
    FString GroupName;

    // Array of child items
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Event Group")
    TArray<FCEDynamicEventGroupChild> Children;

    // Export to XML string
    UFUNCTION(BlueprintCallable, Category = "Dynamic Event Group")
    FString ExportToXML() const;
};
