// CEDynamicEventGroupAssetActions.h
#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "CEType/CEDynamicEventGroup.h"

class FCEDynamicEventGroupAssetActions : public FAssetTypeActions_Base
{
public:
    FCEDynamicEventGroupAssetActions(EAssetTypeCategories::Type InAssetCategory);

    // IAssetTypeActions Implementation
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual UClass* GetSupportedClass() const override;
    virtual uint32 GetCategories() override;
    virtual void OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor) override;

private:
    EAssetTypeCategories::Type AssetCategory;
};
