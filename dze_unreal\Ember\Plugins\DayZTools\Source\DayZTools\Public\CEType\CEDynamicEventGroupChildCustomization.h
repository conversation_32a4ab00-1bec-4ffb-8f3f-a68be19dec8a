// CEDynamicEventGroupChildCustomization.h
#pragma once

#include "CoreMinimal.h"
#include "IDetailCustomization.h"

/**
 * Custom details panel for the Dynamic Event Group Child proxy
 * Ensures the Transform category appears at the top of the details panel
 */
class FCEDynamicEventGroupChildCustomization : public IDetailCustomization
{
public:
    static TSharedRef<IDetailCustomization> MakeInstance();
    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;
};
