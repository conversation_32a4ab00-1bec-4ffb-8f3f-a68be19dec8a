// CEDynamicEventGroupChildProxy.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CEType/CEDynamicEventGroup.h"
#include "ConfigClass/ConfigClass.h"
#include "CEDynamicEventGroupChildProxy.generated.h"

/**
 * Proxy object for editing a single child in the CE Dynamic Event Group editor
 * This class represents a single FCEDynamicEventGroupChild struct in the details panel
 */
UCLASS()
class DAYZTOOLS_API UCEDynamicEventGroupChildProxy : public UObject
{
    GENERATED_BODY()

public:
    UCEDynamicEventGroupChildProxy();

    // Initialize the proxy with a reference to the parent asset and the child index
    void Initialize(UCEDynamicEventGroup* InParentAsset, int32 InChildIndex);

    // ConfigClass asset reference but keeping the name as "Type"
    UPROPERTY(EditAnywhere, Category = "Child Settings", meta = (DisplayName = "Type"))
    TSoftObjectPtr<UConfigClass> Type;

    // Whether to disable loot for this child
    UPROPERTY(EditAnywhere, Category = "Child Settings")
    bool bDeloot;

    // Loot min and max values
    UPROPERTY(EditAnywhere, Category = "Child Settings", meta = (ClampMin = "0"))
    int32 LootMin;

    UPROPERTY(EditAnywhere, Category = "Child Settings", meta = (ClampMin = "0"))
    int32 LootMax;

    // Position coordinates
    UPROPERTY(EditAnywhere, Category = "Transform", meta = (DisplayName = "X Location (m)"))
    float X;

    UPROPERTY(EditAnywhere, Category = "Transform", meta = (DisplayName = "Z Location (m)"))
    float Z;

    UPROPERTY(EditAnywhere, Category = "Transform", meta = (DisplayName = "Y Location (m)"))
    float Y;

    // Rotation angle (in degrees)
    UPROPERTY(EditAnywhere, Category = "Transform", meta = (DisplayName = "Yaw Rotation (degrees)"))
    float A;

    // Sync the proxy properties with the actual child struct
    void SyncFromChild();
    void SyncToChild();

    // Get the parent asset and child index
    UCEDynamicEventGroup* GetParentAsset() const { return ParentAsset; }
    int32 GetChildIndex() const { return ChildIndex; }

    // Get the P3D model from the ConfigClass
    UP3DBlueprint* GetModel() const;

    // Called when a property is changed
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;

private:
    // Reference to the parent asset
    UPROPERTY()
    UCEDynamicEventGroup* ParentAsset;

    // Index of the child in the parent's Children array
    int32 ChildIndex;
};
