// CEDynamicEventGroupEditorCommands.h
#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "EditorStyleSet.h"

class FCEDynamicEventGroupEditorCommands : public TCommands<FCEDynamicEventGroupEditorCommands>
{
public:
    FCEDynamicEventGroupEditorCommands()
        : TCommands<FCEDynamicEventGroupEditorCommands>(
            TEXT("CEDynamicEventGroupEditor"), // Context name
            NSLOCTEXT("Contexts", "CEDynamicEventGroupEditor", "CE Dynamic Event Group Editor"), // Display name
            NAME_None, // No parent
            FAppStyle::GetAppStyleSetName() // Icon Style Set
        )
    {
    }

    virtual void RegisterCommands() override;

    // Command for toggling the ground plane
    TSharedPtr<FUICommandInfo> ToggleGroundPlane;

    // Command for exporting the event group
    TSharedPtr<FUICommandInfo> ExportEventGroup;
};
