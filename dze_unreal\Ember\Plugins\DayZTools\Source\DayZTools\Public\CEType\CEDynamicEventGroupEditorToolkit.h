// CEDynamicEventGroupEditorToolkit.h
#pragma once

#include "CoreMinimal.h"
#include "Toolkits/AssetEditorToolkit.h"
#include "CEType/CEDynamicEventGroup.h"
#include "CEType/CEDynamicEventGroupChildProxy.h"
// #include "Framework/Commands/UICommandList.h" // No longer needed directly here
// #include "CEType/CEDynamicEventGroupEditorCommands.h" // Remove if this is the wrong one
#include "CEType/CEDynamicEventGroupEditorViewportCommands.h" // <--- Include the correct command header

// Forward declarations
class FToolBarBuilder;
class IDetailsView;
class SCEDynamicEventGroupEditorViewport;
class SCEDynamicEventGroupHierarchy;

/**
 * Custom Asset Editor for CE Dynamic Event Group
 */
class FCEDynamicEventGroupEditorToolkit : public FAssetEditorToolkit
{
public:
    FCEDynamicEventGroupEditorToolkit();
    virtual ~FCEDynamicEventGroupEditorToolkit();

    void InitCEDynamicEventGroupEditorToolkit(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UCEDynamicEventGroup* InEventGroupAsset);

    // FAssetEditorToolkit interface
    virtual FName GetToolkitFName() const override;
    virtual FText GetBaseToolkitName() const override;
    virtual FString GetWorldCentricTabPrefix() const override;
    virtual FLinearColor GetWorldCentricTabColorScale() const override;

    // Register and unregister tab spawners
    virtual void RegisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;
    virtual void UnregisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;

    // --- Public Interface ---
    // Select a child in the hierarchy
    void SelectChild(int32 ChildIndex);

    // Handle transform updates from the preview scene
    void HandleTransformUpdate(int32 ChildIndex, const FTransform& NewTransform);

    // Refresh the viewport and hierarchy
    void RefreshEditor();

    // Export event group to XML
    void ExportEventGroup(); // Declaration already exists

protected:
    // Create the editor layout
    TSharedRef<FTabManager::FLayout> CreateEditorLayout();

    // --- New/Updated Protected/Private Methods ---
    // Bind commands to actions
    void BindCommands();

    // Register toolbar extender for custom buttons
    void RegisterToolbarExtender();

    // Build the actual toolbar content via the extender delegate
    void BuildToolbar(FToolBarBuilder& ToolBarBuilder);

    // Command Handlers (can be private or protected)
    void HandleToggleGroundPlane();
    bool IsGroundPlaneVisible() const;

private:
    // The editor asset being edited
    UPROPERTY() // Keep UPROPERTY if needed for GC
    UCEDynamicEventGroup* EventGroupAsset;

    // --- Removed Old Command List ---
    // TSharedPtr<FUICommandList> EditorCommands; // Removed - Use GetToolkitCommands()

    // Tab identifiers
    static const FName ViewportTabID;
    static const FName DetailsTabID;
    static const FName HierarchyTabID;

    // --- Removed Old SetupCommands ---
    // void SetupCommands(); // Removed

    // Functions to create tabs
    TSharedRef<SDockTab> SpawnViewportTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnDetailsTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnHierarchyTab(const FSpawnTabArgs& Args);

    // Reference to the widgets
    TSharedPtr<SCEDynamicEventGroupEditorViewport> ViewportWidget; // Kept forward declaration
    TSharedPtr<SCEDynamicEventGroupHierarchy> HierarchyWidget; // Kept forward declaration
    TSharedPtr<IDetailsView> DetailsView; // Kept forward declaration

    // Selected child index
    int32 SelectedChildIndex;

    // Proxy object for the selected child
    UPROPERTY()
    UCEDynamicEventGroupChildProxy* SelectedChildProxy;

    // Create a proxy object for the selected child
    void CreateChildProxy(int32 ChildIndex);

    // Update the details view to show the selected child
    void UpdateDetailsView();

    // Property change callback
    void OnPropertyChanged(UObject* ObjectBeingModified, FPropertyChangedEvent& PropertyChangedEvent); // Declaration already exists
};