// CEDynamicEventGroupEditorViewport.h
#pragma once

#include "CoreMinimal.h"
#include "SEditorViewport.h"
#include "EditorViewportClient.h"
#include "CEType/CEDynamicEventGroupPreviewScene.h"
#include "CEType/CEDynamicEventGroupViewportClient.h"
#include "Editor/UnrealEd/Public/SCommonEditorViewportToolbarBase.h"
#include "CEType/CEDynamicEventGroupEditorViewportCommands.h"

class UCEDynamicEventGroup;

class SCEDynamicEventGroupEditorViewport : public SEditorViewport, public ICommonEditorViewportToolbarInfoProvider
{
public:
    SLATE_BEGIN_ARGS(SCEDynamicEventGroupEditorViewport) {}
        SLATE_ARGUMENT(TSharedPtr<FCEDynamicEventGroupPreviewScene>, PreviewScene)
        SLATE_ARGUMENT(UCEDynamicEventGroup*, EventGroupAsset)
        SLATE_ARGUMENT(FCEDynamicEventGroupEditorToolkit*, EditorToolkit)
    SLATE_END_ARGS()

    void Construct(const FArguments& InArgs);

    // ICommonEditorViewportToolbarInfoProvider interface
    virtual TSharedRef<SEditorViewport> GetViewportWidget() override;
    virtual TSharedPtr<FExtender> GetExtenders() const override;
    virtual void OnFloatingButtonClicked() override;

    // Update the preview scene when the asset changes
    void UpdatePreview();

    // Get the viewport client
    TSharedPtr<FCEDynamicEventGroupViewportClient> GetViewportClient() const { return ViewportClient; }

    // Get the preview scene
    TSharedPtr<FCEDynamicEventGroupPreviewScene> GetPreviewScene() const { return PreviewScene; }

protected:
    // SEditorViewport interface
    virtual TSharedRef<FEditorViewportClient> MakeEditorViewportClient() override;
    virtual TSharedPtr<SWidget> MakeViewportToolbar() override;

public:
    // Toggle the ground plane visibility
    void ToggleGroundPlaneVisibility();

    // Check if the ground plane is visible
    bool IsGroundPlaneVisible() const;

    // Export the event group
    void ExportEventGroup();

    // Add an extender to the viewport toolbar
    void AddViewportToolbarExtender(TSharedPtr<FExtender> Extender)
    {
        ViewportToolbarExtenders.Add(Extender);
    }



private:
    // The viewport client
    TSharedPtr<FCEDynamicEventGroupViewportClient> ViewportClient;

    // The preview scene
    TSharedPtr<FCEDynamicEventGroupPreviewScene> PreviewScene;

    // The asset being edited
    UCEDynamicEventGroup* EventGroupAsset;

    // The editor toolkit
    FCEDynamicEventGroupEditorToolkit* EditorToolkit;

    // Flag to track ground plane visibility
    bool bShowGroundPlane = true;

    // Array of toolbar extenders
    TArray<TSharedPtr<FExtender>> ViewportToolbarExtenders;
};
