// CEDynamicEventGroupEditorViewportCommands.h
#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "EditorStyleSet.h"

class FCEDynamicEventGroupEditorViewportCommands : public TCommands<FCEDynamicEventGroupEditorViewportCommands>
{
public:
    FCEDynamicEventGroupEditorViewportCommands()
        : TCommands<FCEDynamicEventGroupEditorViewportCommands>(
            TEXT("CEDynamicEventGroupEditorViewport"), // Context name
            NSLOCTEXT("Contexts", "CEDynamicEventGroupEditorViewport", "CE Dynamic Event Group Editor Viewport"), // Display name
            NAME_None, // No parent
            FAppStyle::GetAppStyleSetName() // Icon Style Set
        )
    {
    }

    virtual void RegisterCommands() override;

    // Command for toggling the ground plane
    TSharedPtr<FUICommandInfo> ToggleGroundPlane;

    // Command for exporting the event group
    TSharedPtr<FUICommandInfo> ExportEventGroup;
};
