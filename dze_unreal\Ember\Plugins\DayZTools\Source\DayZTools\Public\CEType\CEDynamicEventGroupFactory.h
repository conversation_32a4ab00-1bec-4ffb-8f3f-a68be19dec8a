// CEDynamicEventGroupFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CEDynamicEventGroupFactory.generated.h"

UCLASS()
class DAYZTOOLS_API UCEDynamicEventGroupFactory : public UFactory
{
    GENERATED_BODY()

public:
    UCEDynamicEventGroupFactory();

    // UFactory interface
    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
