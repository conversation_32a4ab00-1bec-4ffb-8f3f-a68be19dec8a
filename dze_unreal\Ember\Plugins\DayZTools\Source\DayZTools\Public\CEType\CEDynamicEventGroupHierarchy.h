// CEDynamicEventGroupHierarchy.h
#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Widgets/Views/SListView.h"
#include "Widgets/Views/STableRow.h"
#include "Widgets/Input/SButton.h"
#include "Widgets/Layout/SBox.h"
#include "Widgets/Layout/SScrollBox.h"
#include "Widgets/Input/SSearchBox.h"
#include "CEType/CEDynamicEventGroup.h"
#include "ConfigClass/ConfigClass.h"

/**
 * Structure to represent a child item in the hierarchy
 */
struct FCEDynamicEventGroupHierarchyItem
{
    /** The index of this child in the event group */
    int32 ChildIndex;

    /** The name of this child */
    FString DisplayName;

    /** The Config Class asset type of this child */
    TSoftObjectPtr<UConfigClass> Type;

    /** Constructor */
    FCEDynamicEventGroupHierarchyItem(int32 InChildIndex, const FString& InDisplayName, const TSoftObjectPtr<UConfigClass>& InType)
        : ChildIndex(InChildIndex), DisplayName(InDisplayName), Type(InType)
    {
    }
};

/**
 * Widget for displaying and editing the hierarchy of a CE Dynamic Event Group
 */
class SCEDynamicEventGroupHierarchy : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(SCEDynamicEventGroupHierarchy)
    {}
        SLATE_ARGUMENT(UCEDynamicEventGroup*, EventGroupAsset)
    SLATE_END_ARGS()

    /** Constructs the widget */
    void Construct(const FArguments& InArgs);

    /** Refreshes the hierarchy list */
    void RefreshHierarchy();

    /** Gets the selected child index */
    int32 GetSelectedChildIndex() const { return SelectedChildIndex; }

    /** Sets the selected child index */
    void SetSelectedChildIndex(int32 InChildIndex);

    /** Sets the editor toolkit */
    void SetEditorToolkit(class FCEDynamicEventGroupEditorToolkit* InEditorToolkit);

private:
    /** The event group asset being edited */
    UCEDynamicEventGroup* EventGroupAsset;

    /** The list view widget */
    TSharedPtr<SListView<TSharedPtr<FCEDynamicEventGroupHierarchyItem>>> ListView;

    /** The list of hierarchy items */
    TArray<TSharedPtr<FCEDynamicEventGroupHierarchyItem>> HierarchyItems;

    /** The currently selected child index */
    int32 SelectedChildIndex;

    /** Pointer to the editor toolkit */
    class FCEDynamicEventGroupEditorToolkit* EditorToolkit;

    /** Search box for filtering items */
    TSharedPtr<SSearchBox> SearchBox;

    /** Current search filter text */
    FText SearchFilterText;

    /** Generates a row widget for the list view */
    TSharedRef<ITableRow> OnGenerateRow(TSharedPtr<FCEDynamicEventGroupHierarchyItem> Item, const TSharedRef<STableViewBase>& OwnerTable);

    /** Handles selection changes in the list view */
    void OnSelectionChanged(TSharedPtr<FCEDynamicEventGroupHierarchyItem> Item, ESelectInfo::Type SelectInfo);

    /** Creates a context menu for the list view */
    TSharedPtr<SWidget> OnContextMenuOpening();

    /** Handles adding a new child */
    FReply OnAddChildClicked();

    /** Handles removing the selected child */
    void OnRemoveChildClicked();

    /** Handles moving the selected child up */
    void OnMoveChildUpClicked();

    /** Handles moving the selected child down */
    void OnMoveChildDownClicked();

    /** Handles search text changes */
    void OnSearchTextChanged(const FText& InFilterText);

    /** Checks if an item passes the current search filter */
    bool PassesFilter(const TSharedPtr<FCEDynamicEventGroupHierarchyItem>& Item) const;
};
