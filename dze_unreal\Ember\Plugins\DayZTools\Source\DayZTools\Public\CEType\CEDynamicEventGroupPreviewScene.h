// CEDynamicEventGroupPreviewScene.h
#pragma once

#include "CoreMinimal.h"
#include "AdvancedPreviewScene.h"
#include "P3DBlueprint.h"
#include "Components/DirectionalLightComponent.h"
#include "Components/SkyLightComponent.h"
#include "GameFramework/Actor.h" // Include Actor

class UCEDynamicEventGroup;
class UChildActorComponent;

// Forward declaration of our container actor class
class ADynamicEventComponentOwner;

// Forward declaration of the editor toolkit
class FCEDynamicEventGroupEditorToolkit;

class FCEDynamicEventGroupPreviewScene : public FAdvancedPreviewScene
{
public:
    FCEDynamicEventGroupPreviewScene(ConstructionValues CVS);
    virtual ~FCEDynamicEventGroupPreviewScene();

    // Update child actors based on the event group asset
    void UpdateChildActors(UCEDynamicEventGroup* EventGroupAsset);

    // Get a specific child actor component by index
    UChildActorComponent* GetChildActorComponent(int32 Index) const;

    // Get all child actor components
    const TArray<UChildActorComponent*>& GetChildActorComponents() const { return ChildActorComponents; }

    // Update a child's transform
    void UpdateChildTransform(int32 Index, const FTransform& NewTransform);

    // Set the editor toolkit
    void SetEditorToolkit(class FCEDynamicEventGroupEditorToolkit* InEditorToolkit) { EditorToolkit = InEditorToolkit; }

private:
    // Reference to the editor toolkit
    class FCEDynamicEventGroupEditorToolkit* EditorToolkit = nullptr;

public:

    // Get the event group asset being edited
    UCEDynamicEventGroup* GetEventGroupAsset() const { return EventGroupAsset; }

    // Clear all child actors - moved to public
    void ClearChildActors();

private:
    // The event group asset being edited
    UCEDynamicEventGroup* EventGroupAsset;

    // Array of child actor components
    TArray<UChildActorComponent*> ChildActorComponents;

    // Root actor that owns all components
    ADynamicEventComponentOwner* RootOwnerActor;

    // Lighting components
    UDirectionalLightComponent* DirectionalLight;
    USkyLightComponent* SkyLight;

    // Create a child actor from a P3D Blueprint asset
    UChildActorComponent* CreateChildActorFromBlueprint(UP3DBlueprint* Blueprint, const FTransform& Transform);
};