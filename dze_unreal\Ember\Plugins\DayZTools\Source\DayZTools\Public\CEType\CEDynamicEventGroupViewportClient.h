#pragma once

#include "CoreMinimal.h"
#include "EditorViewportClient.h"
#include "InputState.h"
#include "Math/BoxSphereBounds.h"
#include "Templates/SharedPointer.h"
#include "Math/Transform.h"
#include "InputCoreTypes.h"
#include "Math/Axis.h"
#include "Math/Color.h"
#include "Math/Rotator.h"
#include "Math/UnrealMathSSE.h"
#include "Math/Vector.h"
#include "PreviewScene.h"
#include "Templates/SharedPointer.h"
#include "Utils.h"
#include "HitProxies.h"
// #include "GizmoState.h" // Not used

// Forward declarations
class FCEDynamicEventGroupEditorToolkit;
class FCEDynamicEventGroupPreviewScene;
class FCanvas;
class FSceneView;
class FViewport;
class HHitProxy;
class SEditorViewport;
struct FInputKeyEventArgs;
struct FInputEventState; // Forward declare for TrackingStarted
namespace UE::Widget { enum EWidgetMode : int; }

// --- Hit Proxy Definition ---
// Define the Hit Proxy struct HERE, outside the ViewportClient class
struct HCEChildActorProxy : public HHitProxy
{
    DECLARE_HIT_PROXY(); // Declaration macro

    int32 ChildIndex;

    HCEChildActorProxy(int32 InChildIndex) : HHitProxy(HPP_World), ChildIndex(InChildIndex) {}

    virtual EMouseCursor::Type GetMouseCursor() override { return EMouseCursor::Crosshairs; }
};
// --- End Hit Proxy Definition ---

/**
 * Custom viewport client for editing DynamicEventGroup assets.
 */
class FCEDynamicEventGroupViewportClient : public FEditorViewportClient
{
public:
    /**
     * Constructor.
     * @param InToolkitHost         Host for toolkit commands/UI.
     * @param InPreviewScene        Preview scene to render.
     * @param InViewportWidget      Slate viewport widget.
     * @param InEditorToolkit       Optional owning editor toolkit pointer.
     */
    FCEDynamicEventGroupViewportClient(
        const TSharedRef<IToolkitHost>& InToolkitHost,
        FCEDynamicEventGroupPreviewScene* InPreviewScene,
        const TSharedRef<SEditorViewport>& InViewportWidget,
        FCEDynamicEventGroupEditorToolkit* InEditorToolkit = nullptr
    );

    
    // --- End Hit Proxy Definition ---

    //~ Begin FEditorViewportClient Interface
    virtual void Tick(float DeltaSeconds) override;
    virtual bool InputKey(const FInputKeyEventArgs& EventArgs) override;
    virtual bool InputAxis(FViewport* InViewport, FInputDeviceId DeviceId, FKey Key, float Delta, float DeltaTime, int32 NumSamples = 1, bool bGamepad = false) override;
    virtual void Draw(FViewport* InViewport, FCanvas* Canvas) override;
    virtual bool ShouldOrbitCamera() const override;
    virtual void ProcessClick(FSceneView& View, HHitProxy* HitProxy, FKey Key, EInputEvent Event, uint32 HitX, uint32 HitY) override;
    virtual bool CanCycleWidgetMode() const override { return SelectedChildIndex != INDEX_NONE; }

    // Gizmo related overrides
    virtual FVector GetWidgetLocation() const override;
    virtual FMatrix GetWidgetCoordSystem() const override;
    virtual bool InputWidgetDelta(FViewport* InViewport, EAxisList::Type CurrentAxis, FVector& Drag, FRotator& Rot, FVector& Scale) override;

    // Use TrackingStarted/Stopped instead of Begin/EndTransform
    virtual void TrackingStarted(const struct FInputEventState& InInputState, bool bIsDraggingWidget, bool bNudge) override;
    virtual void TrackingStopped() override;
    //~ End FEditorViewportClient Interface

    /** Move camera to frame given bounds. */
    void FocusViewportOnBounds(const FBoxSphereBounds& Bounds, bool bInstant = false);

    // Selection Management
    int32 GetSelectedChildIndex() const { return SelectedChildIndex; }
    void SetSelectedChildIndex(int32 InChildIndex);

private:
    /** Calculates the combined bounds of all valid child actors in the preview scene. */
    FBoxSphereBounds GetAllChildrenBounds() const;

    // Pointers to related objects
    FCEDynamicEventGroupPreviewScene* PreviewScenePtr;
    FCEDynamicEventGroupEditorToolkit* EditorToolkit;

    // State variables
    bool bShouldFocusOnBounds;
    int32 SelectedChildIndex;
    bool bIsManipulating; // Still needed to track state between TrackingStarted/Stopped and InputWidgetDelta
};

