// CEItemDefinitionEditor.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CEType/CEType.h"
#include "CEType/CECategory.h"
#include "CEItemDefinitionEditor.generated.h"

// Bulk edit operation type
UENUM(BlueprintType)
enum class EBulkEditOperation : uint8
{
    Multiply UMETA(DisplayName = "Multiply by Factor"),
    Add UMETA(DisplayName = "Add Fixed Value"),
    Set UMETA(DisplayName = "Set to Fixed Value")
};

/**
 * CE Item Definition Editor asset for DayZ Central Economy configuration
 * Used to load and edit multiple CE Type assets at once in a spreadsheet-like interface
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UCEItemDefinitionEditor : public UObject
{
    GENERATED_BODY()

public:
    UCEItemDefinitionEditor(const FObjectInitializer& ObjectInitializer);

    // NOTE: Old Filter Settings parameters removed - now using Content Browser style filtering

    // Internal properties for new filtering system
    // Not exposed to UI, used by our content browser-style filtering
    UPROPERTY()
    TSoftObjectPtr<UCECategory> ActiveFilterCategory;

    UPROPERTY()
    TSoftObjectPtr<UCEUsage> ActiveFilterUsage;

    UPROPERTY()
    TSoftObjectPtr<UCEValue> ActiveFilterValue;

    UPROPERTY()
    FString SearchText;

    // Bulk edit settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bulk Edit Settings", meta = (ClampMin = "0.1", ClampMax = "10.0", UIMin = "0.1", UIMax = "10.0",
        ToolTip = "Multiply mode: Multiplier for Nominal values. Add mode: Value to add (x10). Set mode: Value to set (x10)."))
    float NominalMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bulk Edit Settings", meta = (ClampMin = "0.1", ClampMax = "10.0", UIMin = "0.1", UIMax = "10.0",
        ToolTip = "Multiply mode: Multiplier for Min values. Add mode: Value to add (x10). Set mode: Value to set (x10)."))
    float MinMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bulk Edit Settings", meta = (ClampMin = "0.1", ClampMax = "10.0", UIMin = "0.1", UIMax = "10.0",
        ToolTip = "Multiply mode: Multiplier for Lifetime values. Add mode: Value to add (x100). Set mode: Value to set (x1000)."))
    float LifetimeMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bulk Edit Settings", meta = (ClampMin = "0.1", ClampMax = "10.0", UIMin = "0.1", UIMax = "10.0",
        ToolTip = "Multiply mode: Multiplier for Restock values. Add mode: Value to add (x10). Set mode: Value to set (x100)."))
    float RestockMultiplier = 1.0f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Bulk Edit Settings", meta = (ToolTip = "Multiply: Use values as multipliers. Add: Add fixed values. Set: Set to fixed values."))
    EBulkEditOperation BulkEditOperation = EBulkEditOperation::Multiply;

    // Functions to load and filter CE Type assets
    UFUNCTION(BlueprintCallable, Category = "CE Item Definition Editor")
    TArray<UCEType*> LoadAllCETypeAssets();

    UFUNCTION(BlueprintCallable, Category = "CE Item Definition Editor")
    TArray<UCEType*> FilterCETypeAssets(const TArray<UCEType*>& Assets);

    // Function to apply bulk edits
    UFUNCTION(BlueprintCallable, Category = "CE Item Definition Editor")
    void ApplyBulkEdit(const TArray<UCEType*>& Assets);

    // Function to save changes to assets
    UFUNCTION(BlueprintCallable, Category = "CE Item Definition Editor")
    void SaveChanges(const TArray<UCEType*>& Assets);
};
