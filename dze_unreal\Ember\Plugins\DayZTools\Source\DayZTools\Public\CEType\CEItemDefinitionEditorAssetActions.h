// CEItemDefinitionEditorAssetActions.h
#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "CEType/CEItemDefinitionEditor.h"

class FCEItemDefinitionEditorAssetActions : public FAssetTypeActions_Base
{
public:
    FCEItemDefinitionEditorAssetActions(EAssetTypeCategories::Type InAssetCategory);

    // IAssetTypeActions Implementation
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual UClass* GetSupportedClass() const override;
    virtual uint32 GetCategories() override;
    virtual void OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor) override;

private:
    EAssetTypeCategories::Type AssetCategory;
};
