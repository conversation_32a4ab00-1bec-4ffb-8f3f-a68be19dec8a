// CEItemDefinitionEditorFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CEItemDefinitionEditorFactory.generated.h"

/**
 * Factory for creating CE Item Definition Editor assets
 */
UCLASS()
class DAYZTOOLS_API UCEItemDefinitionEditorFactory : public UFactory
{
    GENERATED_BODY()

public:
    UCEItemDefinitionEditorFactory();

    // UFactory interface
    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
