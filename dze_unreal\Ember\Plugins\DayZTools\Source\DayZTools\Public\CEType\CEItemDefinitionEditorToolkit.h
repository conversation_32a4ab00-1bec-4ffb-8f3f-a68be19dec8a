// CEItemDefinitionEditorToolkit.h
#pragma once

#include "CoreMinimal.h"
#include "Toolkits/AssetEditorToolkit.h"
#include "CEType/CEItemDefinitionEditor.h"
#include "CEType/CEType.h"
#include "Widgets/Views/STableRow.h"
#include "Widgets/Views/STableViewBase.h"

class SSearchBox;  // Forward declaration for search box
class FMenuBuilder; // Forward declaration for menu builder

// Struct to represent an active filter
struct FActiveFilter
{
    enum class EFilterType
    {
        Category,
        Usage,
        Value
    };

    FActiveFilter(EFilterType InType, FName InName, TSoftObjectPtr<UObject> InAsset)
        : Type(InType), Name(InName), Asset(InAsset), bEnabled(true)
    {}

    EFilterType Type;
    FName Name;
    TSoftObjectPtr<UObject> Asset;
    bool bEnabled;

    bool operator==(const FActiveFilter& Other) const
    {
        return Type == Other.Type && Name == Other.Name;
    }
};

/**
 * Custom Asset Editor for CE Item Definition Editor
 */
class FCEItemDefinitionEditorToolkit : public FAssetEditorToolkit
{
public:
    FCEItemDefinitionEditorToolkit();
    virtual ~FCEItemDefinitionEditorToolkit();

    void InitCEItemDefinitionEditorToolkit(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UCEItemDefinitionEditor* InEditorAsset);

    // FAssetEditorToolkit interface
    virtual FName GetToolkitFName() const override;
    virtual FText GetBaseToolkitName() const override;
    virtual FString GetWorldCentricTabPrefix() const override;
    virtual FLinearColor GetWorldCentricTabColorScale() const override;

    // Register and unregister tab spawners
    virtual void RegisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;
    virtual void UnregisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;

protected:
    // Create the editor layout
    TSharedRef<FTabManager::FLayout> CreateEditorLayout();

    // Refresh the item list
    void RefreshItemList();

    // Apply bulk edits
    void ApplyBulkEdits();

    // Save changes
    void SaveChanges();

    // Filter items
    void FilterItems();

    // Export types to XML
    void ExportTypesToXML();

    // Handle search text changes
    void OnSearchTextChanged(const FText& InSearchText);

    // Handle filter-related functions
    void AddCategoryFilter(TSoftObjectPtr<UCECategory> Category);
    void AddUsageFilter(TSoftObjectPtr<UCEUsage> Usage);
    void AddValueFilter(TSoftObjectPtr<UCEValue> Value);
    void ToggleFilterState(int32 FilterIndex);
    void RemoveFilter(int32 FilterIndex);
    void ClearAllFilters();
    TSharedRef<SWidget> MakeFilterMenu();
    void MakeCategoryFilterSubmenu(FMenuBuilder& SubMenuBuilder);
    void MakeUsageFilterSubmenu(FMenuBuilder& SubMenuBuilder);
    void MakeValueFilterSubmenu(FMenuBuilder& SubMenuBuilder);
    
    // Apply active filters to filter settings
    void ApplyActiveFiltersToSettings();

private:
    // The editor asset being edited
    UCEItemDefinitionEditor* EditorAsset;

    // Tab identifiers
    static const FName ItemsTabID;
    static const FName DetailsTabID;
    static const FName FiltersTabID;
    static const FName BulkEditTabID;

    // Functions to create tabs
    TSharedRef<SDockTab> SpawnItemsTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnDetailsTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnFiltersTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnBulkEditTab(const FSpawnTabArgs& Args);

    // List of loaded CE Type assets
    TArray<UCEType*> LoadedItems;
    TArray<UCEType*> FilteredItems;

    // Selected items
    TArray<UCEType*> SelectedItems;

    // Sorting
    EColumnSortMode::Type SortMode;
    FName SortColumn;

    // Search text
    FString SearchText;
    TSharedPtr<SSearchBox> SearchBox;

    // Active filters
    TArray<FActiveFilter> ActiveFilters;
    TSharedPtr<SWidget> FiltersPanel;
    TSharedPtr<SBorder> FiltersPanelContainer; // Changed from SWidget to SBorder for proper SetContent access

    // UI elements
    TSharedPtr<SListView<UCEType*>> ItemListView;
    TSharedPtr<IDetailsView> ItemDetailsView;
    TSharedPtr<IDetailsView> FiltersView;
    TSharedPtr<IDetailsView> BulkEditView;
    TSharedPtr<STextBlock> StatusTextBlock;

    // Status message
    FText StatusMessage;

    // Generate a row for the list view
    TSharedRef<ITableRow> OnGenerateRow(UCEType* Item, const TSharedRef<STableViewBase>& OwnerTable);

    // Handle selection change
    void OnSelectionChanged(UCEType* Item, ESelectInfo::Type SelectInfo);

    // Handle column sorting
    void OnColumnSortModeChanged(const EColumnSortPriority::Type SortPriority, const FName& ColumnId, const EColumnSortMode::Type InSortMode);

    // Sort items
    void SortItems();

    // Create a details view
    TSharedRef<IDetailsView> CreateDetailsView(UObject* Object, bool bAllowSearch = true);

    // Rebuild the filters panel based on active filters
    void RebuildFiltersPanel();
};
