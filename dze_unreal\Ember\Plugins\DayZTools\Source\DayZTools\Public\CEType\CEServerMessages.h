#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CEServerMessages.generated.h"

/**
 * Server message structure
 * Used for server announcements and messages
 */
USTRUCT(BlueprintType)
struct FServerMessage
{
    GENERATED_BODY()

    // Text with placeholders: #name, #port, #tmin
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Message", meta = (DisplayName = "Text", ToolTip = "Maximum 160 characters. Use #name, #port, and #tmin as placeholders"))
    FString Text;

    // Flags
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Message")
    bool bOnConnect = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Message")
    bool bRepeat = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Message")
    bool bCountdown = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Message", meta = (EditCondition = "bCountdown"))
    bool bShutdown = false;

    // Properties (all time values in minutes)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Message", meta = (EditCondition = "bOnConnect", UIMin = "0", ClampMin = "0", ToolTip = "How long to wait after player connects before displaying message (minutes)"))
    int32 Delay = 0;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Message", meta = (EditCondition = "bRepeat", UIMin = "1", ClampMin = "1", ToolTip = "How frequently to repeat the message (minutes)"))
    int32 RepeatTime = 5;
    
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Message", meta = (EditCondition = "bCountdown", UIMin = "1", ClampMin = "1", ToolTip = "How long until countdown reaches zero (minutes)"))
    int32 Deadline = 60;
};

/**
 * Server Messages asset for DayZ server messages configuration
 * Replaces XML-based server messages configuration with an editable asset
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UCEServerMessages : public UObject
{
    GENERATED_BODY()

public:
    UCEServerMessages(const FObjectInitializer& ObjectInitializer);

    // Messages array
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Server Messages")
    TArray<FServerMessage> Messages;

    // Export to XML string
    UFUNCTION(BlueprintCallable, Category = "Server Messages")
    FString ExportToXML() const;
};