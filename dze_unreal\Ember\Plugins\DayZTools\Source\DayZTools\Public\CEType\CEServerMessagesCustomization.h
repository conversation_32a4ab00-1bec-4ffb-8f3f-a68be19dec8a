#pragma once

#include "CoreMinimal.h"
#include "IDetailCustomization.h"

class FCEServerMessagesCustomization : public IDetailCustomization
{
public:
	static TSharedRef<IDetailCustomization> MakeInstance();
	virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;

private:
	TWeakObjectPtr<class UCEServerMessages> ServerMessagesAsset;

	FReply OnExportToXMLClicked();
	FReply OnAddMessageClicked();
	FReply OnRemoveMessageClicked(int32 MessageIndex);
};