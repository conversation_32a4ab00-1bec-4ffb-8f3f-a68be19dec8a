#pragma once

#include "CoreMinimal.h"
#include "Toolkits/AssetEditorToolkit.h"
#include "Widgets/Notifications/SNotificationList.h"

class UCEServerMessages;

class FCEServerMessagesEditorToolkit : public FAssetEditorToolkit
{
public:
	FCEServerMessagesEditorToolkit();
	virtual ~FCEServerMessagesEditorToolkit();

	// IToolkit interface
	virtual void RegisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;
	virtual void UnregisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;
	// End of IToolkit interface

	// FAssetEditorToolkit interface
	virtual FName GetToolkitFName() const override;
	virtual FText GetBaseToolkitName() const override;
	virtual FText GetToolkitName() const override;
	virtual FText GetToolkitToolTipText() const override;
	virtual FString GetWorldCentricTabPrefix() const override;
	virtual FLinearColor GetWorldCentricTabColorScale() const override;
	virtual bool OnRequestClose() override;
	// End of FAssetEditorToolkit interface

	// Initializes the Server Messages editor
	void Initialize(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, UCEServerMessages* InServerMessages);

	// Export the server messages to XML file
	void ExportServerMessages();

	// Shows the XML export in a dialog
	void ShowXMLExport();

private:
	// The server messages asset being edited
	UCEServerMessages* ServerMessagesAsset;

	// Notification list for feedback to the user
	TSharedPtr<SNotificationList> NotificationList;
	
	// Tab IDs
	static const FName PropertiesTabId;
	static const FName XMLPreviewTabId;
	
	// Create the preview tab
	TSharedRef<SDockTab> SpawnTab_XMLPreview(const FSpawnTabArgs& Args);
	
	// Updates the XML preview text
	void UpdateXMLPreview();
	
	// The text widget that displays the XML preview
	TSharedPtr<class SMultiLineEditableTextBox> XMLPreviewTextBox;
};