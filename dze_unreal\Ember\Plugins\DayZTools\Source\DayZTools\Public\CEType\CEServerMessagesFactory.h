#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CEServerMessagesFactory.generated.h"

/**
 * Factory for creating new Server Messages assets
 */
UCLASS()
class DAYZTOOLS_API UCEServerMessagesFactory : public UFactory
{
	GENERATED_BODY()

public:
	UCEServerMessagesFactory();

	// UFactory interface
	virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
	virtual bool ShouldShowInNewMenu() const override;
};