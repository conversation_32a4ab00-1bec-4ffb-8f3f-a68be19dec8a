// CETagFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CETagFactory.generated.h"

UCLASS()
class DAYZTOOLS_API UCETagFactory : public UFactory
{
    GENERATED_BODY()

public:
    UCETagFactory();

    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
