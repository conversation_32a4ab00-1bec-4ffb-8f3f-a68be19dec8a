// CEType.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CEType/CECategory.h"
#include "CEType/CEUsage.h"
#include "CEType/CETag.h"
#include "CEType/CEValue.h"
#include "ConfigClass/ConfigClass.h"
#include "CEType.generated.h"

// Flag structure for CE Type
USTRUCT(BlueprintType)
struct FCETypeFlags
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flags")
    bool bCountInCargo = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flags")
    bool bCountInHoarder = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flags")
    bool bCountInMap = true;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flags")
    bool bCountInPlayer = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flags")
    bool bCrafted = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Flags")
    bool bDeloot = false;
};

// Usage or Value entry
USTRUCT(BlueprintType)
struct FCETypeEntry
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Entry")
    FString Name;
};

/**
 * CE Type asset for DayZ Central Economy configuration
 * Replaces XML-based type configuration with an editable asset
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UCEType : public UObject
{
    GENERATED_BODY()

public:
    UCEType(const FObjectInitializer& ObjectInitializer);

    // Reference to the Config Class
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings", meta = (DisplayName = "Type"))
    TSoftObjectPtr<UConfigClass> Type;

    // Basic type properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings", meta = (ClampMin = "0"))
    int32 Nominal = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings", meta = (ClampMin = "0"))
    int32 Lifetime = 14400;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings", meta = (ClampMin = "0"))
    int32 Restock = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings", meta = (ClampMin = "0"))
    int32 Min = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings", meta = (ClampMin = "-1"))
    int32 QuantMin = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings", meta = (ClampMin = "-1"))
    int32 QuantMax = -1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings", meta = (ClampMin = "0"))
    int32 Cost = 100;

    // Flags
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings")
    FCETypeFlags Flags;

    // Category
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings")
    TSoftObjectPtr<UCECategory> Category;

    // Usage array
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings|Usage")
    TArray<TSoftObjectPtr<UCEUsage>> Usage;

    // Tag array
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings|Tag")
    TArray<TSoftObjectPtr<UCETag>> Tag;

    // Value array
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Type Settings|Value")
    TArray<TSoftObjectPtr<UCEValue>> Value;

    // Export to XML string
    UFUNCTION(BlueprintCallable, Category = "Type Settings")
    FString ExportToXML() const;
};
