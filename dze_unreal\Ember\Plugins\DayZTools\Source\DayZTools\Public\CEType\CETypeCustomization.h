// CETypeCustomization.h
#pragma once

#include "CoreMinimal.h"
#include "IDetailCustomization.h"

class FCETypeCustomization : public IDetailCustomization
{
public:
    static TSharedRef<IDetailCustomization> MakeInstance();
    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;

private:
    TWeakObjectPtr<class UCEType> CETypeAsset;

    FReply OnExportToXMLClicked();
    
    // Called when the Type property changes
    void OnTypePropertyChanged();
    
    // Attempts to rename the CEType asset to match the Config Class name
    bool AttemptRenameToConfigClass();
};
