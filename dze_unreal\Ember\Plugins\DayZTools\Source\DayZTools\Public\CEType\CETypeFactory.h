// CETypeFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CETypeFactory.generated.h"

UCLASS()
class DAYZTOOLS_API UCETypeFactory : public UFactory
{
    GENERATED_BODY()

public:
    UCETypeFactory();

    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
