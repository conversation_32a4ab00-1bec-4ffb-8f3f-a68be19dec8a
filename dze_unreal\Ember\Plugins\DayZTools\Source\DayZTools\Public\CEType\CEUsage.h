// CEUsage.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CEUsage.generated.h"

/**
 * CE Usage asset for DayZ Central Economy configuration
 * Simple asset type to represent usage types in the CE Type system
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UCEUsage : public UObject
{
    GENERATED_BODY()

public:
    UCEUsage(const FObjectInitializer& ObjectInitializer);
};
