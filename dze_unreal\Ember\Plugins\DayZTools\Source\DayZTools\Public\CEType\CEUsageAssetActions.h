// CEUsageAssetActions.h
#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "CEType/CEUsage.h"

class FCEUsageAssetActions : public FAssetTypeActions_Base
{
public:
    FCEUsageAssetActions(EAssetTypeCategories::Type InAssetCategory);

    // IAssetTypeActions Implementation
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual UClass* GetSupportedClass() const override;
    virtual uint32 GetCategories() override;

private:
    EAssetTypeCategories::Type AssetCategory;
};
