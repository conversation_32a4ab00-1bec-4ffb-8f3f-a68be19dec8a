// CEUsageFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CEUsageFactory.generated.h"

UCLASS()
class DAYZTOOLS_API UCEUsageFactory : public UFactory
{
    GENERATED_BODY()

public:
    UCEUsageFactory();

    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
