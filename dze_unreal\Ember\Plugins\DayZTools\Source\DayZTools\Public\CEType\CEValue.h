// CEValue.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CEValue.generated.h"

/**
 * CE Value asset for DayZ Central Economy configuration
 * Simple asset type to represent values in the CE Type system
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UCEValue : public UObject
{
    GENERATED_BODY()

public:
    UCEValue(const FObjectInitializer& ObjectInitializer);
};
