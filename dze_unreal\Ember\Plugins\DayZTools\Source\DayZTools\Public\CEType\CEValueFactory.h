// CEValueFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "CEValueFactory.generated.h"

UCLASS()
class DAYZTOOLS_API UCEValueFactory : public UFactory
{
    GENERATED_BODY()

public:
    UCEValueFactory();

    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
