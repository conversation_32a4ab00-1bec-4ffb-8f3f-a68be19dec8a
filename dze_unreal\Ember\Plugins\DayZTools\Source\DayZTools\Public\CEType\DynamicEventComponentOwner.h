// DynamicEventComponentOwner.h
#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "DynamicEventComponentOwner.generated.h"

/**
 * Simple actor class that serves as an owner for components in the Dynamic Event Group preview scene
 */
UCLASS()
class DAYZTOOLS_API ADynamicEventComponentOwner : public AActor
{
    GENERATED_BODY()
    
public:    
    // Sets default values for this actor's properties
    ADynamicEventComponentOwner();

    // Called when the game starts or when spawned
    virtual void BeginPlay() override;

    // Called every frame
    virtual void Tick(float DeltaTime) override;
};
