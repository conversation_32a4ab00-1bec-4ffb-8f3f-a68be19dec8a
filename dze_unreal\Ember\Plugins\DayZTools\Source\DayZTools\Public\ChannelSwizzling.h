#pragma once

#include "CoreMinimal.h"
#include <cstdint>
#include <vector>
#include <stdexcept>

enum class TexSwizzle : uint8_t
{
    T<PERSON>lpha,
    TSRed,
    TSGreen,
    TSBlue,
    TSInvAlpha,
    TSInvRed,
    TSInvGreen,
    TSInvBlue,
    TSOne
};

struct ARGBSwizzle
{
    TexSwizzle SwizB;
    TexSwizzle SwizG;
    TexSwizzle SwizR;
    TexSwizzle SwizA;

    TexSwizzle operator[](int ch) const;
    void SetSwizzle(int ch, TexSwizzle value);

    static ARGBSwizzle Default;
};

class ChannelSwizzling
{
public:
    static void Apply(std::vector<uint8_t>& argbPixels, const ARGBSwizzle& swizzle);

private:
    static void InvertSwizzle(ARGBSwizzle& invSwizzle, const ARGBSwizzle& swizzle, uint8_t ch);
    static std::tuple<int, int, int> CheckInvSwizzle(TexSwizzle swiz);
    static void ChannelSwizzle(const ARGBSwizzle& channelSwizzle, std::vector<uint8_t>& argbPixels);
};