// Fill out your copyright notice in the Description page of Project Settings.

#pragma once

#include "CoreMinimal.h"

#include <cstdint>
#include <string>
#include <sstream>
#include <iomanip>
#include <cassert>
#include <cmath>
#include "BinaryReader.h" // Assuming you have a C++ equivalent for BinaryReaderEx

struct ColorP
{
    float Red;
    float Green;
    float Blue;
    float Alpha;

    ColorP(float r, float g, float b, float a)
        : Red(r), Green(g), Blue(b), Alpha(a) {}

    ColorP(FBinaryReader& input)
    {
        Read(input);
    }

    void Read(FBinaryReader& input)
    {
        Red = input.ReadFloat();
        Green = input.ReadFloat();
        Blue = input.ReadFloat();
        Alpha = input.ReadFloat();
    }

    std::string ToString() const
    {
        std::ostringstream oss;
        oss.imbue(std::locale("en_GB.UTF-8"));
        oss << std::fixed << std::setprecision(6);
        oss << "{" << Red << "," << Green << "," << Blue << "," << Alpha << "}";
        return oss.str();
    }
};

struct PackedColor
{
private:
    uint32_t value;

public:
    uint8_t A8() const { return (value >> 24) & 0xff; }
    uint8_t R8() const { return (value >> 16) & 0xff; }
    uint8_t G8() const { return (value >> 8) & 0xff; }
    uint8_t B8() const { return value & 0xff; }

    uint32_t GetValue() const { return value; }

    explicit PackedColor(uint32_t val) : value(val) {}

    PackedColor() : value(0) {}

    PackedColor(uint8_t r, uint8_t g, uint8_t b, uint8_t a = 255)
    {
        value = PackColor(r, g, b, a);
    }

    PackedColor(float r, float g, float b, float a)
    {
        assert(r <= 1.0f && r >= 0 && !std::isnan(r));
        assert(g <= 1.0f && g >= 0 && !std::isnan(g));
        assert(b <= 1.0f && b >= 0 && !std::isnan(b));
        assert(a <= 1.0f && a >= 0 && !std::isnan(a));

        uint8_t r8 = static_cast<uint8_t>(r * 255);
        uint8_t g8 = static_cast<uint8_t>(g * 255);
        uint8_t b8 = static_cast<uint8_t>(b * 255);
        uint8_t a8 = static_cast<uint8_t>(a * 255);

        value = PackColor(r8, g8, b8, a8);
    }

    static uint32_t PackColor(uint8_t r, uint8_t g, uint8_t b, uint8_t a)
    {
        return (static_cast<uint32_t>(a) << 24) | (static_cast<uint32_t>(r) << 16) | (static_cast<uint32_t>(g) << 8) | b;
    }
};
