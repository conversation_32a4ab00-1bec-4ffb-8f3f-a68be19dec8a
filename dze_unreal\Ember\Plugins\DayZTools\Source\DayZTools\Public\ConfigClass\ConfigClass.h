// ConfigClass.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
// ConfigClass.generated.h must be the last include
#include "ConfigClass.generated.h"

/**
 * Config Class asset for DayZ configuration
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UConfigClass : public UObject
{
    GENERATED_BODY()

public:
    UConfigClass(const FObjectInitializer& ObjectInitializer);

    // The model (P3D) associated with this config class
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Config Settings", meta = (DisplayName = "Model", AllowedClasses = "P3DBlueprint"))
    TSoftObjectPtr<class UP3DBlueprint> Model;

    // Export to Config string
    UFUNCTION(BlueprintCallable, Category = "Config Settings")
    FString ExportToConfig() const;
};