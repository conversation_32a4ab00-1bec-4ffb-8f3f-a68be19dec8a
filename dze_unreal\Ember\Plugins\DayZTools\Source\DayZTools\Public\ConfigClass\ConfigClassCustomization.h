// ConfigClassCustomization.h
#pragma once

#include "CoreMinimal.h"
#include "IDetailCustomization.h"

class FConfigClassCustomization : public IDetailCustomization
{
public:
    static TSharedRef<IDetailCustomization> MakeInstance();
    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;

private:
    TWeakObjectPtr<class UConfigClass> ConfigClassAsset;

    FReply OnExportToConfigClicked();
};