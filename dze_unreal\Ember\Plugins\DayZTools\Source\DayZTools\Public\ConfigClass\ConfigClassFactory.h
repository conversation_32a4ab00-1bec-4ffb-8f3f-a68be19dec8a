// ConfigClassFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "ConfigClassFactory.generated.h"

UCLASS()
class DAYZTOOLS_API UConfigClassFactory : public UFactory
{
    GENERATED_BODY()

public:
    UConfigClassFactory();

    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};