#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Framework/Commands/UICommandList.h"
#include "DayZWindowContainer.h"

class DayZGameTab : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(DayZGameTab) {}
    SLATE_END_ARGS()

    void Construct(const FArguments& InArgs);

private:

    // Play modes
    enum class EDayZPlayMode
    {
        Offline,
        Multiplayer
    };

    // Toolbar creation
    TSharedRef<SWidget> MakeToolbar();

    // Menu generation
    TSharedRef<SWidget> GenerateLaunchDayZMenu();

    // Command list
    TSharedPtr<FUICommandList> CommandList;

    // Current play mode
    EDayZPlayMode CurrentPlayMode;

    // Toolbar button actions
    void OnLaunchWorkbench();
    void OnLaunchObjectBuilder();
    void OnLaunchTerrainBuilder();
    void OnBuildPBO();
    void OnPlayButtonClicked();
    void OnSelectPlayMode(EDayZPlayMode SelectedMode);
    bool IsPlayModeChecked(EDayZPlayMode Mode) const;
    void OnLaunchDayZOffline();
    void OnLaunchDayZMultiplayer();
    
};