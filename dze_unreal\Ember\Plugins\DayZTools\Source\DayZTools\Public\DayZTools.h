// DayZTools.h
#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "IAssetTools.h"
#include "DayZGameTab.h"
#include "Assets/TerrainLayers/TerrainLayersAssetTypeActions.h"
#include "FBiomeDataAssetTypeActions.h"

class FRvmatAssetActions;
class FDynamicEventAssetActions;
class FCETypeAssetActions;
class FCECategoryAssetActions;
class FCEUsageAssetActions;
class FCETagAssetActions;
class FCEValueAssetActions;
class FCEItemDefinitionEditorAssetActions;
class FCEDynamicEventGroupAssetActions;
class FConfigClassAssetActions;
class FCELootPointsAssetActions;
class FBiomeDataAssetTypeActions;

class FDayZToolsModule : public IModuleInterface
{
public:
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;

    TSharedRef<class SDockTab> OnSpawnPluginTab(const class FSpawnTabArgs& SpawnTabArgs);


    TSharedPtr<FUICommandList> PluginCommands;

private:
    TSharedPtr<FExtender> BlueprintToolbarExtender;

    /// <summary>
    /// RVMAT STUFF
    /// </summary>
private:
    TSharedPtr<FTerrainLayersAssetTypeActions> TerrainAssetActions;
    TSharedPtr<FRvmatAssetActions> RvmatAssetAction;
    TSharedPtr<IAssetTypeActions> P3DAssetAction;
    TSharedPtr<FDynamicEventAssetActions> DynamicEventAssetAction;
    TSharedPtr<FCETypeAssetActions> CETypeAssetAction;
    TSharedPtr<FCECategoryAssetActions> CECategoryAssetAction;
    TSharedPtr<FCEUsageAssetActions> CEUsageAssetAction;
    TSharedPtr<FCETagAssetActions> CETagAssetAction;
    TSharedPtr<FCEValueAssetActions> CEValueAssetAction;
    TSharedPtr<FCEItemDefinitionEditorAssetActions> CEItemDefinitionEditorAssetAction;
    TSharedPtr<FCEDynamicEventGroupAssetActions> CEDynamicEventGroupAssetAction;
    TSharedPtr<FConfigClassAssetActions> ConfigClassAssetAction;
    TSharedPtr<FCELootPointsAssetActions> CELootPointsAssetAction;
    TSharedPtr<FBiomeDataAssetTypeActions> BiomeDataAssetTypeActions;
    EAssetTypeCategories::Type DayZAssetCategoryBit;

};
