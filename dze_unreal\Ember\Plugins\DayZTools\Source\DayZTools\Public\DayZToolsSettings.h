#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "DayZToolsSettings.generated.h"

USTRUCT(BlueprintType)
struct FDirectoryState
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, Config, Category = "Directory State")
    FString Directory;

    UPROPERTY(EditAnywhere, Config, Category = "Directory State")
    bool bChecked;

    FDirectoryState() : Directory(TEXT("")), bChecked(true) {}
    FDirectoryState(const FString& InDirectory, bool bInChecked)
        : Directory(InDirectory), bChecked(bInChecked) {}

    friend FArchive& operator<<(FArchive& Ar, FDirectoryState& DirectoryState)
    {
        Ar << DirectoryState.Directory;
        Ar << DirectoryState.bChecked;
        return Ar;
    }
};

UCLASS(config = Game, defaultconfig)
class DAYZTOOLS_API UDayZToolsSettings : public UObject
{
    GENERATED_BODY()

public:
    // Remove the constructor if not needed
    UDayZToolsSettings(const FObjectInitializer& ObjectInitializer);

    // Alternatively, implement the default constructor
    //UDayZToolsSettings();


    UPROPERTY(config, EditAnywhere, Category = "Paths", meta = (DisplayName = "Project Trunk Path"))
    FString ProjectTrunkPath;

    UPROPERTY(config, EditAnywhere, Category = "Paths", meta = (DisplayName = "Project Tools Path"))
    FString ProjectToolsPath;

    UPROPERTY(config, EditAnywhere, Category = "Paths", meta = (DisplayName = "Mission Path"))
    FString MissionPath;

    UPROPERTY(config, EditAnywhere, Category = "Paths", meta = (DisplayName = "Default WRP Path", ToolTip = "Path to the WRP file for terrain export operations"))
    FString DefaultWRPPath;

    // Array to store directories and their checked states
    UPROPERTY(Config, EditAnywhere, Category = "Directories")
    TArray<FDirectoryState> SavedDirectories;

    virtual void Serialize(FArchive& Ar) override
    {
        Super::Serialize(Ar);
        Ar << SavedDirectories;
    }

    virtual void PostLoad() override
    {
        Super::PostLoad();
        UE_LOG(LogTemp, Log, TEXT("UDayZToolsSettings PostLoad: Loaded %d directories"), SavedDirectories.Num());
        for (const FDirectoryState& DirState : SavedDirectories)
        {
            UE_LOG(LogTemp, Log, TEXT("Loaded: %s, Checked: %s"), *DirState.Directory, DirState.bChecked ? TEXT("True") : TEXT("False"));
        }
    }
};
