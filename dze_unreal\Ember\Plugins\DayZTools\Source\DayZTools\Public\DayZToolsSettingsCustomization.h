#pragma once

#include "CoreMinimal.h"
#include "IDetailCustomization.h"

class FDayZToolsSettingsCustomization : public IDetailCustomization
{
public:
    static TSharedRef<IDetailCustomization> MakeInstance();

    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;

private:
    static FString OpenDirectoryDialog(const FString& DialogTitle, const FString& DefaultPath);
    FReply OnBrowseButtonClicked(TSharedRef<IPropertyHandle, ESPMode::ThreadSafe> PropertyHandle, const FString& DialogTitle);


};