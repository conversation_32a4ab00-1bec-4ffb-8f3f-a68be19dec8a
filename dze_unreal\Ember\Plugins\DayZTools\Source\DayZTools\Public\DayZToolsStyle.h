#pragma once

#include "CoreMinimal.h"
#include "Styling/SlateStyle.h"
#include "Styling/SlateStyleRegistry.h"

class FDayZToolsStyle
{
public:
    /** Initializes the style set */
    static void Initialize();

    /** Cleans up the style set */
    static void Shutdown();

    /** @return The Slate style set for DayZ Tools */
    static TSharedPtr<ISlateStyle> Get();

    /** @return The name of the Slate style set */
    static FName GetStyleSetName();

private:
    /** The actual style set used by the plugin */
    static TSharedPtr<FSlateStyleSet> StyleSet;
};
