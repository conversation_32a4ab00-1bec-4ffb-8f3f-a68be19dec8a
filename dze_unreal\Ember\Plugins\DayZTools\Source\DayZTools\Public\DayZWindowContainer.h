#pragma once

#include "CoreMinimal.h"
#include "Widgets/SCompoundWidget.h"
#include "Windows/WindowsHWrapper.h"

class SDayZWindowContainer : public SCompoundWidget
{
public:
    SLATE_BEGIN_ARGS(SDayZWindowContainer)
        {}
    SLATE_END_ARGS()

    void Construct(const FArguments& InArgs);

    void SetContent(HWND InWindowHandle, HWND InParentWindowHandle);
    void ClearContent();

    virtual int32 OnPaint(const FPaintArgs& Args, const FGeometry& AllottedGeometry, const FSlateRect& MyCullingRect, FSlateWindowElementList& OutDrawElements, int32 LayerId, const FWidgetStyle& InWidgetStyle, bool bParentEnabled) const override;

private:
    HWND ContentWindowHandle;
    HWND ParentWindowHandle;
    TWeakPtr<SWindow> ParentSlateWindow;
    void LogWindowHandleStatus(const FString& Context) const;
};