// DynamicEvent.h
#pragma once

#include "CoreMinimal.h"
#include "UObject/NoExportTypes.h"
#include "CEType/CEType.h"
#include "DynamicEvent.generated.h"

UENUM(BlueprintType)
enum class EEventPosition : uint8
{
    Fixed UMETA(DisplayName = "Fixed"),
    Player UMETA(DisplayName = "Player"),
    Uniform UMETA(DisplayName = "Uniform")
};

UENUM(BlueprintType)
enum class EEventLimit : uint8
{
    Child UMETA(DisplayName = "Child"),
    Parent UMETA(DisplayName = "Parent"),
    Mixed UMETA(DisplayName = "Mixed"),
    Custom UMETA(DisplayName = "Custom")
};

// Child event structure
USTRUCT(BlueprintType)
struct FEventChild
{
    GENERATED_USTRUCT_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings", meta = (DisplayName = "Type"))
    TSoftObjectPtr<UCEType> TypeAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings", meta = (ClampMin = "0"))
    int32 Min = 20;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings", meta = (ClampMin = "0"))
    int32 Max = 20;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings", meta = (ClampMin = "0"))
    int32 LootMin = 4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Child Settings", meta = (ClampMin = "0"))
    int32 LootMax = 8;
};

/**
 * Dynamic Event asset for DayZ events configuration
 * Replaces XML-based event configuration with an editable asset
 */
UCLASS(BlueprintType)
class DAYZTOOLS_API UDynamicEvent : public UObject
{
    GENERATED_BODY()

public:
    UDynamicEvent(const FObjectInitializer& ObjectInitializer);

    // Active flag (moved to top)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings")
    bool bActive = false;

    // Basic event properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings", meta = (ClampMin = "0"))
    int32 Nominal = 20;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings", meta = (ClampMin = "0"))
    int32 Min = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings", meta = (ClampMin = "0"))
    int32 Max = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings", meta = (ClampMin = "0"))
    int32 Lifetime = 86400;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings", meta = (ClampMin = "0"))
    int32 Restock = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings", meta = (ClampMin = "0"))
    int32 SafeRadius = 100;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings", meta = (ClampMin = "0"))
    int32 DistanceRadius = 5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings", meta = (ClampMin = "0"))
    int32 CleanupRadius = 2500;

    // Reference to another Dynamic Event
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings")
    TSoftObjectPtr<UDynamicEvent> Secondary;

    // Flags
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings|Flags")
    bool bDeletable = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings|Flags")
    bool bInitRandom = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings|Flags")
    bool bRemoveDamaged = true;

    // Spawn Zone properties
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Zone", meta = (DisplayName = "Enabled"))
    bool bSpawnZoneEnabled = false;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Zone", meta = (DisplayName = "Static Spawn Count Min", ClampMin = "0", EditCondition = "bSpawnZoneEnabled", ToolTip = "Minimum number of baseline/ambient entities intended to be present within the radius."))
    int32 SpawnZoneSMin = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Zone", meta = (DisplayName = "Static Spawn Count Max", ClampMin = "0", EditCondition = "bSpawnZoneEnabled", ToolTip = "Maximum number of baseline/ambient entities allowed within the radius."))
    int32 SpawnZoneSMax = 0;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Zone", meta = (DisplayName = "Dynamic Spawn Count Min", ClampMin = "0", EditCondition = "bSpawnZoneEnabled", ToolTip = "Minimum number of entities spawned in response to a trigger (e.g., player presence) within the radius."))
    int32 SpawnZoneDMin = 1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Zone", meta = (DisplayName = "Dynamic Spawn Count Max", ClampMin = "0", EditCondition = "bSpawnZoneEnabled", ToolTip = "Maximum number of entities spawned in response to a trigger within the radius."))
    int32 SpawnZoneDMax = 2;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Spawn Zone", meta = (DisplayName = "Spawn Radius (m)", ClampMin = "0", EditCondition = "bSpawnZoneEnabled", ToolTip = "Defines the radius (in meters) of the zone around the Event position where these parameters apply."))
    int32 SpawnZoneR = 20;

    // Position and limits
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings")
    EEventPosition Position = EEventPosition::Fixed;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings")
    EEventLimit Limit = EEventLimit::Child;

    // Children array
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Event Settings")
    TArray<FEventChild> Children;

    // Export to XML string
    UFUNCTION(BlueprintCallable, Category = "Event Settings")
    FString ExportToXML() const;
};
