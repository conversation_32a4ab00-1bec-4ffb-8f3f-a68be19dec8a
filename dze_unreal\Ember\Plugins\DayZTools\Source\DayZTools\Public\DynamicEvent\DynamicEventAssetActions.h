// DynamicEventAssetActions.h
#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "DynamicEvent/DynamicEvent.h"

class FDynamicEventAssetActions : public FAssetTypeActions_Base
{
public:
    FDynamicEventAssetActions(EAssetTypeCategories::Type InAssetCategory);

    // IAssetTypeActions Implementation
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual UClass* GetSupportedClass() const override;
    virtual uint32 GetCategories() override;

private:
    EAssetTypeCategories::Type AssetCategory;
};
