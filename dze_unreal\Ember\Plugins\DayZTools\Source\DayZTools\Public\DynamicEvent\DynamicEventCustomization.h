// DynamicEventCustomization.h
#pragma once

#include "CoreMinimal.h"
#include "IDetailCustomization.h"

class FDynamicEventCustomization : public IDetailCustomization
{
public:
    static TSharedRef<IDetailCustomization> MakeInstance();
    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;

private:
    TWeakObjectPtr<class UDynamicEvent> DynamicEventAsset;

    FReply OnExportToXMLClicked();
};
