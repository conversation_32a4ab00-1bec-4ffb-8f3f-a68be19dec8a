// DynamicEventFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "DynamicEventFactory.generated.h"

UCLASS()
class DAYZTOOLS_API UDynamicEventFactory : public UFactory
{
    GENERATED_BODY()

public:
    UDynamicEventFactory();

    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ShouldShowInNewMenu() const override;
};
