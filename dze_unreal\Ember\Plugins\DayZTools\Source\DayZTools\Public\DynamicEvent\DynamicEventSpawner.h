// DynamicEventSpawner.h
#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "DynamicEvent/DynamicEvent.h"
#include "CEType/CEDynamicEventGroup.h"
#include "P3DBlueprint.h"
#include "DynamicEventSpawner.generated.h"

UENUM(BlueprintType)
enum class EDebugRadiusType : uint8
{
    None        UMETA(DisplayName = "None"),
    SafeRadius  UMETA(DisplayName = "Safe Radius"),
    DistanceRadius UMETA(DisplayName = "Distance Radius"),
    CleanupRadius  UMETA(DisplayName = "Cleanup Radius")
};

/**
 * Dynamic Event Spawner Actor
 * Placed in the level to define where dynamic events should spawn
 */
UCLASS(Blueprintable, HideCategories=(Input, Collision, Replication, LOD, Rendering, Mobility, HLOD, WorldPartition, DataLayers, Cooking, Actor, Networking, Physics, AssetUserData, Activation, Tags, ComponentReplication, Variable, Tick, Components, Sprite, Box, ChildActorComponent, Scene, SceneComponent, Sockets, Materials, RayTracing, Lighting, TextureStreaming, VirtualTexture, Navigation, Pooling, Collision, HLOD, Mobile, Cooking, Activation, UserInterface, Replication, Input, Actor, Rendering, Transformation, AssetUserData, Networking, DeveloperSettings, EditorOnly, Hidden), ClassGroup="DayZ")
class DAYZTOOLS_API ADynamicEventSpawner : public AActor
{
    GENERATED_BODY()

public:
    // Sets default values for this actor's properties
    ADynamicEventSpawner();

    // Called when the actor is being constructed in the editor
    virtual void OnConstruction(const FTransform& Transform) override;

    // Called when a property is changed in the editor
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;

private:
    // Hidden property to control component visibility
    UPROPERTY()
    bool bShowComponents = false;

    // Components (hidden in details panel)
    UPROPERTY(VisibleAnywhere, Category = "Components", meta = (EditCondition = "bShowComponents", EditConditionHides))
    class USceneComponent* RootSceneComponent;

    UPROPERTY(VisibleAnywhere, Category = "Components", meta = (EditCondition = "bShowComponents", EditConditionHides))
    class UBillboardComponent* BillboardComponent;

    UPROPERTY(VisibleAnywhere, Category = "Components", meta = (EditCondition = "bShowComponents", EditConditionHides))
    class UBoxComponent* SpawnVolumeComponent;

    UPROPERTY(VisibleAnywhere, Category = "Components", meta = (EditCondition = "bShowComponents", EditConditionHides))
    class UChildActorComponent* PreviewActorComponent;

    // Debug sphere component for radius visualization
    UPROPERTY(VisibleAnywhere, Category = "Components", meta = (EditCondition = "bShowComponents", EditConditionHides))
    class USphereComponent* DebugSphereComponent;

public:

    // Dynamic Event Properties (shown at the top of the details panel)
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Event", meta = (DisplayName = "Event Group Asset", AllowedClasses = "CEDynamicEventGroup", DisplayPriority = "1"))
    TSoftObjectPtr<UCEDynamicEventGroup> EventGroupAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Event", meta = (DisplayName = "Event Asset", AllowedClasses = "DynamicEvent", DisplayPriority = "2"))
    TSoftObjectPtr<UDynamicEvent> EventAsset;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Event", meta = (DisplayName = "Preview Asset", DisplayPriority = "3"))
    TSubclassOf<AActor> PreviewActorClass;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Event", meta = (DisplayName = "Show Preview Asset", DisplayPriority = "4"))
    bool bShowPreviewAsset;

    // Debug visualization options
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Event", meta = (DisplayName = "Debug Radius", DisplayPriority = "5"))
    EDebugRadiusType DebugRadiusType = EDebugRadiusType::None;

    // Whether this event should spawn on terrain
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Dynamic Event", meta = (DisplayName = "Spawn On Terrain", DisplayPriority = "6", ToolTip = "If checked, the event will spawn on terrain. Used when exporting to determine whether to write the up axis position."))
    bool bSpawnOnTerrain = false;

    // Update the debug visualization based on the selected radius type
    void UpdateDebugVisualization();

    // Update the preview actor based on the selected actor class
    void UpdatePreviewActor();

    // Update the actor label based on the selected event asset
    void UpdateActorLabel();

};
