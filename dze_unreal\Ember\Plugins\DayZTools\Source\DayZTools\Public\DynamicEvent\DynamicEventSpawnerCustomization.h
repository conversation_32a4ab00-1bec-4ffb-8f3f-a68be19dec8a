// DynamicEventSpawnerCustomization.h
#pragma once

#include "CoreMinimal.h"
#include "IDetailCustomization.h"

/**
 * Custom details panel for the Dynamic Event Spawner actor
 * Hides all categories except Transform and Dynamic Event
 */
class FDynamicEventSpawnerCustomization : public IDetailCustomization
{
public:
    static TSharedRef<IDetailCustomization> MakeInstance();
    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;
};
