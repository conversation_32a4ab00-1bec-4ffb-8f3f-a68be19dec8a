#pragma once

#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "BiomeData.h"
#include "BiomeDataEditor.h"

class FBiomeDataAssetTypeActions : public FAssetTypeActions_Base
{
public:
    virtual FText GetName() const override { return FText::FromString("Biome Data"); }
    virtual FColor GetTypeColor() const override { return FColor(0, 255, 0); }
    virtual UClass* GetSupportedClass() const override { return UBiomeData::StaticClass(); }
    virtual uint32 GetCategories() override { return EAssetTypeCategories::Misc; }

    virtual void OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor) override
    {
        for (UObject* Object : InObjects)
        {
            if (UBiomeData* BiomeData = Cast<UBiomeData>(Object))
            {
                TSharedRef<FBiomeDataEditor> Editor(new FBiomeDataEditor());
                Editor->InitBiomeDataEditor(EToolkitMode::Standalone, EditWithinLevelEditor, BiomeData);
            }
        }
    }
};