#pragma once

#include <CoreMinimal.h>

struct FPackedColor
{
    uint8 R;
    uint8 G;
    uint8 B;
    uint8 A;

    // Default constructor
    FPackedColor()
        : R(0), G(0), B(0), A(255) {}

    // Parameterized constructor
    FPackedColor(uint8 InR, uint8 InG, uint8 InB, uint8 InA = 255)
        : R(InR), G(InG), B(InB), A(InA) {}

    // Constructor from a single uint32 value
    FPackedColor(uint32 InPackedColor)
    {
        A = (InPackedColor >> 24) & 0xFF;
        R = (InPackedColor >> 16) & 0xFF;
        G = (InPackedColor >> 8) & 0xFF;
        B = InPackedColor & 0xFF;
    }

    // Copy constructor
    FPackedColor(const FPackedColor& Other)
        : R(Other.R), G(Other.G), B(Other.B), A(Other.A) {}

    // Assignment operator
    FPackedColor& operator=(const FPackedColor& Other)
    {
        if (this != &Other)
        {
            R = Other.R;
            G = Other.G;
            B = Other.B;
            A = Other.A;
        }
        return *this;
    }

    // Conversion to FColor
    operator FColor() const
    {
        return FColor(A, R, G, B);
    }

    // Function to convert the color to a packed uint32 value
    uint32 ToPackedARGB() const
    {
        return (A << 24) | (R << 16) | (G << 8) | B;
    }
};
