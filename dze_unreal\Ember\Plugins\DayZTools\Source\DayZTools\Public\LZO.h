#pragma once

#include <cstdint>
#include <vector>

class FLZO
{
public:
    static const uint32_t M2_MAX_OFFSET = 0x0800;

    static uint32_t Decompress(const uint8_t* Input, uint8_t* Output, uint32_t ExpectedSize);
    static uint32_t Decompress(const std::vector<uint8_t>& Input, std::vector<uint8_t>& Dst, uint32_t ExpectedSize);
    static std::vector<uint8_t> ReadLZO(const std::vector<uint8_t>& Input, uint32_t ExpectedSize);

private:
    static void CopyMatch(uint8_t*& Op, const uint8_t*& M_Pos, uint32_t& T);
};