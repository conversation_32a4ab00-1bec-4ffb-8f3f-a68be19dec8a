#pragma once

#include "CoreMinimal.h"
#include "Framework/Commands/UICommandList.h"
#include "Framework/MultiBox/MultiBoxExtender.h"
#include "Framework/MultiBox/MultiBoxBuilder.h"
#include "Engine/Engine.h"
#include "Containers/Array.h"
#include "Templates/SharedPointer.h"
#include "Styling/SlateStyle.h"
#include "Misc/Optional.h"
#include "Input/Reply.h"
#include "Widgets/SWidget.h"

// Forward-declare if needed
class FToolBarBuilder;
class SWidget;

// Optional: If you use it in .h, or you can include in .cpp
#include "HAL/FileManager.h"
#include "DayZToolsSettings.h"

/**
 * Dedicated class to manage the custom toolbar in the Level Editor,
 * along with the various button actions previously in FDayZToolsModule.
 */
class FLevelEditorToolbar
{
public:
	/** Initialize once in your module's StartupModule */
	static void Initialize();

	/** Shutdown once in your module's ShutdownModule */
	static void Shutdown();

	static FVector ConvertUnrealToDayZCoords(const FVector& UnrealCoords);

	// -------------------------------
	// Public methods below if needed
	// -------------------------------

private:
	// -------------------------------
	// Toolbar registration / removal
	// -------------------------------
	static void RegisterToolbarExtension();
	static void UnregisterToolbarExtension();

	static TSharedRef<FExtender> GetToolbarExtender();
	static void AddToolbarExtension(class FToolBarBuilder& ToolbarBuilder);

	// Sub-menus
	static TSharedRef<SWidget> GenerateBuildPBOMenu();
	static TSharedRef<SWidget> GenerateLaunchDayZMenu();
	static TSharedRef<SWidget> GenerateCentralEconomyMenu();

	// New ExportTools menu
	static TSharedRef<SWidget> GenerateExportToolsMenu();
	static void GenerateCentralEconomyExportMenu(FMenuBuilder& MenuBuilder);
	static void GenerateDynamicEventsExportMenu(FMenuBuilder& MenuBuilder);

	// -------------------------------
	// The actual button handlers
	// (Previously in the module)
	// -------------------------------
	static void OnLaunchWorkbench();
	static void OnLaunchObjectBuilder();
	static void OnLaunchTerrainBuilder();
	static void OnBuildPBO();
	static void OnPlayButtonClicked();
	static void OnLaunchDayZOffline();
	static void OnLaunchDayZMultiplayer();
	static bool  LaunchDayZServer();
	static bool  LaunchDayZLocalMP();
	static bool  ExitDayZInstances();
	static void  TestLoadWRPFile();
	static void  OnCentralEconomyClicked();
	static void  OnExportEventSpawns();

	// New button handler
	static void OnExportToolsClicked();

	// Export Central Economy functions
	static void OnExportAllCentralEconomy();

	// Export Dynamic Events functions
	static void OnExportAllDynamicEvents();
	static void OnExportDynamicEventDefinitions();
	static void OnExportDynamicEventGroupDefinitions();

	// New export functions for World section
	static void OnExportTypesDefinitions();
	static void OnExportLootPoints();
	static void OnExportAllWorld();
	static void OnExportTerrainHeightmap();
	static void OnExportTerrainLayers();
	static void OnExportTerrainObjects();
	static void OnExportSurfaceMask();

	// Server Messages export function
	static void OnExportServerMessages();

	// Various internal helpers

	static void LoadDirectoryList();
	static void SaveDirectoryList();
	static void OnRefreshDirectoriesClicked();
	static void OnDirectoryCheckboxChanged(ECheckBoxState NewState, int32 DirectoryIndex);
	static ECheckBoxState GetDirectoryCheckboxState(int32 DirectoryIndex);
	static void BinarizePBO(const FString& Directory, const FString& ModBuildDirectory, const FString& ModName, const FString& WorkDrive, const FString& DayZToolsPath, const FString& KeyDirectory, const FString& KeyName, const FString& TrunkPath);
	static void ExtractDataFromConfig(const FString& ProjectCfgPath, const FString& UserCfgPath, const FString& Key, FString& OutValue);
	static FString GetDayZToolsPath();
	static bool  WriteCameraPositionToFile();
	static FVector GetEditorCameraPosition();
	static FString GetCurrentLevelName();
	static bool  HasParentConfig(const FString& Directory);

private:
	// An enum for the DayZ play mode
	enum class EDayZPlayMode
	{
		Offline,
		Multiplayer
	};

	// Keep track of the current DayZ play mode
	static EDayZPlayMode CurrentPlayMode;

	static void OnSelectPlayMode(EDayZPlayMode SelectedMode);
	bool IsPlayModeChecked(EDayZPlayMode Mode) const;

	// For storing �build PBO� directories
	struct FDirectoryItem
	{
		FString Directory;
		bool bChecked;

		FDirectoryItem(const FString& InDirectory, bool bInChecked)
			: Directory(InDirectory), bChecked(InDirectory.Len() > 0 && bInChecked)
		{}
	};
	static TArray<FDirectoryItem> DirectoryList;

private:
	// Shared pointer to the commands used in the toolbar
	static TSharedPtr<FUICommandList> PluginCommands;

	// The actual extender we register with the Level Editor
	static TSharedPtr<FExtender> LevelEditorExtender;
};
