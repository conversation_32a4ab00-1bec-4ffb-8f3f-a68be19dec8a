#pragma once

#include <cstdint>
#include <vector>
#include <stdexcept>
#include <cstring>

/*
   This file is a C++ conversion of "miniLZO" based on your C# code snippet.
   It provides two main functions:
   - lzo1x_1_compress
   - lzo1x_decompress

   LICENSE NOTE:
   The original LZO library is GPL or commercial with special exceptions.
   If you want to use this in your product, be sure to comply with LZO's licensing terms.
*/

/**
 * lzo1x_decompress:
 *   Decompresses the data from inBuf (length inLen) to outBuf.
 *   outLen is set to the decompressed size.
 *   Returns 0 on success, negative on error.
 */
int lzo1x_decompress(const uint8_t* inBuf, uint32_t inLen,
    uint8_t* outBuf, uint32_t& outLen);

/**
 * lzo1x_1_compress:
 *   Compresses data from inBuf (length inLen) to outBuf.
 *   outLen is set to the compressed size.
 *   wrkmem must be a buffer of at least (1<<14)*2 or so.
 *   Returns 0 on success.
 */
int lzo1x_1_compress(const uint8_t* inBuf, uint32_t inLen,
    uint8_t* outBuf, uint32_t& outLen,
    uint8_t* wrkmem);

/**
 * Helper: Convenience function that allocates wrkmem internally
 *         and returns a std::vector<uint8_t> containing compressed bytes.
 */
inline std::vector<uint8_t> lzo1x_compress_vector(const std::vector<uint8_t>& input)
{
    // The C# code uses a wrkmem of size (IntPtr.Size * 16384). 
    // Typically minilzo needs something like 65536 or so. We'll do 65536 here.
    static const size_t WORKMEM_SIZE = 65536;
    std::vector<uint8_t> wrkmem(WORKMEM_SIZE);

    // Allocate output with some overhead: input.size + input.size/16 + 64 + 3
    size_t maxOutSize = input.size() + (input.size() / 16) + 64 + 3;
    std::vector<uint8_t> outBuf(maxOutSize);

    uint32_t outLen = 0;
    int r = lzo1x_1_compress(input.data(), (uint32_t)input.size(),
        outBuf.data(), outLen,
        wrkmem.data());
    if (r != 0)
        throw std::runtime_error("lzo1x_1_compress failed");

    outBuf.resize(outLen);
    return outBuf;
}

/**
 * Helper: Decompress from std::vector into std::vector.
 */
inline std::vector<uint8_t> lzo1x_decompress_vector(const std::vector<uint8_t>& input, size_t expectedOutSize)
{
    std::vector<uint8_t> outBuf(expectedOutSize);
    uint32_t outLen = (uint32_t)expectedOutSize;
    int r = lzo1x_decompress(input.data(), (uint32_t)input.size(), outBuf.data(), outLen);
    if (r != 0)
        throw std::runtime_error("lzo1x_decompress failed");
    if (outLen != expectedOutSize) {
        // It's possible the decompressed size is smaller, but your code expects exact size. Adjust if needed
        outBuf.resize(outLen);
    }
    return outBuf;
}
