#pragma once

#include <cstdint>
#include <vector>
#include "PAAType.h"
#include "BinaryReader.h"
#include "BinaryWriter.h"

class Mipmap
{
public:
    // Read constructor
    Mipmap(FBinaryReader& input, int offset);

    // Write constructor
    Mipmap(uint16_t width, uint16_t height, const std::vector<uint8_t>& data, bool useLZOCompression = true);

    int GetOffset() const { return m_Offset; }
    int GetDataOffset() const { return m_DataOffset; }
    bool IsLZOCompressed() const { return m_IsLZOCompressed; }
    uint16_t GetWidth() const { return m_Width; }
    uint16_t GetHeight() const { return m_Height; }
    uint32_t GetDataSize() const { return m_DataSize; }
    const std::vector<uint8_t>& GetData() const { return m_Data; }

    // Set the raw pixel data for this mipmap
    void SetRawPixelData(const std::vector<uint8_t>& data, bool useLZOCompression = true);

    // Get the raw pixel data from the input stream
    std::vector<uint8_t> GetRawPixelData(FBinaryReader& input, PAAType type) const;

    // Write the mipmap header and data to the output stream
    void Write(FBinaryWriter& output, PAAType type, int& offset);

private:
    static const uint16_t MAGIC_LZW_W = 1234;
    static const uint16_t MAGIC_LZW_H = 8765;

    // Helper methods for compression
    std::vector<uint8_t> CompressWithLZO(const std::vector<uint8_t>& data);
    std::vector<uint8_t> CompressWithLZSS(const std::vector<uint8_t>& data);
    std::vector<uint8_t> CompressP8(const std::vector<uint8_t>& data);

    int m_Offset;
    int m_DataOffset;
    bool m_IsLZOCompressed;
    bool m_HasMagicLZW;
    uint16_t m_Width;
    uint16_t m_Height;
    uint32_t m_DataSize;
    std::vector<uint8_t> m_Data; // Store the raw data for writing
};