#include "MiniLzo.h" // or whatever header has lzo1x_compress_vector
#include <vector>
#include <cstdint>

class FMipmapEncoder
{
public:
    FMipmapEncoder(const std::vector<uint8_t>& mipmap, int width, int height, int offset)
        : Width(static_cast<uint16_t>(width))
        , WidthEncoded(static_cast<uint16_t>(width))
        , Height(static_cast<uint16_t>(height))
        , Offset(offset)
        , PaaData(mipmap)
    {
        // Mirroring your original C# check
        if (Width >= 256 || Height >= 256)
        {
            // Exactly like: PaaData = MiniLZO.MiniLZO.Compress(PaaData);
            PaaData = lzo1x_compress_vector(PaaData);

            // Set the high bit
            WidthEncoded = static_cast<uint16_t>(Width | 0x8000);
        }
    }

    uint16_t GetWidth() const { return Width; }
    uint16_t GetWidthEncoded() const { return WidthEncoded; }
    uint16_t GetHeight() const { return Height; }
    int GetOffset() const { return Offset; }
    const std::vector<uint8_t>& GetPaaData() const { return PaaData; }

    int GetMipmapEntrySize() const
    {
        return static_cast<int>(PaaData.size()) + 7;
    }

private:
    uint16_t Width;
    uint16_t WidthEncoded;
    uint16_t Height;
    int Offset;
    std::vector<uint8_t> PaaData;
};
