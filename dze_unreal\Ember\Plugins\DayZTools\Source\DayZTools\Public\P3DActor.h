#pragma once

#include "CoreMinimal.h"
#include "GameFramework/Actor.h"
#include "Components/StaticMeshComponent.h" // Include StaticMeshComponent
#include "P3DActor.generated.h"

UCLASS(Blueprintable)
class DAYZTOOLS_API AP3DActor : public AActor
{
    GENERATED_BODY()
public:
    AP3DActor();

    /** The file path to the P3D file */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings")
    FString P3DPath;

    /** Determines whether the mesh should be auto-centered upon import */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings")
    bool bAutoCenter;

    /** Determines whether the mesh should be auto-centered upon import */
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings")
    FVector ObjectCenter;

    // Proxy Settings - These properties will appear as a subcategory within P3D Settings
    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInLOD1;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInLOD2;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInLOD3;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInLOD4;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInLOD5;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInLOD6;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInLOD7;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInLOD8;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInGeometry;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInMemory;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInRoadWay;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInPaths;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInViewGeometry;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "P3D Settings|Proxy Settings")
    bool bIncludeInFireGeometry;

    /** Set the static mesh for this actor */
    UFUNCTION(BlueprintCallable, Category = "P3D Settings")
    void SetStaticMesh(UStaticMesh* NewMesh);


};