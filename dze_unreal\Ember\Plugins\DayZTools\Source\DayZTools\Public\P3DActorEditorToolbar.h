#pragma once
#include "CoreMinimal.h"
#include "Framework/Commands/Commands.h"
#include "Framework/Commands/UICommandList.h"
#include "EditorStyleSet.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Math/Transform.h"
#include "BinaryWriter.h"

class UBlueprint;

struct FP3DPoint
{
    FVector Position;
    uint32 Flags;
};

struct FP3DNormal
{
    FVector Direction;
};

struct FP3DFaceVertex
{
    uint32 PointIndex;
    uint32 NormalIndex;
    float U;
    float V;
};

struct FP3DFace
{
    uint32 VertexCount;
    TArray<FP3DFaceVertex> Vertices;  // Always size 4
    uint32 Flags;
    FString Texture;
    FString Material;
};

struct FP3DTag
{
    FString Type;
    FString Name;
    TArray<uint8> RawData;

    // Extra data for specific tag types
    TArray<float> MassValues;
    TArray<TPair<uint32, uint32>> Edges;    // For SharpEdges

    // For #SharpEdges#
    TArray<TPair<uint32, uint32>> SharpEdges;

    // For #Property#
    FString PropName;
    FString PropValue;

    // For #UVSet#
    uint32 UVSetID;
    TArray<TArray<FVector2D>> FaceUVs; // UVs per face per vertex

    TArray<FVector> FramePoints;

    float FrameTime;
};

struct FP3DLOD
{
    uint32 MajorVersion;
    uint32 MinorVersion;
    uint32 NumPoints;
    uint32 NumNormals;
    uint32 NumFaces;
    uint32 Flags;

    TArray<FP3DPoint> Points;
    TArray<FP3DNormal> Normals;
    TArray<FP3DFace> Faces;
    TArray<FP3DTag> Tags;
    float Resolution;
};

struct FProxyTriangle
{
    TArray<FVector> Points;
    FVector Normal;
    TArray<FVector2D> UVs;
    FString TexturePath;
    FString MaterialPath;
};

struct FP3DProxyInfo
{
    FString ComponentName;
    FString P3DPath;

    bool bIncludeInLOD1;
    bool bIncludeInLOD2;
    bool bIncludeInLOD3;
    bool bIncludeInLOD4;
    bool bIncludeInLOD5;
    bool bIncludeInLOD6;
    bool bIncludeInLOD7;
    bool bIncludeInLOD8;
    bool bIncludeInGeometry;
    bool bIncludeInMemory;
    bool bIncludeInRoadWay;
    bool bIncludeInPaths;
    bool bIncludeInViewGeometry;
    bool bIncludeInFireGeometry;

    FVector Location;
    FRotator Rotation;
    FVector Scale;
};

class FP3DFile
{
public:

    FString ReadCString(FArchive& Ar);
    void WriteCString(FBinaryWriter& Writer, const FString& Str) const;
    bool SaveToFile(const FString& FilePath) const;

    bool LoadFromFile(const FString& FilePath);
    void RemoveProxyGeometry();
    void AddProxyGeometry(const TArray<FP3DProxyInfo>& ProxyInfos, const FVector& RootCenter);
    TArray<FP3DLOD> LODs;  // Move to public

private:
    uint32 Version;
    uint32 NumLODs;

    FString ReadNullTerminatedString(const uint8*& Buffer, const uint8* BufferEnd, int32 MaxLength = 1024);

    FP3DLOD* ReadLOD(const uint8*& Buffer, const uint8* BufferEnd);
    void WriteLOD(FBinaryWriter& Writer, const FP3DLOD& LOD) const;
    FP3DTag* ReadTag(FArchive& Ar, const FP3DLOD& LOD);
    void WriteTag(FBinaryWriter& Writer, const FP3DTag& Tag) const;
};

//
// The main class that sets up toolbar hooking for P3DActor Blueprint classes
//
class FP3DActorEditorToolbar
{
public:
    // Call this in your module's StartupModule()
    static void Initialize();

    static FProxyTriangle CreateProxyTriangle(const FVector& Location, const FRotator& Rotation, const FVector& Scale);
    static void RemoveProxyGeometryFromLOD(FP3DLOD& LOD, const TSet<int32>& ProxyPoints, const TSet<int32>& ProxyFaces);
    static void AddProxyTriangleToLOD(FP3DLOD& LOD, const FProxyTriangle& Triangle, const FString& ComponentName);
    static void RecurseSCSNode(USCS_Node* Node, TArray<FP3DProxyInfo>& OutProxies);

protected:

    // Called by BlueprintEditorModule for each blueprint that opens
    static void OnGatherExtensions(TSharedPtr<FExtender> Extender, UBlueprint* Blueprint);

    // The actual code that adds the toolbar button
    static void ExtendToolBar(class FToolBarBuilder& Builder);

    // The handler that runs when the user clicks our button
    static void OnSaveProxies(UBlueprint* Blueprint);
    
};

//
// Commands container for our toolbar actions
//
class FP3DActorEditorToolbarCommands : public TCommands<FP3DActorEditorToolbarCommands>
{
public:
    FP3DActorEditorToolbarCommands()
        : TCommands<FP3DActorEditorToolbarCommands>(
            TEXT("P3DActorEditorToolbarCommands"), // Context name
            NSLOCTEXT("Contexts", "P3DActorEditorToolbar", "P3DActor Editor"), // Display name
            NAME_None,                           // No parent
            FEditorStyle::GetStyleSetName()      // Icon Style Set
        )
    {
    }

    virtual void RegisterCommands() override;

    // This is the command for our "Save Proxies" toolbar button
    TSharedPtr<FUICommandInfo> SaveProxies;
};
