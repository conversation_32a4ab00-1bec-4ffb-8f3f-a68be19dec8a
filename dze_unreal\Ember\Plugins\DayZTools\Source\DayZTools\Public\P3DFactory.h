#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "P3DFactory.generated.h"

UCLASS()
class UP3DFactory : public UFactory
{
    GENERATED_BODY()

public:
    UP3DFactory();

    // UFactory interface
    virtual UObject* FactoryCreateNew(UClass* Class, UObject* InParent, FName Name, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual bool ConfigureProperties() override;

private:
    UClass* ParentClass;
};
