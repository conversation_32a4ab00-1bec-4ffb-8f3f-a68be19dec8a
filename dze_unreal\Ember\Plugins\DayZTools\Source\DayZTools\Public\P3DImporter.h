#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "EditorReimportHandler.h"
#include "Engine/StaticMesh.h"
#include "StaticMeshAttributes.h"
#include "MeshDescription.h"
#include "RvmatAsset.h"
#include "StaticMeshOperations.h"
#include "P3DImporter.generated.h"

UCLASS()
class DAYZTOOLS_API UP3DImporter : public UFactory, public FReimportHandler
{
    GENERATED_BODY()

public:

    UP3DImporter(const FObjectInitializer& ObjectInitializer);

    // UFactory interface
    virtual UObject* FactoryCreateBinary(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, UObject* Context, const TCHAR* Type, const uint8*& Buffer, const uint8* BufferEnd, FFeedbackContext* Warn) override;
    virtual UObject* FactoryCreateFile(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, const FString& Filename, const TCHAR* Parms, FFeedbackContext* Warn, bool& bOutOperationCanceled) override;
    


    // FReimportHandler interface
    virtual bool CanReimport(UObject* Obj, TArray<FString>& OutFilenames) override;
    virtual void SetReimportPaths(UObject* Obj, const TArray<FString>& NewReimportPaths) override;
    virtual EReimportResult::Type Reimport(UObject* Obj) override;
    virtual int32 GetPriority() const override;

    virtual bool DoesSupportClass(UClass* Class) override;
    virtual UClass* ResolveSupportedClass() override;

    FString CurrentFilename;

    struct FProxyData
    {
        FString Name;
        TArray<uint8> Points;
        TArray<uint8> Faces;
        TArray<int32> VertexIndices;
        FVector Position;  // Added for triangle position
        FRotator Rotation; // Added for triangle rotation
    };

    struct Face
    {
        uint32 NumSides;
        TArray<int32> VertexIndices;
        TArray<int32> NormalIndices;
        TMap<int32, TArray<FVector2D>> UVsPerUVSet; // Key: UVSetIndex, Value: UVs for this face
    };

    struct LOD
    {
        uint32 VersionMajor;
        uint32 VersionMinor;
        TArray<FVector> Vertices;
        TArray<FVector> Normals;
        TArray<Face> Faces;
        TArray<FString> Materials;
        TArray<FString> MaterialNames;
        TArray<int32> FaceMaterialIndices;
        TArray<int32> FaceNormalIndices;
        TArray<TArray<FVector2D>> UVSets;
        float Resolution;
        TArray<FProxyData> ProxyData;
        TMap<FString, FString> Properties;
        TMap<FString, FString> FaceTextures; // Added for face textures
    };

    struct P3DData
    {
        TMap<int32, LOD> LODs;
        TMap<FString, FString> AdditionalProperties;
    };

    P3DData LoadP3DDataOnly(const FString& FilePath, bool& bSuccess);
    static FString NormalizePath(const FString& Path);

private:


    LOD TopLOD;
    LOD GeometryLOD;

    FVector ProxyCenter;
    FVector ObjectCenter;

    UMaterialInterface* CreateOrRetrieveMaterial(const FString& MaterialName);
    TMap<FString, UMaterialInterface*> CreatedMaterials;
    TMap<int32, LOD*> ResolutionLODs;
    UStaticMesh* StaticMesh;

    

    P3DData ReadP3DFile(const uint8*& Buffer, const uint8* BufferEnd);
    LOD ReadLOD(const uint8*& Buffer, const uint8* BufferEnd);
    FString ReadNullTerminatedString(const uint8*& Buffer, const uint8* BufferEnd, int32 MaxLength = 1024);
    bool ReadBoolean(const uint8*& Buffer, const uint8* BufferEnd);
    URvmatAsset* ImportRvmatMaterial(const FString& MaterialPath, UObject* InParent);
    UTexture* ImportTexture(const FString& TexturePath, UObject* InParent);
    
    FBox CalculateCombinedBounds(const P3DData& Data) const;
    float CalculateAngle(const FVector& Point1, const FVector& Point2, const FVector& Center);

    UBlueprint* CreateP3DActorBlueprint(
        const FString& RelativeAssetPath,
        const FString& PackageName,
        UStaticMesh* StaticMesh,
        const FString& ContentRelativeP3DPath,
        const P3DData& P3DData
    );

    UStaticMesh* ImportP3DFromPath(const FString& P3DFilePath, FName InName, EObjectFlags Flags);

    bool AddProxyToBlueprint(UBlueprint* P3DPrefabBluerint, UBlueprint* ProxyPrefabBlueprint, const FVector& Position, const FRotator& Rotation, const FVector& MeshCenter);

    void ImportProxies(const TArray<FProxyData>& ProxyDataArray, P3DData p3ddata, UBlueprint* P3DPrefabBluerint);
};