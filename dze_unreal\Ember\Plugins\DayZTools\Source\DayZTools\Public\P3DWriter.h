#pragma once

#include "CoreMinimal.h"
#include "UObject/Object.h"
#include "BinaryWriter.h"    // for FBinaryWriter
#include "P3DImporter.h"     // assuming it declares P3DData, LOD, Face

#include "P3DWriter.generated.h"

/**
 * A simple class to write P3DData back to a .p3d file
 * using the same format your P3DImporter uses for reading.
 */
UCLASS()
class UP3DWriter : public UObject
{
    GENERATED_BODY()

public:
    /**
     * Writes the given P3DData to disk as a .p3d file,
     * using the provided version and output path.
     *
     * @param Data        The parsed P3DData to write
     * @param InVersion   The version (from reading, e.g. 1.0) to embed
     * @param OutFilePath The file path where you want to save the .p3d
     * @return true if successful, false otherwise
     */
    bool WriteP3DFile(const UP3DImporter::P3DData& Data, uint32 InVersion, const FString& OutFilePath);

private:
    // Optionally, some helper methods for writing fixed-size strings, etc.
    void WriteFixedSizeAscii(FBinaryWriter& Writer, const std::string& Str, int32 MaxSize);
};
