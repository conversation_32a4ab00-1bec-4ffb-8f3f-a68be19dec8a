#pragma once

#include <vector>
#include <array>
#include <string>
#include <memory>
#include "PAAType.h"
#include "Palette.h"
#include "Mipmap.h"
#include "BinaryReader.h"

class PAA
{
public:
    PAA(const std::string& file);
    PAA(const std::vector<uint8_t>& data, bool isPac = false);
    PAA(FBinaryReader& stream, bool isPac = false);

    PAAType GetType() const { return m_Type; }
    const Palette& GetPalette() const { return m_Palette; }
    int GetWidth() const { return m_Mipmaps[0].GetWidth(); }
    int GetHeight() const { return m_Mipmaps[0].GetHeight(); }

    const Mipmap& operator[](size_t i) const { return m_Mipmaps[i]; }
    const std::vector<Mipmap>& GetMipmaps() const { return m_Mipmaps; }

    static std::vector<uint8_t> GetARGB32PixelData(const std::vector<uint8_t>& paaData, bool isPac = false, int mipmapIndex = 0);
    static std::vector<uint8_t> GetARGB32PixelData(const PAA& paa, const std::vector<uint8_t>& paaData, int mipmapIndex = 0);
    static std::vector<uint8_t> GetARGB32PixelData(const PAA& paa, const std::vector<uint8_t>& paaData, const Mipmap& mipmap);


private:
    static PAAType MagicNumberToType(uint16_t magic);
    void Read(FBinaryReader& input, bool isPac = false);

    std::vector<Mipmap> m_Mipmaps;
    std::array<int, 16> m_MipmapOffsets;
    PAAType m_Type = PAAType::UNDEFINED;
    Palette m_Palette;
};