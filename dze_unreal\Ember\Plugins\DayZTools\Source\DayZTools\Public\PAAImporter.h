#pragma once
#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "EditorReimportHandler.h"
#include "Engine/Texture2D.h"
#include "PAAImporter.generated.h"

UCLASS()
class DAYZTOOLS_API UPAAImporter : public UFactory
{
    GENERATED_BODY()
public:
    UPAAImporter(const FObjectInitializer& ObjectInitializer);

    virtual bool DoesSupportClass(UClass* Class) override;
    virtual UClass* ResolveSupportedClass() override;
    virtual UObject* FactoryCreateBinary(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, UObject* Context, const TCHAR* Type, const uint8*& Buffer, const uint8* BufferEnd, FFeedbackContext* Warn) override;

    // Override FactoryCreateFile to capture the filename
    virtual UObject* FactoryCreateFile(
        UClass* InClass,
        UObject* InParent,
        FName InName,
        EObjectFlags Flags,
        const FString& Filename,
        const TCHAR* Parms,
        FFeedbackContext* Warn,
        bool& bOutOperationCanceled
    ) override;


private:
    bool ImportTexture(UTexture2D* Texture, const uint8*& Buffer, const uint8* BufferEnd);
    // Stores the current filename being imported
    FString CurrentFilename;

    // Utility function to normalize paths
    FString NormalizePath(const FString& Path);
};