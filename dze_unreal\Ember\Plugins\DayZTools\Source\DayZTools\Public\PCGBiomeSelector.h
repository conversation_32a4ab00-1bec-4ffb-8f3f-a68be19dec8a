#pragma once

#include "CoreMinimal.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGPoint.h"
#include "BiomeTypes.h"
#include "PCGBiomeSelector.generated.h"

USTRUCT(BlueprintType)
struct FBiomeSelectionRule
{
    GENERATED_BODY()

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    class UBiomeData* BiomeData = nullptr;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float Priority = 0.5f;

    UPROPERTY(EditAnywhere, BlueprintReadWrite, Category = "Biome")
    bool bEnabled = true;
};

UCLASS(BlueprintType, ClassGroup = (Procedural))
class DAYZTOOLS_API UPCGBiomeSelectorSettings : public UPCGSettings
{
    GENERATED_BODY()

public:
    UPCGBiomeSelectorSettings();

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Biomes")
    TArray<FBiomeSelectionRule> MainBiomes;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Biomes")
    TArray<FBiomeSelectionRule> SubBiomes;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Settings")
    bool bUseSubBiomes = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Settings", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float BiomeBlendRange = 0.1f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Debug")
    bool bDebugMode = false;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Debug", meta = (EditCondition = "bDebugMode"))
    bool bShowBiomeColors = true;

    virtual FPCGElementPtr CreateElement() const override;

#if WITH_EDITOR
    virtual FName GetDefaultNodeName() const override { return FName(TEXT("BiomeSelector")); }
    virtual FText GetDefaultNodeTitle() const override { return NSLOCTEXT("PCGBiomeSelector", "NodeTitle", "Biome Selector"); }
    virtual EPCGSettingsType GetType() const override { return EPCGSettingsType::Spatial; }
#endif

protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
};

class FPCGBiomeSelectorElement : public IPCGElement
{

private:
    float CalculateBiomeViability(const FPCGPoint& Point, const UPCGMetadata* Metadata, const UBiomeData* BiomeData) const;
    UBiomeData* SelectBiome(const FPCGPoint& Point, const UPCGMetadata* Metadata, const TArray<FBiomeSelectionRule>& BiomeRules) const;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
};