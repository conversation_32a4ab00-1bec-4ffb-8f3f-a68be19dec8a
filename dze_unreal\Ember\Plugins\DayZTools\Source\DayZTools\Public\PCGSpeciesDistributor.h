#pragma once

#include "CoreMinimal.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGPoint.h"
#include "BiomeTypes.h"
#include "PCGSpeciesDistributor.generated.h"

USTRUCT(BlueprintType)
struct FSpeciesCandidate
{
    GENERATED_BODY()

    FVector Location = FVector::ZeroVector;
    FBiomeSpecies Species;
    float Viability = 0.0f;
    float Age = 1.0f;
    int32 Priority = 0;
    int32 OriginalPointIndex = -1;
    int32 SizeIndex = -1;  // Add this
    int32 VariationIndex = -1;  // Add this
};

UCLASS(BlueprintType, ClassGroup = (Procedural))
class DAYZTOOLS_API UPCGSpeciesDistributorSettings : public UPCGSettings
{
    GENERATED_BODY()

public:
    UPCGSpeciesDistributorSettings();

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Species")
    bool bUseAgeForSizing = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Species", meta = (ClampMin = "0.0", ClampMax = "1.0", EditCondition = "bUseAgeForSizing"))
    float AgeWeight = 0.7f;  // How much age influences size vs viability

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Species", meta = (ClampMin = "100.0", ClampMax = "2000.0", EditCondition = "bUseAgeForSizing"))
    float MaxAgeDistance = 500.0f;  // Maximum distance for age calculation

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Species", meta = (ClampMin = "0.0", ClampMax = "1.0", EditCondition = "bUseAgeForSizing"))
    float ViabilityEdgeThreshold = 0.3f;  // What viability value is considered the "edge"

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Species")
    bool bUsePrioritySystem = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Species")
    bool bUseViabilityRadius = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Species", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float MinimumViability = 0.1f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Performance")
    bool bUseSpatialHashing = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Performance", meta = (ClampMin = "10.0", ClampMax = "1000.0"))
    float SpatialHashCellSize = 100.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Debug")
    bool bDebugViability = false;

    virtual FPCGElementPtr CreateElement() const override;

#if WITH_EDITOR
    virtual FName GetDefaultNodeName() const override { return FName(TEXT("SpeciesDistributor")); }
    virtual FText GetDefaultNodeTitle() const override { return NSLOCTEXT("PCGSpeciesDistributor", "NodeTitle", "Species Distributor"); }
    virtual EPCGSettingsType GetType() const override { return EPCGSettingsType::Spatial; }
#endif

protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
};

class FPCGSpeciesDistributorElement : public IPCGElement
{

private:
    float CalculateAgeFromViability(
        const FVector& Location,
        const TArray<FPCGPoint>& AllPoints,
        const UPCGMetadata* Metadata,
        const FBiomeSpecies& Species,
        const UPCGSpeciesDistributorSettings* Settings) const;
    float CalculateSpeciesViability(const FPCGPoint& Point, const UPCGMetadata* Metadata, const FBiomeSpecies& Species) const;
    void RunSpeciesCompetition(TArray<FSpeciesCandidate>& Candidates, const UPCGSpeciesDistributorSettings* Settings) const;

protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;
};