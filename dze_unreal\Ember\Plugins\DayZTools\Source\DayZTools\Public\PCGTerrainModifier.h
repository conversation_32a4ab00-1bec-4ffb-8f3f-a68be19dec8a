#pragma once

#include "CoreMinimal.h"
#include "PCGSettings.h"
#include "PCGElement.h"
#include "PCGPoint.h"
#include "PCGTerrainModifier.generated.h"

USTRUCT()
struct FLandscapeTerrainModification
{
    GENERATED_BODY()

    FVector Location = FVector::ZeroVector;
    float Height = 0.0f;
    float Radius = 100.0f;
    class ULandscapeLayerInfoObject* LayerInfo = nullptr;
};

UCLASS(BlueprintType, ClassGroup = (Procedural))
class DAYZTOOLS_API UPCGTerrainModifierSettings : public UPCGSettings
{
    GENERATED_BODY()

public:
    UPCGTerrainModifierSettings();

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Terrain")
    bool bApplyHeightChanges = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Terrain")
    bool bApplyLayerWeights = true;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Terrain", meta = (ClampMin = "0.1", ClampMax = "4.0"))
    float HeightBlendFalloff = 2.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Terrain", meta = (ClampMin = "0.0", ClampMax = "1.0"))
    float LayerWeightStrength = 1.0f;

    UPROPERTY(BlueprintReadWrite, EditAnywhere, Category = "Debug")
    bool bDebugDraw = false;

    virtual FPCGElementPtr CreateElement() const override;

#if WITH_EDITOR
    virtual FName GetDefaultNodeName() const override { return FName(TEXT("TerrainModifier")); }
    virtual FText GetDefaultNodeTitle() const override { return NSLOCTEXT("PCGTerrainModifier", "NodeTitle", "Terrain Modifier"); }
    virtual EPCGSettingsType GetType() const override { return EPCGSettingsType::Spatial; }
#endif

protected:
    virtual TArray<FPCGPinProperties> InputPinProperties() const override;
    virtual TArray<FPCGPinProperties> OutputPinProperties() const override;
};

class FPCGTerrainModifierElement : public IPCGElement
{
protected:
    virtual bool ExecuteInternal(FPCGContext* Context) const override;

private:
#if WITH_EDITOR
    void ApplyModificationsToLandscape(
        class ALandscape* Landscape,
        const TArray<FLandscapeTerrainModification>& Modifications,
        const UPCGTerrainModifierSettings* Settings) const;

    void ApplyHeightModification(
        class ALandscape* Landscape,
        class ULandscapeInfo* LandscapeInfo,
        const FVector& Location,
        float Height,
        float Radius,
        float Falloff) const;

    void ApplyLayerWeightModification(
        class ALandscape* Landscape,
        class ULandscapeInfo* LandscapeInfo,
        const FVector& Location,
        float Radius,
        class ULandscapeLayerInfoObject* LayerInfo,
        float Strength,
        float Falloff) const;
#endif
};