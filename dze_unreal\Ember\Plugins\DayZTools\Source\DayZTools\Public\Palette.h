#pragma once

#include <vector>
#include <cstdint>
#include <array>
#include "PAAType.h" // Enum for PAAType
#include "ChannelSwizzling.h" // Struct for ARGBSwizzle and enum for TexSwizzle
#include "ColorHelper.h"
#include "BinaryReader.h"
#include "BinaryWriter.h"

class Palette
{
public:
    static const int PicFlagAlpha = 1;
    static const int PicFlagTransparent = 2;

    Palette() = default;
    Palette(PAAType format);

    // Read the palette from the input stream
    void Read(FBinaryReader& input, std::array<int, 16>& startOffsets);

    // Write the palette to the output stream
    void Write(FBinaryWriter& output, std::array<int, 16>& startOffsets);

    // Calculate average and max colors from ARGB32 data
    void CalculateColors(const std::vector<uint8_t>& argb32Data);

    // Getters
    const std::vector<PackedColor>& GetColors() const { return Colors; }
    std::vector<PackedColor>& GetColors() { return Colors; }
    PackedColor GetAverageColor() const { return AverageColor; }
    PackedColor GetMaxColor() const { return MaxColor; }
    ARGBSwizzle GetChannelSwizzle() const { return ChannelSwizzle; }
    bool GetIsAlpha() const { return IsAlpha; }
    bool GetIsTransparent() const { return IsTransparent; }

    // Setters
    void SetAverageColor(const PackedColor& color) { AverageColor = color; }
    void SetMaxColor(const PackedColor& color) { MaxColor = color; }
    void SetChannelSwizzle(const ARGBSwizzle& swizzle) { ChannelSwizzle = swizzle; }
    void SetIsAlpha(bool isAlpha) { IsAlpha = isAlpha; }
    void SetIsTransparent(bool isTransparent) { IsTransparent = isTransparent; }

private:
    std::vector<PackedColor> Colors;
    PackedColor AverageColor;
    PackedColor MaxColor;
    ARGBSwizzle ChannelSwizzle;
    bool IsAlpha = false;
    bool IsTransparent = false;
};