#pragma once

#include <vector>
#include <cstdint>
#include <memory>
#include "Palette.h"
#include "ColorHelper.h"

class PixelFormatConversion
{
public:
    // Conversion from various PAA formats to ARGB32
    static std::vector<uint8_t> ARGB16ToARGB32(const std::vector<uint8_t>& src);
    static std::vector<uint8_t> ARGB1555ToARGB32(const std::vector<uint8_t>& src);
    static std::vector<uint8_t> AI88ToARGB32(const std::vector<uint8_t>& src);
    static std::vector<uint8_t> P8ToARGB32(const std::vector<uint8_t>& src, const Palette& palette);
    static std::vector<uint8_t> DXTToARGB32(const std::vector<uint8_t>& imageData, int width, int height, int dxtType);

    // Conversion from ARGB32 to various PAA formats
    static std::vector<uint8_t> ARGB32ToARGB16(const std::vector<uint8_t>& src);
    static std::vector<uint8_t> ARGB32ToARGB1555(const std::vector<uint8_t>& src);
    static std::vector<uint8_t> ARGB32ToAI88(const std::vector<uint8_t>& src);
    static std::vector<uint8_t> ARGB32ToP8(const std::vector<uint8_t>& src, const std::vector<PackedColor>& palette);
    static std::vector<uint8_t> ARGB32ToDXT1(const std::vector<uint8_t>& src, int width, int height);
    static std::vector<uint8_t> ARGB32ToDXT3(const std::vector<uint8_t>& src, int width, int height);
    static std::vector<uint8_t> ARGB32ToDXT5(const std::vector<uint8_t>& src, int width, int height);

private:
    // Helper methods
    static void SetColor(std::vector<uint8_t>& img, int offset, uint8_t a, uint8_t r, uint8_t g, uint8_t b);
    static void GetColor(const std::vector<uint8_t>& img, int offset, uint8_t& a, uint8_t& r, uint8_t& g, uint8_t& b);

    // DXT decompression methods
    static void DecompressDxt1Block(const uint8_t*& imageReader, int x, int y, int blockCountX, int width, int height, std::vector<uint8_t>& imageData);
    static void DecompressDxt3Block(const uint8_t*& imageReader, int x, int y, int blockCountX, int width, int height, std::vector<uint8_t>& imageData);
    static void DecompressDxt5Block(const uint8_t*& imageReader, int x, int y, int blockCountX, int width, int height, std::vector<uint8_t>& imageData);

    // Color conversion methods
    static void ConvertRgb565ToRgb888(uint16_t color, uint8_t& r, uint8_t& g, uint8_t& b);
    static uint16_t ConvertRgb888ToRgb565(uint8_t r, uint8_t g, uint8_t b);
};