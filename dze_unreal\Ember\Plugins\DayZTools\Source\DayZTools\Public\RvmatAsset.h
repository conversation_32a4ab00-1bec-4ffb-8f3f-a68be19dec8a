// RvmatAsset.h
#pragma once
#include "CoreMinimal.h"
#include "Materials/MaterialInstanceConstant.h"
#include "RvmatAsset.generated.h"

UCLASS(BlueprintType)
class URvmatAsset : public UMaterialInstanceConstant
{
    GENERATED_BODY()
public:
    URvmatAsset(const FObjectInitializer& ObjectInitializer);
    int32 GetSortPriority(const FMaterialParameterInfo& ParameterInfo) const;

    // Override PostLoad to ensure proper setup
    virtual void PostLoad() override;

    // Override PostEditChangeProperty to handle changes to the parent material
    virtual void PostEditChangeProperty(FPropertyChangedEvent& PropertyChangedEvent) override;
    void SetParentEditorOnly(UMaterialInterface* NewParent);

#if WITH_EDITOR
    DECLARE_MULTICAST_DELEGATE(FOnRvmatAssetChanged);
    FOnRvmatAssetChanged OnRvmatAssetChanged;
#endif

#if WITH_EDITOR
    // Add a function to update parameters
    void UpdateRvmatParameters();
#endif
};