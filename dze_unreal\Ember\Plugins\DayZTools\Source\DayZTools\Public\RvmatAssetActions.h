// RvmatAssetActions.h
#pragma once
#include "CoreMinimal.h"
#include "AssetTypeActions_Base.h"
#include "RvmatAsset.h"

class FRvmatAssetActions : public FAssetTypeActions_Base
{
public:
    FRvmatAssetActions(EAssetTypeCategories::Type category);

    // IAssetTypeActions Implementation
    virtual FText GetName() const override;
    virtual FColor GetTypeColor() const override;
    virtual UClass* GetSupportedClass() const override;
    virtual uint32 GetCategories() override;
    virtual void OpenAssetEditor(const TArray<UObject*>& InObjects, TSharedPtr<IToolkitHost> EditWithinLevelEditor) override;  // Add this line

private:
    EAssetTypeCategories::Type _assetCategory;
};
