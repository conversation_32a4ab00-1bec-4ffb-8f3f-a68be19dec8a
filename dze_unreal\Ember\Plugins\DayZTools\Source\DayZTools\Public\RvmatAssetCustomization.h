// RvmatAssetCustomization.h
#pragma once
#include "CoreMinimal.h"
#include "IDetailCustomization.h"

class FRvmatAssetCustomization : public IDetailCustomization
{
    struct FPixelShaderOption
    {
        FString DisplayName;
        FString MaterialName;
        FPixelShaderOption(FString InDisplayName, FString InMaterialName)
            : DisplayName(InDisplayName), MaterialName(InMaterialName) {}
    };

public:
    FRvmatAssetCustomization();
    static TSharedRef<IDetailCustomization> MakeInstance();
    virtual void CustomizeDetails(IDetailLayoutBuilder& DetailBuilder) override;

private:
    TSharedPtr<FString> LastSelectedShader;
    TWeakObjectPtr<class URvmatAsset> RvmatAsset;
    TArray<TSharedPtr<FPixelShaderOption>> PixelShaderOptions;
    TArray<TSharedPtr<FString>> PixelShaderOptionsList;
    TSharedPtr<class SComboBox<TSharedPtr<FString>>> PixelShaderComboBox;
    IDetailLayoutBuilder* DetailBuilderPtr;

    FString GetParentMaterialName() const;
    void OnPixelShaderChanged(TSharedPtr<FString> NewValue, ESelectInfo::Type SelectType);
    void RefreshDetailsView();
    void UpdateComboBoxSelection();
    FText GetCurrentPixelShaderText() const;
};