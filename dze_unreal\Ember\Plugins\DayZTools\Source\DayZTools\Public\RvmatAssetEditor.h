#pragma once

#include "CoreMinimal.h"
#include "Toolkits/AssetEditorToolkit.h"

class URvmatAsset;
class FRvmatEditorTabFactory;
class SRvmatEditorViewport;

/**
 * Custom Asset Editor for Rvmat Assets
 */
class FRvmatAssetEditor : public FAssetEditorToolkit
{
public:
    void InitRvmatAssetEditor(const EToolkitMode::Type Mode, const TSharedPtr<IToolkitHost>& InitToolkitHost, URvmatAsset* InRvmatAsset);

    // FAssetEditorToolkit interface
    virtual FName GetToolkitFName() const override;
    virtual FText GetBaseToolkitName() const override;
    virtual FString GetWorldCentricTabPrefix() const override;
    virtual FLinearColor GetWorldCentricTabColorScale() const override;

    // Register and unregister tab spawners
    virtual void RegisterTabSpawners(const TSharedRef<FTabManager>& Tab<PERSON>anager) override;
    virtual void UnregisterTabSpawners(const TSharedRef<FTabManager>& TabManager) override;

protected:
    // Create the editor layout
    TSharedRef<FTabManager::FLayout> CreateEditorLayout();

private:
    URvmatAsset* RvmatAsset;

    // Tab identifiers
    static const FName PreviewTabID;
    static const FName DetailsTabID;

    // New Tab ID for RVMAT Output
    static const FName OutputTabID;

    // Function to create the Output tab
    TSharedRef<SDockTab> SpawnOutputTab(const FSpawnTabArgs& Args);

    // Functions to create tabs
    TSharedRef<SDockTab> SpawnPreviewTab(const FSpawnTabArgs& Args);
    TSharedRef<SDockTab> SpawnDetailsTab(const FSpawnTabArgs& Args);
};
