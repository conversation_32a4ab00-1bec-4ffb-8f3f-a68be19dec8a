// RvmatAssetFactory.h
#pragma once

#include "CoreMinimal.h"
#include "Factories/Factory.h"
#include "RvmatUtils.h"
#include "RvmatAssetFactory.generated.h"

class URvmatAsset;  // Forward declaration

UCLASS()
class DAYZTOOLS_API URvmatAssetFactory : public UFactory
{
    GENERATED_BODY()

public:
    URvmatAssetFactory(const FObjectInitializer& ObjectInitializer);

    virtual UObject* FactoryCreateNew(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, UObject* Context, FFeedbackContext* Warn) override;
    virtual UObject* FactoryCreateFile(UClass* InClass, UObject* InParent, FName InName, EObjectFlags Flags, const FString& Filename, const TCHAR* Parms, FFeedbackContext* Warn, bool& bOutOperationCanceled) override;
    virtual bool CanCreateNew() const override;
    virtual bool ShouldShowInNewMenu() const override;

private:
    void PrintParsedData(const TMap<FString, FRvmatUtils::VariantType>& Data, int32 Indent = 0);
    void SetParentMaterial(URvmatAsset* Asset, const TMap<FString, FRvmatUtils::VariantType>& ParsedData);
    void SetSurfaceProperties(URvmatAsset* Asset, const TMap<FString, FRvmatUtils::VariantType>& ParsedData);
    FString GetMaterialNameFromPixelShaderID(const FString& PixelShaderID);
    void HandleStage(URvmatAsset* Asset, const FString& StageName, const TMap<FString, FRvmatUtils::VariantType>& StageData, const FString& TrunkPath);
    UTexture* ImportTexture(const FString& FullTexturePath, const FString& TrunkPath);
};