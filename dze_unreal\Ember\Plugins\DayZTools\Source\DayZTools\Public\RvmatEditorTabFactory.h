// RvmatEditorTabFactory.h
#pragma once

#include "CoreMinimal.h"
#include "WorkflowOrientedApp/WorkflowTabFactory.h"
#include "RvmatEditorViewport.h"  // Include your custom viewport class

class FRvmatEditorTabFactory : public FWorkflowTabFactory
{
public:
    // Constructor that takes in the asset editor toolkit
    FRvmatEditorTabFactory(TSharedPtr<class FAssetEditorToolkit> InEditor);

    // Override function to create the content of the tab
    virtual TSharedRef<SWidget> CreateTabBody(const FWorkflowTabSpawnInfo& Info) const override;

protected:
    // Pointer to the editor instance, stored as a weak pointer to avoid circular references
    TWeakPtr<class FAssetEditorToolkit> EditorPtr;
};