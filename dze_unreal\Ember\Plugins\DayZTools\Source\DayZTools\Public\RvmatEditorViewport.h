// RvmatEditorViewport.h
#pragma once

#include "CoreMinimal.h"
#include "SEditorViewport.h"
#include "RvmatViewportClient.h"
#include "RvmatPreviewScene.h"
#include "RvmatEditorViewportToolBar.h"

class SRvmatEditorViewport : public SEditorViewport, public ICommonEditorViewportToolbarInfoProvider
{
public:
    SLATE_BEGIN_ARGS(SRvmatEditorViewport) {}
        SLATE_ARGUMENT(TSharedPtr<FRvmatPreviewScene>, PreviewScene)  // Add this to accept the preview scene
    SLATE_END_ARGS()

        void Construct(const FArguments& InArgs);

        // Toolbar interface
        virtual TSharedRef<SEditorViewport> GetViewportWidget() override;
        virtual TSharedPtr<FExtender> GetExtenders() const override;
        virtual void OnFloatingButtonClicked() override;

protected:
    virtual TSharedRef<FEditorViewportClient> MakeEditorViewportClient() override;
    virtual TSharedPtr<SWidget> MakeViewportToolbar() override;

private:
    TSharedPtr<FRvmatViewportClient> ViewportClient;
    TSharedPtr<FRvmatPreviewScene> PreviewScene;
};