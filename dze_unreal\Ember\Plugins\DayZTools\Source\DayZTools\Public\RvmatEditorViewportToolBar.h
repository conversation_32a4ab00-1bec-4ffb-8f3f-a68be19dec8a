// RvmatEditorViewportToolBar.h
#pragma once

#include "CoreMinimal.h"
#include "Editor/UnrealEd/Public/SCommonEditorViewportToolbarBase.h"

class SRvmatEditorViewportToolBar : public SCommonEditorViewportToolbarBase
{
public:
    SLATE_BEGIN_ARGS(SRvmatEditorViewportToolBar) {}

    SLATE_END_ARGS()

    void Construct(const FArguments& InArgs, TSharedPtr<class SRvmatEditorViewport> InViewport);

    // Any custom toolbar functions
};