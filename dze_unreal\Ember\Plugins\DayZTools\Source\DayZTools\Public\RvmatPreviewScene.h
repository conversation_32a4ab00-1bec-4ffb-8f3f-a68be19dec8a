#pragma once

#include "CoreMinimal.h"
#include "AdvancedPreviewScene.h"  // Include this instead of PreviewScene

class UStaticMeshComponent;

class FRvmatPreviewScene : public FAdvancedPreviewScene  // Inherit from FAdvancedPreviewScene
{
public:
    FRvmatPreviewScene(ConstructionValues CVS);

    // Set the material to the preview mesh (sphere)
    void SetPreviewMaterial(UMaterialInterface* Material);

    // Get the preview mesh component
    UStaticMeshComponent* GetPreviewMeshComponent() const;

private:
    UStaticMeshComponent* PreviewMeshComponent;
};
