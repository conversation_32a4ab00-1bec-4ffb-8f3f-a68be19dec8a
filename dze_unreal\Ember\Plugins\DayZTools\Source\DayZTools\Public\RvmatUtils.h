#pragma once

#include "CoreMinimal.h"

class DAYZTOOLS_API FRvmatUtils
{
public:
    enum class EVariantType
    {
        Float,
        String,
        FloatArray,
        Map
    };

    class VariantType
    {
    public:
        EVariantType Type;
        union
        {
            float FloatValue;
            FString* StringValue;
            TArray<float>* FloatArrayValue;
            TMap<FString, VariantType>* MapValue;
        };

        VariantType() : Type(EVariantType::Float), FloatValue(0.0f) {}
        VariantType(float Value) : Type(EVariantType::Float), FloatValue(Value) {}
        VariantType(const FString& Value) : Type(EVariantType::String), StringValue(new FString(Value)) {}
        VariantType(const TArray<float>& Value) : Type(EVariantType::FloatArray), FloatArrayValue(new TArray<float>(Value)) {}
        VariantType(const TMap<FString, VariantType>& Value) : Type(EVariantType::Map), MapValue(new TMap<FString, VariantType>(Value)) {}

        ~VariantType()
        {
            Clear();
        }

        VariantType(const VariantType& Other)
        {
            CopyFrom(Other);
        }

        VariantType& operator=(const VariantType& Other)
        {
            if (this != &Other)
            {
                Clear();
                CopyFrom(Other);
            }
            return *this;
        }

        void Clear()
        {
            switch (Type)
            {
            case EVariantType::String:
                delete StringValue;
                break;
            case EVariantType::FloatArray:
                delete FloatArrayValue;
                break;
            case EVariantType::Map:
                delete MapValue;
                break;
            }
        }

        void CopyFrom(const VariantType& Other)
        {
            Type = Other.Type;
            switch (Type)
            {
            case EVariantType::Float:
                FloatValue = Other.FloatValue;
                break;
            case EVariantType::String:
                StringValue = new FString(*Other.StringValue);
                break;
            case EVariantType::FloatArray:
                FloatArrayValue = new TArray<float>(*Other.FloatArrayValue);
                break;
            case EVariantType::Map:
                MapValue = new TMap<FString, VariantType>(*Other.MapValue);
                break;
            }
        }

        bool IsType(EVariantType CheckType) const { return Type == CheckType; }

        template<typename T>
        T Get() const;
    };

    static TMap<FString, VariantType> LoadRVMat(const FString& FilePath);
    static bool SaveRVMat(const FString& FilePath, const TMap<FString, VariantType>& Data);

    // Move ParseRVMat to public section
    static TMap<FString, VariantType> ParseRVMat(const FString& FileContent);

private:
    static TPair<FString, VariantType> ParseLine(const FString& Line);
    static TArray<float> ParseArray(const FString& Value);
    static TMap<FString, VariantType> ParseClass(const TArray<FString>& Lines, int32& Index);
    static FString FormatValue(const VariantType& Value);
    static TArray<FString> WriteClass(const TMap<FString, VariantType>& Data, int32 Indent = 0);
    static FString SerializeRVMat(const TMap<FString, VariantType>& Data);
};

template<> inline float FRvmatUtils::VariantType::Get<float>() const { check(Type == EVariantType::Float); return FloatValue; }
template<> inline FString FRvmatUtils::VariantType::Get<FString>() const { check(Type == EVariantType::String); return *StringValue; }
template<> inline TArray<float> FRvmatUtils::VariantType::Get<TArray<float>>() const { check(Type == EVariantType::FloatArray); return *FloatArrayValue; }
template<> inline TMap<FString, FRvmatUtils::VariantType> FRvmatUtils::VariantType::Get<TMap<FString, FRvmatUtils::VariantType>>() const { check(Type == EVariantType::Map); return *MapValue; }