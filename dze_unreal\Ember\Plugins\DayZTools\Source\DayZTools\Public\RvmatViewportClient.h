#pragma once

#include "CoreMinimal.h"
#include "EditorViewportClient.h"
#include "UnrealClient.h"

class FRvmatViewportClient : public FEditorViewportClient
{
public:
    FRvmatViewportClient(FPreviewScene* InPreviewScene, const TSharedRef<SEditorViewport>& InEditorViewportWidget);

    virtual void Tick(float DeltaSeconds) override;
    virtual bool InputKey(const FInputKeyEventArgs& EventArgs) override;
    virtual bool InputAxis(FViewport* InViewport, FInputDeviceId DeviceId, FKey Key, float Delta, float DeltaTime, int32 NumSamples = 1, bool bGamepad = false) override;
    virtual void Draw(FViewport* InViewport, FCanvas* Canvas) override;
    virtual bool ShouldOrbitCamera() const override;
    virtual void FocusViewportOnBounds(const FBoxSphereBounds& Bounds, bool bInstant = false);

private:
    //TSharedRef<SEditorViewport> EditorViewportWidget;
    bool bIsRotating;
    bool bShouldFocusOnBounds = true; // Start by focusing on bounds, disable after
};
