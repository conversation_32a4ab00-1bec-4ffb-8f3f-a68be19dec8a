#pragma once

#include "CoreMinimal.h"
#include "Engine/Texture2D.h"
#include "TerrainCache.generated.h"

USTRUCT(BlueprintType)
struct FTerrainCacheEntry
{
    GENERATED_BODY()
    
    UPROPERTY()
    UTexture2D* CacheTexture = nullptr;
    
    UPROPERTY()
    FDateTime LastUpdateTime;
    
    UPROPERTY()
    FIntPoint Resolution = FIntPoint(2048, 2048);
    
    UPROPERTY()
    FBox2D WorldBounds = FBox2D(FVector2D(-8192000, -8192000), FVector2D(8192000, 8192000));
};

UCLASS()
class DAYZTOOLS_API UTerrainCacheSubsystem : public UWorldSubsystem
{
    GENERATED_BODY()
    
public:
    virtual void Initialize(FSubsystemCollectionBase& Collection) override;
    virtual void Deinitialize() override;
    
    UFUNCTION(BlueprintCallable, Category = "Terrain Cache")
    UTexture2D* GetOrCreateCache(const FString& CacheName, FIntPoint Resolution, bool bForceRecreate = false);
    
    UFUNCTION(BlueprintCallable, Category = "Terrain Cache")
    void InvalidateCache(const FString& CacheName);
    
    UFUNCTION(BlueprintCallable, Category = "Terrain Cache")
    void InvalidateAllCaches();
    
    bool UpdateCacheTexture(const FString& CacheName, const TArray<float>& Data);
    bool ReadCacheTexture(const FString& CacheName, TArray<float>& OutData);
    
    FVector2D WorldToUV(const FVector& WorldLocation) const;
    FVector UVToWorld(const FVector2D& UV, float Z = 0.0f) const;
    
private:
    UPROPERTY()
    TMap<FString, FTerrainCacheEntry> CacheEntries;
    
    UPROPERTY()
    class ALandscapeProxy* CachedLandscape = nullptr;
    
    void FindLandscapeActor();
};