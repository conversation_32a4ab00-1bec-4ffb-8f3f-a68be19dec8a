#pragma once
#include "CoreMinimal.h"
#include "BinaryReader.h"
#include "BinaryWriter.h"
#include "Math/Matrix.h"
#include "P3DActor.h"
#include "Containers/Map.h"
#include "Containers/Array.h"
#include "Engine/Texture2D.h"
#include <vector>

// Forward Declarations
class FArchive;
class EditableWrpObject;
struct FGeographyInfo;
struct FObjectId;

class APCGWorldActor;
class APCGPartitionActor;

// Struct to hold a layer�s properties
struct FLayerInfo
{
    FString Texture;
    FString Material;
    FColor Color;

    FLayerInfo() : Texture(TEXT("")), Material(TEXT("")), Color(FColor::Black) {}
};

struct FDecomposedTransform
{
    FVector Location;
    FRotator Rotation;
    FVector Scale;

    FDecomposedTransform() : Location(FVector::ZeroVector), Rotation(FRotator::ZeroRotator), Scale(FVector::OneVector) {}

    FString ToString() const;
};


class FMaterialGenerator
{
public:
    FMaterialGenerator();

    // Main entry point to generate material indices and process tiles.
    TArray<uint16> GenerateMaterialIndices();

    // Config and path strings (adjust these paths as needed)
    FString BaseAbsoluteLayerPath;
    FString BaseRelativeLayerPath;
    FString SurfaceMaskPath;
    FString LayersConfigPath;

    // Global data storage
    TArray<FString> GlobalMaterials;            // Index 0 is "None"
    TArray<uint16> GlobalMaterialIndices;
    TMap<FIntVector, int32> GlobalBlockMapping;   // Key: (tile_col, tile_row, bx*1000+by) � our encoding of (tile_col, tile_row, bx, by)


    TArray<FString> GlobalLayerNames;  // This will store the layer names in the order they were defined.
    TMap<FString, FLayerInfo> LayersInfo;

    TArray<uint16> GenerateAccurateMaterialIndices();


    TMap<FColor, int32> ColorToLayer;

    // Mapping from a layer index to an RGBA splat color
    TMap<int32, FColor> LayerToSplat;

    TArray<TPair<int32, int32>> GetDynamicBlockBounds(int32 TileDim, int32 Pad, int32 BlockSize);
    int32 FindDynamicBlockIndex(int32 Coord, const TArray<TPair<int32, int32>>& Bounds);

    void DumpColorToHex(const FColor& Color);
    void RemoveSurfaceTilePNGs(const FString& DirectoryToConvert);
    void ConvertAllPNGsToPAA(const FString& DirectoryToConvert, const FString& Prefix);
    bool SaveTileImage(const FString& SavePath, const TArray<FColor>& ImageData, int32 Width, int32 Height);
    // Helper functions
    bool ParseLayersConfig();
    bool ExtractLayersBlock(const FString& ConfigText, FString& OutBlock);
    void ProcessTileAndRegisterBlocks(const TArray<TArray<uint8>>& TileData,
        int32 TileCol, int32 TileRow,
        const TArray<int32>& TileOrder,
        int32 BlockSize,
        int32 Pad);
    void GetTileLayerOrder(const TArray<TArray<uint8>>& TileData, TArray<int32>& OutTileOrder);
    bool BuildLayerIndexMap(int32 Size);
    void ExtractTileLayerIndices(int32 XStart, int32 YStart,int32 TileSize,TArray<TArray<uint8>>& OutTileData);
    void TileIndicesToSplatRGBA(const TArray<TArray<uint8>>& TileData, TArray<FColor>& OutImageData);
    void CreateSplatTilesIncludingPadding(int32 ImageSize, int32 TileSize, int32 TileCount, int32 FirstTileOffset, int32 TileStep, int32 BlockSize);
    int32 GetGridIndex(int32 GlobalCoord, int32 TileStep, int32 TileCount, int32 FullValidSize);

    private:
    // NEW member to hold the flattened [Y*W + X] layer index
    TArray<uint8> LayerFlat;
    // NEW: remember the mask dimensions
    int32 MaskSize = 0;

};


class EditableWrp
{
public:
    EditableWrp() = default;
    EditableWrp(FBinaryReader& Input);
    void Read(FBinaryReader& Input);
    void Write(FBinaryWriter& Output) const;

    // Public Properties
    int32 LandRangeX;
    int32 LandRangeY;
    int32 TerrainRangeX;
    int32 TerrainRangeY;
    float CellSize;
    TArray<float> Elevation;
    TArray<uint16> MaterialIndex;
    TArray<FString> MatNames;
    TArray<EditableWrpObject> Objects;

    TArray<EditableWrpObject> GetNonDummyObjects() const;

private:
    void ReadContent(FBinaryReader& Input);
};

class EditableWrpObject
{
public:
    EditableWrpObject() = default;
    EditableWrpObject(FBinaryReader& Input);
    void Write(FBinaryWriter& Output) const;

    // Properties
    FMatrix Transform;
    int32 ObjectID;
    FString Model;

    static const EditableWrpObject Dummy;

    // New methods
    FDecomposedTransform DecomposeTransform() const;
    void ComposeTransform(const FDecomposedTransform& InTransform);
};

struct FGeographyInfo
{
    int16 Info;
    uint8 GetMinWaterDepth() const { return Info & 0b11; }
    bool IsFull() const { return (Info >> 2) & 0b1; }
    bool HasForest() const { return (Info >> 3) & 0b1; }
    bool HasRoad() const { return (Info >> 4) & 0b1; }
    uint8 GetMaxWaterDepth() const { return (Info >> 5) & 0b11; }
    uint8 GetHowManyObjects() const { return (Info >> 7) & 0b11; }
    uint8 GetHowManyHardObjects() const { return (Info >> 9) & 0b11; }
    uint8 GetGradient() const { return (Info >> 11) & 0b111; }
    bool HasSomeRoadway() const { return (Info >> 14) & 0b1; }
    bool HasSomeObjects() const { return (Info >> 15) & 0b1; }
    operator int16() const { return Info; }
    FGeographyInfo& operator=(int16 Value) { Info = Value; return *this; }
};

struct FObjectId
{
    int32 Id;
    bool IsObject() const { return (Id >> 31) & 1; }
    int16 GetObjId() const { return Id & 0x7FF; }
    int16 GetObjX() const { return (Id >> 11) & 0x3FF; }
    int16 GetObjZ() const { return (Id >> 21) & 0x3FF; }
    operator int32() const { return Id; }
    FObjectId& operator=(int32 Value) { Id = Value; return *this; }
};

class StaticEntityInfo
{
public:
    StaticEntityInfo(FBinaryReader& Input);
    FString ClassName;
    FString ShapeName;
    FVector Position;
    FObjectId ObjectId;
};

// Utility functions
FDecomposedTransform DecomposeMatrix(const FMatrix& InMatrix);
FMatrix ComposeMatrix(const FDecomposedTransform& InTransform);
void PrintDecomposedMatrix(const FMatrix& InMatrix);
void ReadAndPrintHeightmap(const FString& InputFilePath, int32 Width, int32 Height);
void ExportLandscapeHeightmap(UWorld* World, const FString& Filename);
void ReadAndWriteWrp(const FString& InputFilePath, const FString& OutputFilePath);
TArray<float> ReadHeightmap(const FString& InputFilePath);
void GatherP3DActors(UWorld* World, TArray<EditableWrpObject>& OutObjects);
FVector ExtractEulerAngles(const FMatrix& RotationMatrix);

// Specialized terrain export functions
bool ExportTerrainHeightmapImpl(UWorld* World, const FString& OutputPath);
bool ExportTerrainLayersImpl(UWorld* World, const FString& OutputPath);
bool ExportTerrainObjectsImpl(UWorld* World, const FString& OutputPath);
bool ExportSurfaceMask(UWorld* World, const FString& OutputPath);