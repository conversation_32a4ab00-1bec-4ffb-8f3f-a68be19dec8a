class Inventory_Base;
class SledgeHammer: Inventory_Base
	{
		scope=2;
		displayName="$STR_CfgVehicles_SledgeHammer0";
		descriptionShort="$STR_CfgVehicles_SledgeHammer1";
		model="\dz\gear\tools\Sledge_Hammer.p3d";
		itemInfo[]=
		{
			"SledgeHammer"
		};
		rotationFlags=12;
		weight=5000;
		itemSize[]={2,6};
		itemBehaviour=2;
		openItemSpillRange[]={30,50};
		inventorySlot[]=
		{
			"Shoulder",
			"Melee"
		};
		isMeleeWeapon=1;
		class DamageSystem
		{
			class GlobalHealth
			{
				class Health
				{
					hitpoints=100;
					healthLevels[]=
					{
						
						{
							1,
							
							{
								"DZ\gear\tools\data\Sledge_Hammer.rvmat"
							}
						},
						
						{
							0.69999999,
							
							{
								"DZ\gear\tools\data\Sledge_Hammer.rvmat"
							}
						},
						
						{
							0.5,
							
							{
								"DZ\gear\tools\data\Sledge_Hammer_damage.rvmat"
							}
						},
						
						{
							0.30000001,
							
							{
								"DZ\gear\tools\data\Sledge_Hammer_damage.rvmat"
							}
						},
						
						{
							0,
							
							{
								"DZ\gear\tools\data\Sledge_Hammer_destruct.rvmat"
							}
						}
					};
				};
			};
		};
		class MeleeModes
		{
			class Default
			{
				ammo="MeleeSledgeHammer";
				range=1.8;
			};
			class Heavy
			{
				ammo="MeleeSledgeHammer_Heavy";
				range=1.8;
			};
			class Sprint
			{
				ammo="MeleeSledgeHammer_Heavy";
				range=3.7;
			};
		};
		class AnimEvents
		{
			class SoundWeapon
			{
				class pickup_light
				{
					soundSet="hatchet_pickup_light_SoundSet";
					id=796;
				};
				class pickup
				{
					soundSet="hatchet_pickup_SoundSet";
					id=797;
				};
				class drop
				{
					soundset="woodaxe_drop_SoundSet";
					id=898;
				};
				class SledgeWoodHammer_loop
				{
					soundSet="SledgeWoodHammer_loop_SoundSet";
					id=1117;
				};
				class SledgeWoodHammer_end
				{
					soundSet="SledgeWoodHammer_end_SoundSet";
					id=1118;
				};
				class ShoulderR_Hide
				{
					soundset="ShoulderR_Hide_SoundSet";
					id=1210;
				};
				class ShoulderR_Show
				{
					soundset="ShoulderR_Show_SoundSet";
					id=1211;
				};
			};
		};
	};